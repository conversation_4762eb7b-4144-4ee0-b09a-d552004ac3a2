package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerSocialFund;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerSocialFundListVO;
import com.jri.biz.domain.vo.CustomerSocialFundVO;
import com.jri.biz.domain.request.CustomerSocialFundQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerSocialFundMapper extends BaseMapper<CustomerSocialFund> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerSocialFundListVO> listPage(@Param("query") CustomerSocialFundQuery query, Page<CustomerSocialFundListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerSocialFundVO getDetailById(@Param("id") Long id);

    Boolean deleteById(@Param("id") Long id,@Param("updateBy") String updateBy);

    CustomerSocialFundVO getDetailByCiId(Long ciId);
}
