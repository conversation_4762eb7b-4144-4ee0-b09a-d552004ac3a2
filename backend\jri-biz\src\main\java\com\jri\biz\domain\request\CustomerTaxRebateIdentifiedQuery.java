package com.jri.biz.domain.request;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;


/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "出口退税查询对象")
public class CustomerTaxRebateIdentifiedQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
