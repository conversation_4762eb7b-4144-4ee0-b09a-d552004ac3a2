package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 停止记账原因查询类
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="停止记账原因查询对象")
public class BasicBookkeepingStopQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 停止记账原因
     */
    @ApiModelProperty("停止记账原因")
    private String name;

    /**
     * 使用状态0-停用 1-启用
     */
    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;
}
