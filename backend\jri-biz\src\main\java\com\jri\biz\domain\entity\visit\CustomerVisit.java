package com.jri.biz.domain.entity.visit;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_visit")
@ApiModel(value = "CustomerVisit对象", description = "")
public class CustomerVisit implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("计划标题")
    private String planName;

    @ApiModelProperty("关联客户id")
    private Long customerId;

    @ApiModelProperty("计划拜访日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDate;

    @ApiModelProperty("实际拜访日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date actualPlanDate;

    @ApiModelProperty("拜访人id")
    private Long visitorId;

    @ApiModelProperty("拜访方式")
    private String planVisitMethod;

    @ApiModelProperty("实际拜访方式")
    private String actualVisitMethod;

    @ApiModelProperty("完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date completeTime;

    @ApiModelProperty("状态 0 待完成 1已完成 2已取消")
    private String status;

    @ApiModelProperty("拜访目的")
    private String visitPurpose;

    @ApiModelProperty("拜访反馈")
    private String visitFeedback;

    @ApiModelProperty("取消原因")
    private String cancelReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("位置名称")
    private String locationName;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;



}
