package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.biz.domain.entity.BizNodeHistory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 合同模板视图对象
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="ContractTempVO视图对象")
public class ContractTempVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("模板名称")
    private String tempName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("审批流程id")
    private Long flowId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("html字符串")
    private String htmlStr;

    @ApiModelProperty("字段列表json")
    private String fieldList;

    @ApiModelProperty("状态0-待审批 1-通过 2-驳回")
    private String status;

    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;

    @ApiModelProperty("附件")
    private String urls;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("审批节点列表")
    private List<BizNodeHistory> reviewList;
}