package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 工单记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("order_record")
@ApiModel(value = "OrderRecord对象", description = "工单记录")
public class OrderRecord implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 操作记录名称
     */
    private String recordName;

    /**
     * 指派给
     */
    private Long executor;

    /**
     * 工单id
     */
    private Long orderId;

    /**
     * 备注
     */
    private String remark;
}
