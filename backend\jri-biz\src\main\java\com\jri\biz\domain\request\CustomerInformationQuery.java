package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "基础信息查询对象")
public class CustomerInformationQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户名称")
    private String customerName;
    @ApiModelProperty(value = "客户编号")
    private String customerNo;
    @ApiModelProperty(value = "所属行业")
    private String industry;
    @ApiModelProperty(value = "客户状态")
    private String customerStatus;
    @ApiModelProperty(value = "客户性质")
    private String customerProperty;

    @ApiModelProperty(value = "是否废弃", allowableValues = "1,0")
    private Integer discard;

    @ApiModelProperty("不需要导出到excel的列")
    private List<String> noShowList = new ArrayList<>();

    @ApiModelProperty("选中导出记录id")
    private List<Long> idList;

    @ApiModelProperty("企业认定")
    private String companyIdentification;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("纬度")
    private BigDecimal lat;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("距离")
    private BigDecimal distance;

    private Long userId;

    private String dataScopeSql;

    @ApiModelProperty("排序列")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("查询列值map")
    private Map<String, String> queryColumAndValue;

    @ApiModelProperty("分配主办会计开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate sponsorAccountingAllocationStartDate;

    @ApiModelProperty("分配主办会计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate sponsorAccountingAllocationEndDate;

    @ApiModelProperty("分配客户成功开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate customerSuccessAllocationStartDate;

    @ApiModelProperty("分配客户成功结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate customerSuccessAllocationEndDate;

    @ApiModelProperty("企业成立开始时间")
    private String establishDateStart;

    @ApiModelProperty("企业成立结束时间")
    private String establishDateEnd;

}
