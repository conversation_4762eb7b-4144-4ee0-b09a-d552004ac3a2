package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerContact;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerContactListVO;
import com.jri.biz.domain.vo.CustomerContactVO;
import com.jri.biz.domain.request.CustomerContactQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 客户联系人 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerContactMapper extends BaseMapper<CustomerContact> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerContactListVO> listPage(@Param("query") CustomerContactQuery query, Page<CustomerContactListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerContactVO getDetailById(@Param("id") Long id);

    CustomerContact add(CustomerContact customerContact);

    int removeByCiId(@Param("ciId") Long ciId);

    Boolean deleteById(@Param("id") Long id,@Param("updateBy") String updateBy);

    List<CustomerContactVO> getDetailByCiId(Long ciId);
}
