package com.jri.biz.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 审账整改常量
 *
 * <AUTHOR>
 * @since 2023/11/29 9:49
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PaymentReviewRectificationStatusEnum {

    PENDING("pending", "待整改"),
    RECTIFIED("rectified", "已整改"),
    NOT_RECTIFIED("not_rectified", "不予整改");


    private String code;

    private String value;

    public static String searchValueByCode(String code) {
        for (var eventNodeEnum : PaymentReviewRectificationStatusEnum.values()) {
            if (eventNodeEnum.getCode().equals(code)) {
                return eventNodeEnum.getValue();
            }
        }
        return null;
    }
}
