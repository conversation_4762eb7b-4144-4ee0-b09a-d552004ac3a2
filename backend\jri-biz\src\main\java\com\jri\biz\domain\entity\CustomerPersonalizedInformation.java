package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "customer_personalized_information", autoResultMap = true)
@ApiModel(value = "CustomerPersonalizedInformation对象", description = "")
public class CustomerPersonalizedInformation implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 个性化信息表id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 客户信息主表id
     */
    private Long ciId;

    /**
     * 客户性格
     */
    private String personality;

    /**
     * 客户类型
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> types;

    /**
     * 其他标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;


    /**
     * 客户年龄层次
     */
    private String ageLevel;

    /**
     * 客户性格补充
     */
    private String personalityComplement;

    /**
     * 资料收取要求
     */
    private String collectionRequirement;

    /**
     * 财务处理要求
     */
    private String dealRequirement;

    /**
     * 开票特殊需求
     */
    private String billingDemand;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;


}
