package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;


/**
 * 客资来源 渠道 关联 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@ApiModel(value = "客资来源 渠道 关联表单请求对象")
public class CusSourceChannelForm extends CusSourceBaseForm {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("银行账户")
    private String bankAccount;

}
