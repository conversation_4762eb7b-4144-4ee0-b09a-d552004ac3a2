package com.jri.biz.domain.entity.address;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 地址供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("address_supplier")
@ApiModel(value = "AddressSupplier对象", description = "地址供应商")
public class AddressSupplier implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 供应商名称
     */
    private String supplier;

    /**
     * 地址成本
     */
    private String addressCost;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 收费类型
     */
    private String feeType;

    /**
     * 有效期至
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDate validTo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态0-停用 1-启用
     */
    private String enable;

    /**
     * 是否过期 0-否 1-是
     */
    private String isExpired;


}
