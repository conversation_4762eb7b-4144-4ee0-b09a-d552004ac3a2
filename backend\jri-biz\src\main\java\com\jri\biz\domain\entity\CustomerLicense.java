package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_license")
@ApiModel(value = "CustomerLicense对象", description = "")
public class CustomerLicense implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 许可证件id
     */
    @TableId(value = "license_id",type= IdType.ASSIGN_ID)
    private Long licenseId;

    /**
     * 客户信息主表id
     */
    private Long ciId;

    /**
     * 许可证名称
     */
    private String licenseName;

    /**
     * 证件到期日
     */
    private LocalDate expireDate;

    /**
     * 年检日
     */
    private LocalDate annualInspectionDate;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;


}
