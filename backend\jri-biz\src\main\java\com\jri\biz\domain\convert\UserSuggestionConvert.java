package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.UserSuggestion;
import com.jri.biz.domain.request.suggestion.UserSuggestionAddForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 企业用户建议对象转换
 *
 * <AUTHOR>
 * @since 2024-01-25
 */

@Mapper
public interface UserSuggestionConvert {
    UserSuggestionConvert INSTANCE = Mappers.getMapper(UserSuggestionConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    UserSuggestion convert(UserSuggestionAddForm form);

}