package com.jri.biz.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.entity.CustomerBankCommon;
import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.jri.biz.domain.entity.CustomerTaxRebateIdentified;
import com.jri.biz.domain.vo.*;
import com.jri.biz.mapper.*;
import com.jri.common.core.domain.CommonBizFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

@Service
public class CompletenessService {

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    private CustomerBankMapper customerBankMapper;

    @Resource
    private CustomerBusinessInformationMapper customerBusinessInformationMapper;

    @Resource
    private CustomerSocialFundMapper customerSocialFundMapper;

    @Resource
    private CustomerTaxInformationMapper customerTaxInformationMapper;

    @Resource
    private CustomerTaxRebateIdentifiedMapper customerTaxRebateIdentifiedMapper;

    @Resource
    private CustomerTaxExportDetailMapper customerTaxExportDetailMapper;

    @Resource
    private CustomerPersonalizedInformationMapper customerPersonalizedInformationMapper;

    @Resource
    private CustomerBankCommonMapper customerBankCommonMapper;

    public CustomerInformationVO getDetailById(Long id) {
        CustomerInformationVO customerInformationVO = new CustomerInformationVO();
        customerInformationVO = customerInformationMapper.getDetailById(id);
        //查询附件
        if (null != customerInformationVO) {
            List<CommonBizFile> interviewFileList = commonBizFileService.selectByMainIdAndBizType(customerInformationVO.getCustomerId(), BizType.INTERVIEW);
            List<CommonBizFile> businessFileList = commonBizFileService.selectByMainIdAndBizType(customerInformationVO.getCustomerId(), BizType.BUSINESS_LICENSE);
            customerInformationVO.setInterviewFileList(interviewFileList);
            customerInformationVO.setBusinessFileList(businessFileList);
        }
        return customerInformationVO;
    }

    public CustomerBankVO getBankDetailByCiId(Long ciId) {
        CustomerBankVO customerBankVO = new CustomerBankVO();
        customerBankVO=customerBankMapper.getDetailByCiId(ciId);
        if (null != customerBankVO) {
            //查询附件
            List<CommonBizFile> accountOpenFileList = commonBizFileService.selectByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_ACCOUNT_OPEN);
            List<CommonBizFile> changeInfoFileList = commonBizFileService.selectByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_CHANGE_INFO);
            customerBankVO.setAccountOpenFileList(accountOpenFileList);
            customerBankVO.setChangeInfoFileList(changeInfoFileList);
            // 一般户列表
            LambdaQueryWrapper<CustomerBankCommon> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerBankCommon::getBankId, customerBankVO.getBankId());
            customerBankVO.setCommonList(customerBankCommonMapper.selectList(wrapper));
        }
        return customerBankVO;
    }

    public CustomerBusinessInformationVO getBusinessDetailByCiId(Long ciId) {

        CustomerBusinessInformationVO customerBusinessInformationVO = customerBusinessInformationMapper.getDetailByCiId(ciId);
        CommonBizFile individualTaxPasswordFile = commonBizFileService.selectOneByMainIdAndBizType(ciId, BizType.INDIVIDUAL_TAX_PASSWORD);
        List<CommonBizFile> businessChangeInfoFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_CHANGE_INFO);
        List<CommonBizFile> businessLicenseFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_LICENSE);
        List<CommonBizFile> registrationInformationFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.REGISTRATION_INFORMATION);
        List<CommonBizFile> businessConstitutionFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_CONSTITUTION);
        List<CommonBizFile> shareholderCommitteeRessolutionFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.SHAREHOLDER_COMMITTEE_RESSOLUTION);
        List<CommonBizFile> adressFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.ADRESS);
        List<CommonBizFile> businessOtherFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_OTHER);
        List<CommonBizFile> identityDocumentFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.IDENTITY_DOCUMENT);
        List<CommonBizFile> handoverDocumentFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_HANDOVER_DOCUMENT);

        if (null != customerBusinessInformationVO) {
            customerBusinessInformationVO.setIndividualTaxPasswordFile(individualTaxPasswordFile);
            customerBusinessInformationVO.setBusinessFileList(businessLicenseFileList);
            customerBusinessInformationVO.setRegistrationInformationFileList(registrationInformationFileList);
            customerBusinessInformationVO.setBusinessConstitutionFileList(businessConstitutionFileList);
            customerBusinessInformationVO.setShareholderCommitteeRessolutionFileList(shareholderCommitteeRessolutionFileList);
            customerBusinessInformationVO.setAdressFileList(adressFileList);
            customerBusinessInformationVO.setBusinessChangeInfoFileList(businessChangeInfoFileList);
            customerBusinessInformationVO.setBusinessOtherFileList(businessOtherFileList);
            customerBusinessInformationVO.setIdentityDocumentFileList(identityDocumentFileList);
            customerBusinessInformationVO.setHandoverDocumentFileList(handoverDocumentFileList);

            //查询客户名称
            CustomerInformationVO customerInformationVO = customerInformationMapper.getDetailById(ciId);
            customerBusinessInformationVO.setCustomerName(customerInformationVO.getCustomerName());
        }

        return customerBusinessInformationVO;
    }

    public CustomerSocialFundVO getSocialDetailByCiId(Long ciId) {
        CustomerSocialFundVO customerSocialFundVO=customerSocialFundMapper.getDetailByCiId(ciId);
        if (null != customerSocialFundVO) {
            //查询附件
            List<CommonBizFile> fundAccountOpenFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.FUND_ACCOUNT_OPEN);
            List<CommonBizFile> socialAccountOpenFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.SOCIAL_ACCOUNT_OPEN);
            customerSocialFundVO.setFundAccountOpenFileList(fundAccountOpenFileList);
            customerSocialFundVO.setSocialAccountOpenFileList(socialAccountOpenFileList);
        }
        return customerSocialFundVO;
    }

    public CustomerTaxInformationVO getTaxDetailByCiId(Long ciId) {
        //查询详细
        CustomerTaxInformationVO customerTaxInformationVO = customerTaxInformationMapper.getDetailByCiId(ciId);
        if (null != customerTaxInformationVO) {
            List<CommonBizFile> customerBillingInformationFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.CUSTOMER_BILLING_INFORMATION);
            List<CommonBizFile> taxInformationFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAX_INFORMATION);
            List<CommonBizFile> taxpayerIdentificationFormFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAXPAYER_IDENTIFICATION_FORM);
            List<CommonBizFile> customerFirstConfirmList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.CUSTOMER_FIRST_CONFIRM);
            customerTaxInformationVO.setCustomerBillingInformationFileList(customerBillingInformationFileList);
            customerTaxInformationVO.setTaxInformationFileList(taxInformationFileList);
            customerTaxInformationVO.setTaxpayerIdentificationFormFileList(taxpayerIdentificationFormFileList);
            customerTaxInformationVO.setCustomerFirstConfirmList(customerFirstConfirmList);

            CustomerTaxRebateIdentified customerTaxRebateIdentified = customerTaxRebateIdentifiedMapper.selectByMainId(customerTaxInformationVO.getId());
            List<CustomerTaxExportDetail> CustomerTaxExportDetail =customerTaxExportDetailMapper.selectByMainId(customerTaxInformationVO.getId());
            customerTaxInformationVO.setCustomerTaxRebateIdentified(customerTaxRebateIdentified);
            customerTaxInformationVO.setCustomerTaxExportDetail(CustomerTaxExportDetail);
        }
        return customerTaxInformationVO;
    }

    public Optional<CustomerPersonalizedInformationVO> getByCiId(Long ciId) {
        var list = customerPersonalizedInformationMapper.getByCiId(ciId);
        if (list != null && list.size() > 0) {
            CustomerPersonalizedInformationVO customerPersonalizedInformationVO = list.get(0);
            List<CommonBizFile> shareholderFamilyRelationshipFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.SHAREHOLDER_FAMILY_RELATIONSHIP);
            List<CommonBizFile> personalizedInformationAttachmentFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.PERSONALIZED_INFORMATION_ATTACHMENT);
            customerPersonalizedInformationVO.setShareholderFamilyRelationshipFileList(shareholderFamilyRelationshipFileList);
            customerPersonalizedInformationVO.setPersonalizedInformationAttachmentFileList(personalizedInformationAttachmentFileList);
            return Optional.ofNullable(list.get(0)) ;
        }else {
            return Optional.empty();
        }
    }

    private BigDecimal getCompleteness(Long customerId) {
        int num = 0;
        int total = 89;
        String customerProperty = "";
        // 删除 客户开票资料 字段
        // 基础信息 17
        CustomerInformationVO customerInformationVO = getDetailById(customerId);
        if (null != customerInformationVO) {
            customerProperty = customerInformationVO.getCustomerProperty();
            if (ObjectUtil.isNotEmpty(customerInformationVO.getCustomerName())) {
                num = num + 2;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getContactPerson())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getContactPhone())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getManger())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getBranchOffice())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getAddress())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getInformationMark())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getCustomerStatus())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getCustomerProperty())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getNofeeReason())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getInterviewFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getNofeeReasonMark())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getIndustry())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getCustomerSuccess())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getCounselor())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerInformationVO.getSponsorAccounting())) {
                num = num + 1;
            }
        }

        // 删除字段<网银>、<结算卡>、<对账周期>
        // 银行信息 15
        CustomerBankVO customerBankVO = getBankDetailByCiId(customerId);
        if (null != customerBankVO) {
            if (ObjectUtil.isNotEmpty(customerBankVO.getBankBaseName())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBankVO.getBankBaseAccount())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerBankVO.getInternetbankFlag())) {
//                num = num + 1;
//            }
//            if (ObjectUtil.isNotEmpty(customerBankVO.getDebitCardFlag())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerBankVO.getReceiptCardFlag())) {
                if ("1".equals(customerBankVO.getReceiptCardFlag())) {
                    total = total + 1;
                    if (ObjectUtil.isNotEmpty(customerBankVO.getReceiptCardType())) {
                        if ("账号密码".equals(customerBankVO.getReceiptCardType())) {
                            total = total + 2;
                            if (ObjectUtil.isNotEmpty(customerBankVO.getReceiptCardAccount())) {
                                num = num + 1;
                            }
                            if (ObjectUtil.isNotEmpty(customerBankVO.getReceiptCardPassword())) {
                                num = num + 1;
                            }
                        }
                        num = num + 1;
                    }
                }
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerBankVO.getCommonBankAccount())) {
//                num = num + 1;
//            }
//            if (ObjectUtil.isNotEmpty(customerBankVO.getCommonBankName())) {
//                num = num + 1;
//            }
//            if (ObjectUtil.isNotEmpty(customerBankVO.getCommonInternetbankFlag())) {
//                num = num + 1;
//            }
//            if (ObjectUtil.isNotEmpty(customerBankVO.getCommonReceiptCardFlag())) {
//                if ("1".equals(customerBankVO.getCommonReceiptCardFlag())) {
//                    total = total + 1;
//                    if (ObjectUtil.isNotEmpty(customerBankVO.getCommonInternetbankType())) {
//                        if ("账号密码".equals(customerBankVO.getCommonInternetbankType())) {
//                            total = total + 2;
//                            if (ObjectUtil.isNotEmpty(customerBankVO.getCommonReceiptCardPassword())) {
//                                num = num + 1;
//                            }
//                            if (ObjectUtil.isNotEmpty(customerBankVO.getCommonInternetbankAccount())) {
//                                num = num + 1;
//                            }
//                        }
//                        num = num + 1;
//                    }
//                }
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerBankVO.getCommonList())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerBankVO.getCycle())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerBankVO.getAccountOpenFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBankVO.getChangeInfoFileList())) {
                num = num + 1;
            }
        }

        // 删除字段<国税账号>、<国税密码>、<个税账号>、<个税密码>、<个体户核定>；
        // 增加字段<交接单>
        // 工商信息 26
        CustomerBusinessInformationVO customerBusinessInformationVO = getBusinessDetailByCiId(customerId);
        if (null != customerBusinessInformationVO) {
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getHandoverDocumentFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getType())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getCrediCode())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getLegalPerson())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getRegisteredAddress())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getContract())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getScope())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getWebsite())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getBussinessStatus())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getRegistrationAuthority())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getEstablishDate())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getRegisteredCapital())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getIndustry())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getRegistrationNumber())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getOpenDate())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getOpenEnd())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getOrganizationCode())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getApprovalDate())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getNationalTaxAccount())) {
//                num = num + 1;
//            }
//            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getNationalTaxPassward())) {
//                num = num + 1;
//            }
//            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getIndividualTaxAccount())) {
//                num = num + 1;
//            }
//            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getIndividualTaxPassword())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getBusinessFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getRegistrationInformationFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getBusinessConstitutionFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getShareholderCommitteeRessolutionFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getAdressFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getIdentityDocumentFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getBusinessChangeInfoFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getBusinessOtherFileList())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerBusinessInformationVO.getIsIndividual())) {
//                num = num + 1;
//            }
        }

        // 社保信息 8
        CustomerSocialFundVO customerSocialFundVO = getSocialDetailByCiId(customerId);
        if (null != customerSocialFundVO) {
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getSocialAccountFlag())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getSocialAccount())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getSocialPassword())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getFundAccountFlag())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getFundAccount())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getFundPassword())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getSocialAccountOpenFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerSocialFundVO.getFundAccountOpenFileList())) {
                num = num + 1;
            }
        }

        // 需求：发票额度不需要了，税企银三方账户不需要了；
        // 需求：删除<法人身份证号>、<法人实名登记>、<网上税务局注册>；
        // 需求：增加 客户首次确认 字段
        // 税务信息 22
        CustomerTaxInformationVO customerTaxInformationVO = getTaxDetailByCiId(customerId);
        if (null != customerTaxInformationVO) {
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getNaturalPersonPassword())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getIdentityNumber())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getTaxRegistrationOrgan())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getTaxOrganAddress())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getRateRegistration())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getLegalRealNameFlag())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getTaxRealNameFlag())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getIndividualCheckFlag())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getOnlineRevenueRegistrationFlag())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getReservedPhoneNumber())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getTripleAgreementFlag())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getIdentificationMethodFlag())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getCertificateAccount())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getCertificatePassword())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getDrawingSheetFlag())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getInvoiceFlag())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getInvoiceSealFlag())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getDrawingSheetDept())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getInvoiceDept())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getInvoiceSealDept())) {
                num = num + 1;
            }
//            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getInvoiceLimit())) {
//                num = num + 1;
//            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getInvoiceType())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getDrawingSheetType())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getTaxInformationFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getCustomerBillingInformationFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getTaxpayerIdentificationFormFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerTaxInformationVO.getCustomerFirstConfirmList())) {
                num = num + 1;
            }
            CustomerTaxRebateIdentified customerTaxRebateIdentified = customerTaxInformationVO.getCustomerTaxRebateIdentified();
            if (null != customerTaxRebateIdentified && "进出口".equals(customerProperty)) {
                total=total+4;
                if (ObjectUtil.isNotEmpty(customerTaxRebateIdentified.getApprovalTime())) {
                    num = num + 1;
                }
                if (ObjectUtil.isNotEmpty(customerTaxRebateIdentified.getWatchTime())) {
                    num = num + 1;
                }
                if (ObjectUtil.isNotEmpty(customerTaxRebateIdentified.getWatchInstructions())) {
                    num = num + 1;
                }
                if (ObjectUtil.isNotEmpty(customerTaxRebateIdentified.getDept())) {
                    num = num + 1;
                }
            }
        }

        // 个性化信息 10
        CustomerPersonalizedInformationVO customerPersonalizedInformationVO = getByCiId(customerId).orElse(null);
        if (null != customerPersonalizedInformationVO) {
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getPersonality())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getTypes())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getTags())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getAgeLevel())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getPersonalityComplement())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getCollectionRequirement())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getDealRequirement())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getBillingDemand())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getShareholderFamilyRelationshipFileList())) {
                num = num + 1;
            }
            if (ObjectUtil.isNotEmpty(customerPersonalizedInformationVO.getPersonalizedInformationAttachmentFileList())) {
                num = num + 1;
            }
        }
        // 一共109
        return BigDecimal.valueOf(num).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
    }

    @Transactional(rollbackFor = Exception.class)
    public void UpdateCompleteness(Long customerId) {
        BigDecimal completeness = getCompleteness(customerId);
        CustomerInformation customerInformation = customerInformationMapper.selectById(customerId);
        customerInformation.setCompleteness(completeness);
        customerInformationMapper.updateById(customerInformation);
    }
}
