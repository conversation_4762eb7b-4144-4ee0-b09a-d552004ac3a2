package com.jri.biz.cus.service;

import com.jri.biz.cus.domain.entity.CusCcRecord;
import com.jri.biz.cus.mapper.CusCcRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.vo.CusCcRecordListVO;
import com.jri.biz.cus.domain.vo.CusCcRecordVO;
import com.jri.biz.cus.domain.request.CusCcRecordForm;
import com.jri.biz.cus.domain.request.CusCcRecordQuery;
import com.jri.biz.cus.domain.convert.CusCcRecordConvert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <p>
 * 操作记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class CusCcRecordService extends ServiceImpl<CusCcRecordMapper, CusCcRecord> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCcRecordListVO> listPage(CusCcRecordQuery query) {
        var page = new Page<CusCcRecordListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CusCcRecordVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusCcRecordForm form) {
        // todo 完善新增/更新逻辑
        CusCcRecord cusCcRecord = CusCcRecordConvert.INSTANCE.convert(form);
        return save(cusCcRecord);
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CusCcRecordForm form) {
        // todo 完善新增/更新逻辑
        CusCcRecord cusCcRecord = CusCcRecordConvert.INSTANCE.convert(form);
        return updateById(cusCcRecord);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    /**
     * 新增操作记录
     *
     * @param recordName 操作记录名称
     * @param remark 备注
     * @param ccId 线索/客户id
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(String recordName, String remark, Long ccId) {
        CusCcRecord cusCcRecord = new CusCcRecord();
        cusCcRecord.setRecordName(recordName);
        cusCcRecord.setRemark(remark);
        cusCcRecord.setCcId(ccId);
        save(cusCcRecord);
    }

    /**
     * 查询操作记录列表
     *
     * @param ccId 客户线索id
     */
    public List<CusCcRecordListVO> listByCcId(Long ccId) {
        return getBaseMapper().listByCcId(ccId);
    }

    /**
     * 查询操作记录列表(商机)
     *
     * @param ccId 客户线索id
     */
    public List<CusCcRecordListVO> businessListByCcId(Long ccId) {
        return getBaseMapper().businessListByCcId(ccId);
    }

    /**
     * 新增操作记录(商机)
     *
     * @param recordName 操作记录名称
     * @param remark 备注
     * @param ccId 线索/客户id
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBusiness(String recordName, String remark, Long ccId) {
        CusCcRecord cusCcRecord = new CusCcRecord();
        cusCcRecord.setRecordName(recordName);
        cusCcRecord.setRemark(remark);
        cusCcRecord.setCcId(ccId);
        cusCcRecord.setBizType("1");
        save(cusCcRecord);
    }
}
