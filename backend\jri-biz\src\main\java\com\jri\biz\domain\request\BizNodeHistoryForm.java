package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 流程历史 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-04
 */

@Data
@NoArgsConstructor
@ApiModel(value="流程历史表单请求对象")
public class BizNodeHistoryForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

}
