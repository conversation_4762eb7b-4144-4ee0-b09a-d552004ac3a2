package com.jri.web.controller.biz;


import com.jri.biz.domain.request.ReviewAuditForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.service.BizNodeHistoryService;
import com.jri.biz.domain.vo.BizNodeHistoryListVO;
import com.jri.biz.domain.vo.BizNodeHistoryVO;
import com.jri.biz.domain.request.BizNodeHistoryForm;
import com.jri.biz.domain.request.BizNodeHistoryQuery;


import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 流程历史 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Validated
@RestController
@RequestMapping("/bizNodeHistory")
@Api(tags = "流程历史")
public class BizNodeHistoryController {
    @Resource
    private BizNodeHistoryService bizNodeHistoryService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<BizNodeHistoryListVO>> listPage(BizNodeHistoryQuery query) {
        return R.ok(bizNodeHistoryService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BizNodeHistoryVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(bizNodeHistoryService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid BizNodeHistoryForm form) {
        return R.ok(bizNodeHistoryService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid BizNodeHistoryForm form) {
        return R.ok(bizNodeHistoryService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(bizNodeHistoryService.deleteById(id));
    }

    @PostMapping("/reviewAudit")
    @ApiOperation("合同评审审核")
    public R<Void> reviewAudit(@RequestBody @Valid ReviewAuditForm form) {
        bizNodeHistoryService.reviewAudit(form);
        return R.ok();
    }

    @PostMapping("/changeReviewAudit")
    @ApiOperation("合同变更审核")
    public R<Void> changeReviewAudit(@RequestBody @Valid ReviewAuditForm form) {
        bizNodeHistoryService.changeReviewAudit(form);
        return R.ok();
    }

    @PostMapping("/borrowReviewAudit")
    @ApiOperation("借阅申请审核")
    public R<Void> borrowReviewAudit(@RequestBody @Valid ReviewAuditForm form) {
        bizNodeHistoryService.borrowReviewAudit(form);
        return R.ok();
    }

    @PostMapping("/tempReviewAudit")
    @ApiOperation("模板审核")
    public R<Void> tempReviewAudit(@RequestBody @Valid ReviewAuditForm form) {
        bizNodeHistoryService.tempReviewAudit(form);
        return R.ok();
    }
}

