package com.jri.biz.domain.vo.job;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 视图列表对象
 *
 * <AUTHOR>
 * @since 2023-08-08
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="ContractAlertListVO视图列表对象")
public class ContractAlertListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同id主键")
    private Long contractId;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户编号")
    private String customerNo;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("服务产品")
    private String productName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("合同总金额")
    private BigDecimal totalCost;

    @ApiModelProperty("起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("客户经理")
    private String manger;

    @ApiModelProperty("客户经理id")
    private Long mangerUserId;

    @ApiModelProperty("提醒时间")
    private String alertTime;

}
