package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 客资来源 直投 关联视图列表对象
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusSourceDirectListVO视图列表对象")
public class CusSourceDirectListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}