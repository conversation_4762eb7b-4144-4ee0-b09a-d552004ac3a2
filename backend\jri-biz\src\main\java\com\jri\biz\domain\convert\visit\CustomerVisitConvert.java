package com.jri.biz.domain.convert.visit;

import com.jri.biz.domain.entity.visit.CustomerVisit;
import com.jri.biz.domain.request.visit.CustomerVisitForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-07-28
 */

@Mapper
public interface CustomerVisitConvert {
    CustomerVisitConvert INSTANCE = Mappers.getMapper(CustomerVisitConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerVisit convert(CustomerVisitForm form);

}
