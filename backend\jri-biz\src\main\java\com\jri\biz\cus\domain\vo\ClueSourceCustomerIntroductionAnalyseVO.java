package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/3 10:22
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "客户介绍来源统计VO")
public class ClueSourceCustomerIntroductionAnalyseVO extends BaseClueSourceAnalyseVO {

    @ApiModelProperty(value = "介绍总数")
    private Long introductionCount = 0L;

}
