package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


/**
 * 工单 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Data
@NoArgsConstructor
@ApiModel(value="工单表单请求对象")
public class OrderForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("工单编号")
    private String orderNo;

    @ApiModelProperty("客户id")
    private Long ciId;

    @ApiModelProperty("客户id列表")
    private List<Long> ciIdList;

    @ApiModelProperty("紧急状态0-一般 1-紧急")
    private String isUrgent;

    @ApiModelProperty("期望完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("工单类型id")
    private Long orderTypeId;

    @ApiModelProperty("工单标题")
    private String orderTitle;

    @ApiModelProperty("地区")
    private String address;

    @ApiModelProperty("工单内容")
    private String content;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("指派给")
    private Long executor;

    @ApiModelProperty("工单状态0-待完成 1-完成 2-回退")
    private String orderStatus;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("行政区划id")
    private Long administrativeId;
}
