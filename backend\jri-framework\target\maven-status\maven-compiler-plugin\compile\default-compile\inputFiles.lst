C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\manager\ShutdownManager.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\domain\server\Sys.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\SysRegisterService.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\domain\server\Mem.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\domain\server\Jvm.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\properties\PermitAllUrlProperties.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\ApplicationConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\RedisConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\datasource\DynamicDataSourceContextHolder.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\RestTemplateConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\KaptchaTextCreator.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\exception\GlobalExceptionHandler.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\aspectj\DataSourceAspect.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\UserDetailsBySmsCodeServiceImpl.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\WeChatAuthenticationToken.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\filter\JwtAuthenticationTokenFilter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\MyBatisConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\TomcatConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\SmsCodeAuthenticationToken.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\datasource\DynamicDataSource.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\UserDetailsServiceImpl.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\aspectj\RateLimiterAspect.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\FilterConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\interceptor\impl\SameUrlDataInterceptor.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\manager\factory\AsyncFactory.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\domain\server\Cpu.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\properties\DruidProperties.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\domain\server\SysFile.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\ThreadPoolConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\aspectj\LogAspect.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\context\PermissionContextHolder.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\handle\LogoutSuccessHandlerImpl.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\ServerConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\handle\AuthenticationEntryPointImpl.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\provider\SmsCodeAuthenticationProvider.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\PermissionService.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\SysLoginService.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\interceptor\RepeatSubmitInterceptor.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\TokenService.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\SecurityConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\manager\AsyncManager.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\ResourcesConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\SysPermissionService.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\domain\Server.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\UserDetailsByWeChatCodeServiceImpl.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\JacksonConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\aspectj\DataScopeAspect.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\RestTemplateInterceptor.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\CaptchaConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\handler\MybatisPlusMetaObjectHandler.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\context\AuthenticationContextHolder.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\web\service\SysPasswordService.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\DruidConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\config\FastJson2JsonRedisSerializer.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-framework\src\main\java\com\jri\framework\security\provider\WeChatAuthenticationProvider.java
