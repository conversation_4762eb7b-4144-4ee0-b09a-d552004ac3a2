package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 标签 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-09
 */

@Data
@NoArgsConstructor
@ApiModel(value="标签表单请求对象")
public class CusTagForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("标签名称")
    private String name;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态0-停用1-正常")
    private String status;
}
