package com.jri.biz.domain.vo.fund;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "TurnoverOfMonthDetailVo视图对象")
public class CollectionAnalysisStatisticVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "维护企业数量")
    private Long maintainCustomer = 0L;

    @ApiModelProperty(value = "当月应收")
    private BigDecimal allPaymentAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "当月实收")
    private BigDecimal allReceiptAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "当月欠费")
    private BigDecimal monthDebt;

}
