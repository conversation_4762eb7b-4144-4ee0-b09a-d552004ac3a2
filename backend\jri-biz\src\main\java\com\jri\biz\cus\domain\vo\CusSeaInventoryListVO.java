package com.jri.biz.cus.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 保有量设置视图列表对象
 *
 * <AUTHOR>
 * @since 2023-08-22
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusSeaInventoryListVO视图列表对象")
public class CusSeaInventoryListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}