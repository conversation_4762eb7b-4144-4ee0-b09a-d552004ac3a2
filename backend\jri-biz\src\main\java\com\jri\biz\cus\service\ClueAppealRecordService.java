package com.jri.biz.cus.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.constants.AppealStatusConstant;
import com.jri.biz.cus.constants.SourceConstant;
import com.jri.biz.cus.domain.convert.ClueAppealRecordConvert;
import com.jri.biz.cus.domain.entity.ClueAppealRecord;
import com.jri.biz.cus.domain.entity.CusSource;
import com.jri.biz.cus.domain.request.ClueAppealEndForm;
import com.jri.biz.cus.domain.request.ClueAppealRecordForm;
import com.jri.biz.cus.domain.request.ClueAppealRecordQuery;
import com.jri.biz.cus.domain.vo.ClueAppealRecordListVO;
import com.jri.biz.cus.domain.vo.ClueAppealRecordVO;
import com.jri.biz.cus.mapper.ClueAppealRecordMapper;
import com.jri.common.annotation.DataScope;
import com.jri.common.exception.CheckedException;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 线索申诉记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Service
public class ClueAppealRecordService extends ServiceImpl<ClueAppealRecordMapper, ClueAppealRecord> {

    @Resource
    private CusCustomerOrClueService cusCustomerOrClueService;

    @Resource
    private CusSourceService cusSourceService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    @DataScope(deptAlias = "dept", userAlias = "user")
    public IPage<ClueAppealRecordListVO> listPage(ClueAppealRecordQuery query) {
        var page = new Page<ClueAppealRecordListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public ClueAppealRecordVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(ClueAppealRecordForm form) {
        Long clueId = form.getClueId();
        String remark = form.getRemark();
        var clue = cusCustomerOrClueService.getById(clueId);
        // 判断来源 必须平台
        CusSource source = cusSourceService.getById(clue.getSource());
        if (ObjectUtil.isNotNull(source) && !StrUtil.equals(source.getBiz(), SourceConstant.PLATFORM)) {
            throw new CheckedException("来源错误无法申述");
        }
        // 判断跟进状态
        if (!StrUtil.equalsAny(clue.getFollowStatus(), "0", "1")) {
            throw new CheckedException("跟进状态错误无法申述");
        }
        // 判断是否申述过
        Long count = getBaseMapper().getCountByClueId(clueId);
        if (count > 0) {
            throw new CheckedException("线索已申述");
        }
        ClueAppealRecord clueAppealRecord = ClueAppealRecordConvert.INSTANCE.convert(form);
        clueAppealRecord.setStatus(AppealStatusConstant.APPEALING);
        clueAppealRecord.setCreateUserId(SecurityUtils.getUserId());
        // 线索状态调整
        cusCustomerOrClueService.clueAppealStartHandle(clueId, remark);
        return save(clueAppealRecord);
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(ClueAppealEndForm form) {
        Long clueId = form.getClueId();
        Boolean success = form.getSuccess();

        ClueAppealRecord clueAppealRecord = getBaseMapper().getByClueId(clueId);
        if (success) {
            clueAppealRecord.setStatus(AppealStatusConstant.APPEAL_SUCCESS);
        } else {
            clueAppealRecord.setStatus(AppealStatusConstant.APPEAL_FAIL);
        }
        // 线索状态调整
        cusCustomerOrClueService.clueAppealEndHandle(clueId, success);
        return updateById(clueAppealRecord);
    }

}
