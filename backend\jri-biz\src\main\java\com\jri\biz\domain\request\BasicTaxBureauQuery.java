package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 税务局信息查询类
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="税务局信息查询对象")
public class BasicTaxBureauQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 使用状态0-停用 1-启用
     */
    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;

    /**
     * 税务局地址
     */
    @ApiModelProperty("税务局地址")
    private String address;
}
