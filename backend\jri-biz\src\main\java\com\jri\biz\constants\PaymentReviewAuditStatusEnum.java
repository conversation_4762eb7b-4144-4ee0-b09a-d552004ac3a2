package com.jri.biz.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 审账整改常量
 *
 * <AUTHOR>
 * @since 2023/11/29 9:49
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PaymentReviewAuditStatusEnum {

    PENDING("pending", "待审批"),
    RECTIFIED("pass", "通过"),
    NOT_RECTIFIED("not_pass", "不通过");


    private String code;

    private String value;

    public static String searchValueByCode(String code) {
        for (var eventNodeEnum : PaymentReviewAuditStatusEnum.values()) {
            if (eventNodeEnum.getCode().equals(code)) {
                return eventNodeEnum.getValue();
            }
        }
        return null;
    }
}
