package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.YearMonth;

/**
 * 客户流动分析查询
 *
 * <AUTHOR>
 * @since 2024/4/22 14:06
 *
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "客户流动分析查询")
public class CustomerUserFlowAnalyseQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "分析月份")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth month;

    @ApiModelProperty(value = "角色 客户成功-customer_success 开票员-counselor 财税顾问-manager 主办会计-sponsor_accounting")
    private String role;

    @ApiModelProperty(value = "用户状态 0-正常 1-停用")
    private String userStatus;

}
