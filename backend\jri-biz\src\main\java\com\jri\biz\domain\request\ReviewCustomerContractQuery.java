package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@ApiModel("进度查询")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReviewCustomerContractQuery extends BaseQuery {

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("业务类型0-新增合同1-变更合同")
    private String bizType;

    @ApiModelProperty("审批结果0-待审批 1-通过 2-驳回")
    private String reviewStatus;

    private Long userId;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("提交人")
    private String createBy;

    private String dataScopeSql;

    @ApiModelProperty("排序列")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("合同总金额最大")
    private BigDecimal totalCostMax;

    @ApiModelProperty("合同总金额最小")
    private BigDecimal totalCostMin;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

}
