package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicCompany;
import com.jri.biz.domain.request.BasicCompanyForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 分公司信息对象转换
 *
 * <AUTHOR>
 * @since 2023-08-02
 */

@Mapper
public interface BasicCompanyConvert {
    BasicCompanyConvert INSTANCE = Mappers.getMapper(BasicCompanyConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BasicCompany convert(BasicCompanyForm form);

}