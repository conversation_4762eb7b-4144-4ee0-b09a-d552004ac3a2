package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 分公司信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("basic_company")
@ApiModel(value = "BasicCompany对象", description = "分公司信息")
public class BasicCompany implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 分公司名称
     */
    @Excel(name = "* 分公司名称")
    private String name;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 使用状态0-停用 1-启用
     */
    private String enable;

    /**
     * 分公司地址
     */
    @Excel(name = "分公司地址")
    private String address;

    /**
     * 分公司联系人
     */
    @Excel(name = "联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String phone;

    /**
     * 账号信息
     */
    private String account;


}
