package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_business_information")
@ApiModel(value = "CustomerBusinessInformation对象", description = "")
public class CustomerBusinessInformation implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工商信息id
     */
    @TableId(value = "business_information_id",type= IdType.ASSIGN_ID)
    private Long businessInformationId;

    /**
     * 客户信息主表
     */
    private Long ciId;

    /**
     * 公司类型
     */
    private String type;

    /**
     * 统一社会信用代码
     */
    private String crediCode;

    /**
     * 法定代表人
     */
    private String legalPerson;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 联系方式
     */
    private String contract;

    /**
     * 经营范围
     */
    private String scope;

    /**
     * 网址
     */
    private String website;

    /**
     * 经营状态
     */
    private String bussinessStatus;

    /**
     * 登记机关
     */
    private String registrationAuthority;

    /**
     * 成立日期
     */
    @TableField(fill = FieldFill.UPDATE)
    private String establishDate;

    /**
     * 注册资金
     */
    private String registeredCapital;

    /**
     * 行业
     */
    private String industry;

    /**
     * 注册号
     */
    private String registrationNumber;

    /**
     * 营业开始时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private String openDate;

    /**
     * 营业结束时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private String openEnd;

    /**
     * 组织机关代码
     */
    private String organizationCode;

    /**
     * 核准日期
     */
    @TableField(fill = FieldFill.UPDATE)
    private String approvalDate;

    /**
     * 国税账号
     */
    private String nationalTaxAccount;

    /**
     * 国税密码
     */
    private String nationalTaxPassward;

    /**
     * 个税账号
     */
    private String individualTaxAccount;

    /**
     * 个税密码
     */
    private String individualTaxPassword;

    /**
     * 营业执照图片地址
     */
    private String businessLicense;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 个体户核定0-否1-是
     */
    private Boolean isIndividual;
}
