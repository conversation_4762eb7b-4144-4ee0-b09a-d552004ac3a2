package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "合同查询对象")
public class CustomerContractQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @ApiModelProperty(value = "客户Id")
    private String customerId;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("合同状态3-已终止 4-执行中 5-已到期 6-意向合同")
    private String contractStatus;

    @ApiModelProperty("客户信息主表id")
    private Long ciId;

    @ApiModelProperty("关联人员")
    private String personnel;

    @ApiModelProperty("产品id")
    private Long productId;

    private Long userId;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("排序列")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("合同总金额最大")
    private BigDecimal totalCostMax;

    @ApiModelProperty("合同总金额最小")
    private BigDecimal totalCostMin;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("开始时间开始")
    private LocalDate startTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("开始时间结束")
    private LocalDate startTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结束时间开始")
    private LocalDate endTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结束时间结束")
    private LocalDate endTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

    @ApiModelProperty(value = "财税顾问用户id")
    private Long mangerUserId;

    @ApiModelProperty(value = "开票员用户id")
    private Long counselorUserId;

    @ApiModelProperty(value = "客户成功用户id")
    private Long customerSuccessUserId;

    @ApiModelProperty(value = "主办会计用户id")
    private Long sponsorAccountingUserId;

}
