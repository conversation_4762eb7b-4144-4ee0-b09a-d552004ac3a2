package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.request.*;
import com.jri.biz.domain.vo.OrderListVO;
import com.jri.biz.domain.vo.OrderVO;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.OrderService;
import com.jri.biz.service.ProgressService;
import com.jri.common.core.domain.R;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <p>
 * 工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Validated
@RestController
@RequestMapping("/order")
@Api(tags = "工单")
public class OrderController {
    @Resource
    private OrderService orderService;

    @Resource
    private ExportService exportService;

    @Resource
    private ProgressService progressService;

    @GetMapping("/listMyCreate")
    @ApiOperation("列表查询(我发起的)")
    public R<IPage<OrderListVO>> listPage(OrderQuery query) {
        return R.ok(orderService.listPage(query));
    }

    @GetMapping("/listMyToDo")
    @ApiOperation("列表查询(我的待办)")
    public R<IPage<OrderListVO>> listMyToDo(OrderQuery query) {
        return R.ok(orderService.listMyToDo(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<OrderVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(orderService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<String> save(@RequestBody @Valid OrderForm form) {
        return R.ok(orderService.add(form));
    }

    @PostMapping("/saveBatch")
    @ApiOperation("批量保存数据")
    public R<Void> saveBatch(@RequestBody @Valid OrderForm form) {
        orderService.addBatch(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(orderService.deleteById(id));
    }

    @PostMapping("/orderAssign")
    @ApiOperation("工单指派")
    public R<Void> orderAssign(@RequestBody @Valid OrderAssignForm form) {
        orderService.orderAssign(form);
        return R.ok();
    }

    @PostMapping("/orderComplete")
    @ApiOperation("工单完成")
    public R<Void> orderComplete(@RequestBody @Valid OrderCompleteForm form) {
        orderService.orderComplete(form);
        return R.ok();
    }

    @PostMapping("/orderBack")
    @ApiOperation("工单回退")
    public R<Void> orderBack(@RequestBody @Valid OrderBackForm form) {
        orderService.orderBack(form);
        return R.ok();
    }

    @GetMapping("/listMyAssign")
    @ApiOperation("列表查询(由我指派)")
    public R<IPage<OrderListVO>> listMyAssign(OrderQuery query) {
        return R.ok(orderService.listMyAssign(query));
    }

    @PostMapping("/orderAbnormal")
    @ApiOperation("标记异常")
    public R<Void> orderAbnormal(@RequestBody @Valid OrderAbnormalForm form) {
        orderService.orderAbnormal(form);
        return R.ok();
    }

    @PostMapping("/orderContinue")
    @ApiOperation("继续办理")
    public R<Void> orderContinue(@ApiParam("工单id") @RequestParam("orderId") Long orderId) {
        orderService.orderContinue(orderId);
        return R.ok();
    }

    @GetMapping("/listAll")
    @ApiOperation("列表查询(全部)")
    public R<IPage<OrderListVO>> listAll(CheckOrderQuery query) {
        return R.ok(orderService.listAll(query));
    }

    @GetMapping("/listAbnormal")
    @ApiOperation("列表查询(异常工单)")
    public R<IPage<OrderListVO>> listAbnormal(CheckOrderQuery query) {
        return R.ok(orderService.listAbnormal(query));
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public R<Void> export(HttpServletResponse response, @Validated @RequestBody CheckOrderQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "工单记录.xlsx");
        var id = progressService.create(ProgressType.ORDER_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        exportService.orderExport(fileName, query, id);
        return R.ok();
    }

}

