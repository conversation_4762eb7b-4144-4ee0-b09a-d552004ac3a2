package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 流程历史查询类
 *
 * <AUTHOR>
 * @since 2023-07-04
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="流程历史查询对象")
public class BizNodeHistoryQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
