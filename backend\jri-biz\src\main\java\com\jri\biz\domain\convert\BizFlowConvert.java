package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BizFlow;
import com.jri.biz.domain.request.BizFlowForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 流程对象转换
 *
 * <AUTHOR>
 * @since 2023-06-16
 */

@Mapper
public interface BizFlowConvert {
    BizFlowConvert INSTANCE = Mappers.getMapper(BizFlowConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BizFlow convert(BizFlowForm form);

}