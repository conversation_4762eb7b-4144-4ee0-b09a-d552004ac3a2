package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 产品活动价视图对象
 *
 * <AUTHOR>
 * @since 2023-11-02
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BusinessProductActivityVO视图对象")
public class BusinessProductActivityVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}