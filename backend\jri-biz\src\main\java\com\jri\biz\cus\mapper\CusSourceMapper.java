package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.entity.CusSource;
import com.jri.biz.cus.domain.request.CusSourceQuery;
import com.jri.biz.cus.domain.vo.CusSourceListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客资来源 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface CusSourceMapper extends BaseMapper<CusSource> {

    /**
     * 列表查询
     *
     * @param query 查询条件
     * @return 列表
     */
    List<CusSourceListVO> getList(@Param("query") CusSourceQuery query);

    /**
     * 查询关联客户数量
     *
     * @param customerFlag 客户标识
     * @param id 来源id
     * @return 数量
     */
    Long countByClue(@Param("customerFlag") Boolean customerFlag, @Param("id") Long id);


    /**
     * 根据客户id查询关联数量
     *
     * @param customerId 客户id
     * @return 数量
     */
    Long countByCustomerId(@Param("customerId") Long customerId, @Param("sourceId") Long sourceId);

    /**
     * 更新状态启用
     *
     * @param ids id数组
     */
    void updateStatusEnable(Long[] ids);

    /**
     * 查询启用的子级
     *
     * @param id id
     * @return 子级
     */
    Long selectEnableChildrenById(Long id);
}
