package com.jri.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.CustomerLicense;
import com.jri.biz.domain.request.CustomerLicenseQuery;
import com.jri.biz.domain.vo.CustomerLicenseListVO;
import com.jri.biz.domain.vo.CustomerLicenseVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerLicenseMapper extends BaseMapper<CustomerLicense> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerLicenseListVO> listPage(@Param("query") CustomerLicenseQuery query, Page<CustomerLicenseListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerLicenseVO getDetailById(@Param("id") Long id);

    Boolean deleteById(@Param("id") Long id,@Param("updateBy") String updateBy);

    List<CustomerLicenseVO> getDetailByCiId(Long ciId);

    /**
     * 根据ciId删除
     *
     * @param ciId ciId
     */
    void removeByCiId(@Param("ciId") Long ciId);


    Long countByLicenseName(@Param("licenseName") String licenseName);
}
