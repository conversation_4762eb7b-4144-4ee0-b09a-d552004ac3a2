package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicTaxBureau;
import com.jri.biz.domain.request.BasicTaxBureauForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 税务局信息对象转换
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Mapper
public interface BasicTaxBureauConvert {
    BasicTaxBureauConvert INSTANCE = Mappers.getMapper(BasicTaxBureauConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BasicTaxBureau convert(BasicTaxBureauForm form);

}