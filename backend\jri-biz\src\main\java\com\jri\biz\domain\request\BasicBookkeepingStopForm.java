package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 停止记账原因 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Data
@NoArgsConstructor
@ApiModel(value="停止记账原因表单请求对象")
public class BasicBookkeepingStopForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 父id
     */
    @ApiModelProperty("父id")
    private Long parentId;

    /**
     * 祖级列表
     */
    @ApiModelProperty("祖级列表")
    private String ancestors;

    /**
     * 停止记账原因
     */
    @ApiModelProperty("停止记账原因")
    private String name;

    /**
     * 显示顺序
     */
    @ApiModelProperty("显示顺序")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 使用状态0-停用 1-启用
     */
    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;
}
