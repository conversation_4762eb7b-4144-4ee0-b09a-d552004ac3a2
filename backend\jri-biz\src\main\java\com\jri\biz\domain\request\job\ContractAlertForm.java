package com.jri.biz.domain.request.job;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 *  合同预警表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-08
 */

@Data
@NoArgsConstructor
@ApiModel(value="合同预警表单请求对象")
public class ContractAlertForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("到期预警id")
    private Long id;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;

    @ApiModelProperty("到期预警时间")
    private String alertTime;

}
