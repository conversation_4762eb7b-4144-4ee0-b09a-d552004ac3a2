<!--
 * @Description:  许可证组价
 * @Author: thb
 * @Date: 2023-05-29 11:19:16
 * @LastEditTime: 2023-11-13 15:50:33
 * @LastEditors: thb
-->
<template>
  <template v-if="formData.discard !== 1">
    <el-button type="primary" @click="handleAdd" v-if="!disabled || isEdit" class="m-g">新增</el-button>
    <!-- <el-button type="primary" @click="handleAdd" v-if="isEdit">新增</el-button> -->
    <el-button type="primary" v-if="disabled && !isEdit" @click="handleEdit" class="m-g">编辑</el-button>
  </template>

  <template v-if="isEdit">
    <el-button type="primary" @click="handleSave" class="m-g">保存</el-button>
    <el-button @click="handleCancel" class="m-g">取消</el-button>
  </template>

  <FormTable :formData="formData.licenseTable" :option="option">
    <template #index="{ $index }">
      <span>{{ $index + 1 }}</span>
    </template>
    <template #licenseName="{ row }">
      <span v-if="disabled && !isEdit"> {{ row.licenseName }}</span>
      <!-- <el-input v-else maxlength="20" v-model="row.licenseName" placeholder="请输入" /> -->
      <el-select v-else v-model="row.licenseName" placeholder="请选择" clearable>
        <el-option v-for="(option, index) in license_name" :key="index" :label="option.label" :value="option.value" />
      </el-select>
    </template>
    <template #expireDate="{ row }">
      <span v-if="disabled && !isEdit"> {{ row.expireDate }}</span>
      <el-date-picker
        v-else
        class="date-picker"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        v-model="row.expireDate"
        type="date"
        placeholder="请选择"
      />
    </template>
    <template #annualInspectionDate="{ row }">
      <span v-if="disabled && !isEdit"> {{ row.annualInspectionDate }}</span>
      <el-date-picker
        v-else
        class="date-picker"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        v-model="row.annualInspectionDate"
        type="date"
        placeholder="请选择"
      />
    </template>
    <template #fileList="{ row }">
      <template v-if="disabled && !isEdit">
        <FileList :list="row.fileList" />
      </template>
      <FileUpload v-else v-model="row.fileList" :isShowTip="false" :limit="10" />
    </template>
    <template #action="{ $index }">
      <el-button type="danger" @click="handleDelete($index)">删除</el-button>
    </template>
  </FormTable>
</template>
<script setup>
import FormTable from '@/components/FormTable'
import { useRemote } from '@/hooks/useRemote'
import { saveCustomerLicense, getCustomerLicenseByCiId } from '@/api/customer/file'
import { cloneDeep } from 'lodash'
import { getCurrentInstance } from 'vue'
import FileList from '@/components/FileList'

const { proxy } = getCurrentInstance()
const { license_name } = proxy.useDict('license_name')

const disabled = inject('disabled')
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const emits = defineEmits(['update:modelValue', 'on-change', 'on-edit'])
const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})

// watch(
//   formData,
//   () => {
//     emits('on-change')
//   },
//   {
//     deep: true
//   }
// )
// const formTable = ref({
//   tableData: []
//   // rules: {
//   //   licenseName: [
//   //     {
//   //       message: '请输入',
//   //       trigger: 'blur'
//   //     }
//   //   ]
//   // }
// })

const option = ref([
  {
    prop: 'index',
    label: '序号',
    width: '50'
  },
  {
    prop: 'licenseName',
    label: '许可证名称'
  },
  {
    prop: 'expireDate',
    label: '证件到期日'
  },
  {
    prop: 'annualInspectionDate',
    label: '证件年检日'
  },
  {
    prop: 'fileList',
    label: '许可证附件',
    width: '320'
  },
  {
    prop: 'action',
    label: '操作'
  }
])

// 获取详情的时候将数据存储一份作为源数据
const originData = ref([])
const defaultColumns = [
  {
    prop: 'index',
    label: '序号',
    width: '50'
  },
  {
    prop: 'licenseName',
    label: '许可证名称'
  },
  {
    prop: 'expireDate',
    label: '证件到期日'
  },
  {
    prop: 'annualInspectionDate',
    label: '证件年检日'
  },
  {
    prop: 'fileList',
    label: '许可证附件',
    width: '320'
  }
]

const getDetail = async () => {
  const { data } = await getCustomerLicenseByCiId(props.modelValue.ciId)
  originData.value = cloneDeep(data || [])
  formData.value = Object.assign(formData.value, {
    licenseTable: {
      tableData: data || []
    }
  })
}
watch(
  disabled,
  () => {
    if (disabled.value) {
      option.value = defaultColumns
      // 通过ciId获取许可证件详情
      getDetail()
    }
  },
  {
    immediate: true
  }
)

// 监听是否是编辑
const edit = inject('isEdit')
watch(
  edit,
  async () => {
    if (edit.value) {
      await getDetail()
      emits('on-edit')
    }
  },
  {
    immediate: true
  }
)

// 编辑
const handleEdit = () => {
  isEdit.value = true
  option.value = [
    {
      prop: 'index',
      width: '50',
      label: '序号'
    },
    {
      prop: 'licenseName',
      label: '许可证名称'
    },
    {
      prop: 'expireDate',
      label: '证件到期日'
    },
    {
      prop: 'annualInspectionDate',
      label: '证件年检日'
    },
    {
      prop: 'fileList',
      label: '许可证附件',
      width: '320'
    },
    {
      prop: 'action',
      label: '操作'
    }
  ]
}

// 保存接口
const handleSave = async () => {
  option.value = [
    {
      prop: 'index',
      label: '序号',
      width: '50'
    },
    {
      prop: 'licenseName',
      label: '许可证名称'
    },
    {
      prop: 'expireDate',
      label: '证件到期日'
    },
    {
      prop: 'annualInspectionDate',
      label: '证件年检日'
    },
    {
      prop: 'fileList',
      label: '许可证附件',
      width: '320'
    }
  ]
  await saveRemote()
  isEdit.value = false
  await getDetail()
}

// 取消
const handleCancel = () => {
  option.value = [
    {
      prop: 'index',
      label: '序号',
      width: '50'
    },
    {
      prop: 'licenseName',
      label: '许可证名称'
    },
    {
      prop: 'expireDate',
      label: '证件到期日'
    },
    {
      prop: 'annualInspectionDate',
      label: '证件年检日'
    },
    {
      prop: 'fileList',
      label: '许可证附件',
      width: '320'
    }
  ]
  isEdit.value = false
  formData.value.licenseTable.tableData = cloneDeep(originData.value)
}
const handleAdd = () => {
  formData.value.licenseTable.tableData.push({
    licenseName: '',
    expireDate: '',
    annualInspectionDate: ''
  })
}

const handleDelete = index => {
  formData.value.licenseTable.tableData.splice(index, 1)
}

const isEdit = ref(false)
const saveRemote = async () => {
  const id = await useRemote(
    saveCustomerLicense,
    {
      ciId: formData.value.ciId,
      list: formData.value.licenseTable.tableData.map(item => {
        return {
          ...item,
          ciId: formData.value.ciId
        }
      })
    },
    [],
    '许可证件'
  )
  formData.value.licenseId = id
  return id
}
defineExpose({
  saveRemote
})
</script>
<style lang="scss" scoped>
:deep(.download-text) {
  width: 100%;
  display: block;
}
.date-picker.el-date-editor.el-input {
  width: 137px;
}
.m-g {
  margin-bottom: 12px;
}
.form-rap {
  :deep(.el-form-item.el-form-item--default) {
    margin-bottom: 0;
  }
}
</style>
