package com.jri.biz.service.contract;

import cn.hutool.core.util.ObjectUtil;
import com.jri.biz.constants.BizType;
import com.jri.biz.constants.ContractSignLocationEnum;
import com.jri.biz.domain.entity.CustomerContract;
import com.jri.biz.service.CommonBizFileService;
import com.jri.biz.service.CustomerContractService;
import com.jri.biz.utils.PdfboxUtil;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.AliyunOSSUtil;
import com.jri.common.utils.SecurityUtils;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.uuid.Seq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;

/**
 * 合同签名service
 *
 * <AUTHOR>
 * @since 2024/1/19 15:59
 */
@Slf4j
@Service
public class ContractSignService {

    @Resource
    private CustomerContractService customerContractService;

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private AliyunOSSUtil aliyunOSSUtil;

    /**
     * 合同签名
     *
     * @param contractId 合同id
     * @param file       文件
     */
    @Transactional(rollbackFor = Exception.class)
    public void sign(Long contractId, MultipartFile file) {
        CustomerContract contract = customerContractService.getById(contractId);
        // 判断是否已经签过
        if (ObjectUtil.equal(Boolean.TRUE, contract.getSignFlag())) {
            throw new ServiceException("合同已签名");
        }
        byte[] pdfByte = new byte[0];
        try {
            InputStream imageInputStream = file.getInputStream();
            // 获取文件
            CommonBizFile oldFile = commonBizFileService.selectOneByMainIdAndBizType(contractId, BizType.CONTRACT);
            if (ObjectUtil.isNull(oldFile)) {
                throw new ServiceException("合同文件不存在");
            }
            byte[] bytes = aliyunOSSUtil.getObject(oldFile.getUrls());

            ContractSignLocationEnum locationEnum = getContractSignLocationEnum(contract);

            pdfByte = PdfboxUtil.addPic(new ByteArrayInputStream(bytes), imageInputStream, locationEnum);
        } catch (IOException e) {
            log.error("合同签名异常", e);
        }
        // 新增
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), contract.getContractNo() + ".pdf");
        aliyunOSSUtil.saveObject(fileName, pdfByte);
        CommonBizFile newFile = new CommonBizFile();
        newFile.setFileNames(contract.getContractNo() + "-签名" + ".pdf");
        newFile.setUrls(fileName);
        newFile.setUploadBy(SecurityUtils.getLoginUser().getUser().getNickName());
        newFile.setUploadTime(LocalDateTime.now());
        newFile.setMainId(contract.getContractId());
        newFile.setBizType(BizType.CONTRACT);
        commonBizFileService.save(newFile);
        contract.setSignFlag(true);
        customerContractService.updateById(contract);
    }

    /**
     * 根据合同信息获取合同签名位置
     *
     * @param contract 合同
     * @return 合同签名位置
     */
    private static ContractSignLocationEnum getContractSignLocationEnum(CustomerContract contract) {
        ContractSignLocationEnum locationEnum = null;
        // 判断合同类型决定签名
        String contractType = contract.getContractType();
        String declareType = contract.getDeclareType();
        switch (contractType) {
            case "0" -> {
                if ("0".equals(declareType)) {
                    locationEnum = ContractSignLocationEnum.ACCOUNTING_ZERO;
                } else {
                    locationEnum = ContractSignLocationEnum.ACCOUNTING;
                }
            }
            case "1" -> locationEnum = ContractSignLocationEnum.DISPOSABLE;
            case "2" -> locationEnum = ContractSignLocationEnum.ADDRESS;
        }
        return locationEnum;
    }

}
