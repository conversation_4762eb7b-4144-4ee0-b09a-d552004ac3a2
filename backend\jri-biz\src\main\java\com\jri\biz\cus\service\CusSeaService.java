package com.jri.biz.cus.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.convert.CusSeaConvert;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.entity.CusSea;
import com.jri.biz.cus.domain.request.CusSeaForm;
import com.jri.biz.cus.domain.request.CusSeaQuery;
import com.jri.biz.cus.domain.vo.CusSeaInventoryVO;
import com.jri.biz.cus.domain.vo.CusSeaListVO;
import com.jri.biz.cus.domain.vo.CusSeaVO;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.biz.cus.mapper.CusSeaInventoryMapper;
import com.jri.biz.cus.mapper.CusSeaMapper;
import com.jri.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 公海配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Service
public class CusSeaService extends ServiceImpl<CusSeaMapper, CusSea> {

    @Resource
    private CusCustomerOrClueMapper cusCustomerOrClueMapper;

    @Resource
    private CusSeaInventoryMapper cusSeaInventoryMapper;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusSeaListVO> listPage(CusSeaQuery query) {
        var page = new Page<CusSeaListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CusSeaVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(CusSeaForm form) {
        CusSea cusSea = CusSeaConvert.INSTANCE.convert(form);
        Integer duration = cusSea.getDuration();
        Integer recovery = cusSea.getRecovery();
        if (ObjectUtil.isEmpty(duration)) {
            if (ObjectUtil.isNotEmpty(recovery)) {
                throw new ServiceException("未填写资源回收规则,不能填写回收提醒");
            }
        } else {
            if (ObjectUtil.isNotEmpty(recovery)) {
                if (duration <= recovery) {
                    throw new ServiceException("资源回收规则必须大于回收提醒");
                }
            }
        }
        saveOrUpdate(cusSea);
    }


    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        LambdaQueryWrapper<CusCustomerOrClue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCustomerOrClue::getSeaId, id);
        wrapper.eq(CusCustomerOrClue::getIsSea, "1");
        if (cusCustomerOrClueMapper.selectCount(wrapper) > 0) {
            throw new ServiceException("该公海中存在资源,不可删除");
        }
        CusSeaInventoryVO inventory = cusSeaInventoryMapper.getDetailById();
        if (null != inventory) {
            if (id.equals(inventory.getCusSeaId()) || id.equals(inventory.getClueSeaId())) {
                throw new ServiceException("该公海在保有量中设置,不可删除");
            }
        }
        return removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setStatus(Long id) {
        CusSea byId = getById(id);
        if (null == byId) {
            throw new ServiceException("工单类型不存在");
        }
        // 状态取反
        String status = StrUtil.equals(byId.getStatus(), "0") ? "1" : "0";
        byId.setStatus(status);
        updateById(byId);
    }
}
