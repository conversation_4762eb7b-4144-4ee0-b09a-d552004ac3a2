package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 联系人
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_cc_contact")
@ApiModel(value = "CusCcContact对象", description = "联系人")
public class CusCcContact implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 姓名
     */
    private String contactName;

    /**
     * 手机号
     */
    private String contactPhone;

    /**
     * 职位
     */
    private String post;

    /**
     * 微信
     */
    private String wx;

    /**
     * QQ
     */
    private String qq;

    /**
     * 性别0-未知 1-男 2-女
     */
    private String sex;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 来源(统计用)0-直接创建1-由线索转为客户联系人
     */
    private String source;

    /**
     * 线索/客户id
     */
    private Long ccId;

    /**
     * 转为客户时间
     */
    private LocalDateTime changeTime;

    /**
     * 是否决策人0-否1-是
     */
    private Integer isLeader;


    /**
     * 是否常用联系人0-否1-是
     */
    private Integer isOften;

    /**
     * 线索来源
     */
    private String clueSource;
}
