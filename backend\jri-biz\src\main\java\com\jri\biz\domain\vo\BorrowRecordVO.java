package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.biz.domain.entity.BizNodeHistory;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 借阅记录视图对象
 *
 * <AUTHOR>
 * @since 2023-07-11
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BorrowRecordVO视图对象")
public class BorrowRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("合同id")
    private Long mainId;

    @ApiModelProperty("借阅人")
    private Long userId;

    @ApiModelProperty("借阅人姓名")
    private String nickName;

    @ApiModelProperty("借阅到期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expirationTime;

    @ApiModelProperty("借阅事由")
    private String borrowReason;

    @ApiModelProperty("借阅状态0-待审批 1-通过 2-驳回")
    private String status;


    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("合同类型名称")
    private String contractTypeName;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty("合同附件")
    private CommonBizFile file;

    @ApiModelProperty("审批节点列表")
    private List<BizNodeHistory> reviewList;

    public String getContractTypeName() {
        String contractType = getContractType();
        if ("0".equals(contractType)) {
            return "记账合同";
        } else if ("1".equals(contractType)) {
            return "一次性合同";
        } else if ("2".equals(contractType)) {
            return "地址服务协议合同";
        } else {
            return null;
        }
    }
}