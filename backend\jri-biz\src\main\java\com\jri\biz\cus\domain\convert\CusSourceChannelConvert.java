package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourceChannel;
import com.jri.biz.cus.domain.request.CusSourceChannelForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客资来源 渠道 关联对象转换
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Mapper
public interface CusSourceChannelConvert {
    CusSourceChannelConvert INSTANCE = Mappers.getMapper(CusSourceChannelConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusSourceChannel convert(CusSourceChannelForm form);

}