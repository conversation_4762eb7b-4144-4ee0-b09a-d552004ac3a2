package com.jri.biz.cus.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 股东信息 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-11-08
 */

@Data
@NoArgsConstructor
@ApiModel(value="客资股东信息表单请求对象")
public class CusCcShareholderInfoForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("股东姓名")
    private String shareholderName;

    @ApiModelProperty("股东手机号")
    private String shareholderPhone;

    @ApiModelProperty("线索/客户id")
    private Long ccId;

    @ApiModelProperty("股东附件")
    private List<CommonBizFile> shareholderFileList;
}
