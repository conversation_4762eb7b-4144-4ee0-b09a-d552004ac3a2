package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2024/7/2 9:48
 *
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "线索管理页面头部统计")
public class ClueHeaderStatisticVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "线索创建总数")
    private Long totalCreate = 0L;

    @ApiModelProperty(value = "今日线索创建数")
    private Long todayCreate = 0L;

    @ApiModelProperty(value = "线索创建待分配总数")
    private Long totalCreateWaitAssign = 0L;

    @ApiModelProperty(value = "今日线索创建待分配数")
    private Long todayCreateWaitAssign = 0L;

}
