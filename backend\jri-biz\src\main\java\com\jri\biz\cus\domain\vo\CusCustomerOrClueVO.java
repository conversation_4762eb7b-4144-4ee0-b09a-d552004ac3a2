package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.biz.cus.domain.entity.CusCcTags;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索/客户信息视图对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCustomerOrClueVO视图对象")
public class CusCustomerOrClueVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("线索来源(客户来源)")
    private String source;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @ApiModelProperty("来源名称")
    private String sourceName;

    @ApiModelProperty("介绍客户id")
    private Long introductionCustomerId;

    @ApiModelProperty("介绍客户名称")
    private String introductionCustomerName;

    @ApiModelProperty("公司名称(客户名称)")
    private String companyName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("行业")
    private String industry;

    @ApiModelProperty("地区")
    private String area;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("税务性质")
    private String taxNature;

    @ApiModelProperty("首次跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate firstFollowTime;

    @ApiModelProperty("客户等级")
    private String level;

    @ApiModelProperty("公海id")
    private Long seaId;

    @ApiModelProperty("公海名称")
    private String seaName;

    @ApiModelProperty("公海管理员id")
    private Long manageId;

    @ApiModelProperty("是否正在公海中0-否1-是")
    private String isSea;

    @ApiModelProperty("主联系人id")
    private Long mainContactId;

    @ApiModelProperty("类型0-线索1-客户2-线索关联已有客户")
    private String type;

    @ApiModelProperty("录入方式0-手动录入1-公海录入")
    private String entryType;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("最近跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFollowTime;

    @ApiModelProperty("领取/分配时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime getTime;

    @ApiModelProperty("最近跟进人")
    private String lastFollowUser;

    @ApiModelProperty("跟进状态")
    private String followStatus;

    @ApiModelProperty("当前处理人")
    private Long currentUserId;

    @ApiModelProperty("当前处理人姓名(跟进人)")
    private String currentUserName;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("成为客户时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime becomeTime;

    @ApiModelProperty("回收时长")
    private Integer duration;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("产品名称")
    private String productName;

    // *************联系人表******************

    @ApiModelProperty("姓名")
    private String contactName;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("职位")
    private String post;

    @ApiModelProperty("微信")
    private String wx;

    @ApiModelProperty("QQ")
    private String qq;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("生日")
    private String birthday;

    //************标签*************

    @ApiModelProperty("标签列表")
    private List<CusCcTags> tags;

    @ApiModelProperty("是否有新注册业务")
    private Boolean isExtra = false;

    @ApiModelProperty("掉保时长")
    private String timeStr;

    @ApiModelProperty("最近修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedTime;

    @ApiModelProperty("保护开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime protectionStartTime;

    @ApiModelProperty("部门id列表")
    private List<Long> deptIds;

    @ApiModelProperty("来源业务类型")
    private String sourceBiz;

    @ApiModelProperty("来源申诉状态")
    private String sourceAppealStatus;

    private Boolean appealFlag;
}