package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 客户联系人查询类
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="客户联系人查询对象")
public class CustomerContactQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户主表id")
    private Long ciId;


}
