package com.jri.biz.domain.entity;

import com.jri.common.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/9/6 11:14
 */
@Getter
@Setter
public class CustomerTaxInformationImportDto {

    @Excel(name = "企业名称")
    private String customerName;

    @Excel(name = "登记税务机关")
    private String taxRegistrationOrganStr;

    @Excel(name = "税种登记")
    private String rateRegistration;

    @Excel(name = "办税员实名认证")
    private String taxRealNameStr;

    @Excel(name = "预留手机号")
    private String reservedPhoneNumber;

    @Excel(name = "所得税认定方式")
    private String identificationMethodStr;

    @Excel(name = "证书帐号")
    private String certificateAccount;

    @Excel(name = "证书密码")
    private String certificatePassword;

    @Excel(name = "客户开票资料")
    private String customerBillingInformationFileUrl;

    @Excel(name = "税务资料")
    private String taxInformationFileUrl;

    @Excel(name = "开票盘")
    private String drawingSheetFlag;

    @Excel(name = "发票")
    private String invoiceFlag;

    @Excel(name = "发票章")
    private String invoiceSealFlag;

    @Excel(name = "一般纳税人认定表")
    private String taxpayerIdentificationFileUrl;

    @Excel(name = "发票类型")
    private String invoiceType;

    @Excel(name = "开票盘类型")
    private String drawingSheetType;

    @Excel(name = "看点时间")
    private String watchTime;

    @Excel(name = "进出口认定时间")
    private String approvalTime;

    @Excel(name = "看点说明")
    private String watchInstructions;

    @Excel(name = "社保开户")
    private String socialAccountStr;

    @Excel(name = "社保账号")
    private String socialAccount;

    @Excel(name = "社保密码")
    private String socialPassword;

    @Excel(name = "社保开户附件")
    private String socialAccountOpenFileUrl;

    @Excel(name = "公积金开户")
    private String fundAccountStr;

    @Excel(name = "公积金账号")
    private String fundAccount;

    @Excel(name = "公积金密码")
    private String fundPassword;

    @Excel(name = "公积金开户附件")
    private String fundAccountOpenFileUrl;
}