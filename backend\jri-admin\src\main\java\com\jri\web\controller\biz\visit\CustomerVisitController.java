package com.jri.web.controller.biz.visit;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.request.visit.CustomerVisitForm;
import com.jri.biz.domain.request.visit.CustomerVisitQuery;
import com.jri.biz.domain.vo.visit.CustomerVisitListVO;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.ProgressService;
import com.jri.biz.service.visit.CustomerVisitService;
import com.jri.common.core.domain.R;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Validated
@RestController
@RequestMapping("/customerVisit")
@Api(tags = "拜访计划")
public class CustomerVisitController {

    @Resource
    private CustomerVisitService customerVisitService;

    @Resource
    private ExportService exportService;

    @Resource
    private ProgressService progressService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerVisitListVO>> listPage(CustomerVisitQuery query) {
        return R.ok(customerVisitService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerVisitListVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerVisitService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CustomerVisitForm form) {
        return R.ok(customerVisitService.add(form));
    }

    @PostMapping("/batchSave")
    @ApiOperation("批量保存数据")
    public R<Void> batchSave(@RequestBody @Valid CustomerVisitForm form) {
        customerVisitService.batchAdd(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerVisitService.deleteById(id));
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public R<Long> export(@Validated @RequestBody CustomerVisitQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "拜访计划.xlsx");
        var id = progressService.create(ProgressType.VISIT_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        exportService.visitExport(fileName, query, id);
        return R.ok(id);
    }

}

