package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 商机查询类
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="商机查询对象")
public class CusCcBusinessQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("商机名称")
    private String name;

    @ApiModelProperty("公司名称(客户名称)")
    private String companyName;

    @ApiModelProperty("销售阶段")
    private String stage;

    @ApiModelProperty("跟进状态0-未跟进 1-跟进中 2-赢单 3-输单")
    private String followStatus;

    private Long userId;
}
