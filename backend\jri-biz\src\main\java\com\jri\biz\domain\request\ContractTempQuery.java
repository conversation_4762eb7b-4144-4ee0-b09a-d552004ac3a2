package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 合同模板查询类
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="合同模板查询对象")
public class ContractTempQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("模板名称")
    private String tempName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;

    @ApiModelProperty("状态0-待审批 1-通过 2-驳回")
    private String status;

    private Long userId;
}
