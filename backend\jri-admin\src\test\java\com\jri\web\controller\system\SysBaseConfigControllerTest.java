package com.jri.web.controller.system;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @since 2023/5/31 10:33
 */
@SpringBootTest
class SysBaseConfigControllerTest {

    @Autowired
    private SysBaseConfigController controller;

    @Test
    void list() throws Exception{
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/system/baseConfig/list")
                .param("id", "1663462608693215233");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }
}