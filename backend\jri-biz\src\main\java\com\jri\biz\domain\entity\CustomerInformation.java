package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_information")
@ApiModel(value = "CustomerInformation对象", description = "")
public class CustomerInformation implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @TableId(value = "customer_id", type = IdType.ASSIGN_ID)
    private Long customerId;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 主联系人id
     */
    private Long contractMainId;

    /**
     * 客户经理
     */
    private String manger;

    @ApiModelProperty(value = "客户经理+2级部门")
    private String mangerAndDept;

    @ApiModelProperty("财税顾问+2级部门")
    private String counselorAndDept;

    @ApiModelProperty("客户成功+2级部门")
    private String customerSuccessAndDept;

    @ApiModelProperty("主办会计+2级部门")
    private String sponsorAccountingAndDept;

    @ApiModelProperty("所有业务员2级部门id")
    private String seceondDeptArry;

    /**
     * 所属分公司
     */
    private String branchOffice;

    /**
     * 实际经营地址
     */
    private String address;

    /**
     * 企业联系信息备注
     */
    private String informationMark;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 客户性质
     */
    private String customerProperty;

    /**
     * 不收费原因备注
     */
    private String nofeeReasonMark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 是否废弃，1是0否
     */
    private int discard;

    /**
     * 废弃原因
     */
    private String discardReason;

    /**
     * 从事行业
     */
    private String industry;

    @ApiModelProperty("不收费原因")
    private String nofeeReason;

    @ApiModelProperty("财税顾问")
    private String counselor;

    @ApiModelProperty("客户成功")
    private String customerSuccess;

    @ApiModelProperty("主办会计")
    private String sponsorAccounting;

    @ApiModelProperty(value = "资料完善度")
    private BigDecimal completeness;

    @ApiModelProperty(value = "客户经理id")
    @TableField(fill = FieldFill.UPDATE)
    private Long mangerUserId;

    @ApiModelProperty(value = "开票员id")
    @TableField(fill = FieldFill.UPDATE)
    private Long counselorUserId;

    @ApiModelProperty(value = "客户成功id")
    @TableField(fill = FieldFill.UPDATE)
    private Long customerSuccessUserId;

    @ApiModelProperty(value = "主办会计id")
    @TableField(fill = FieldFill.UPDATE)
    private Long sponsorAccountingUserId;

    /**
     * 钱包金额
     */
    @ApiModelProperty(value = "钱包金额")
    private BigDecimal walletAmount;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "客户经理关联时间")
    private LocalDateTime mangerLinkTime;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "开票员关联时间")
    private LocalDateTime counselorLinkTime;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "客户成功关联时间")
    private LocalDateTime customerSuccessLinkTime;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "主办会计关联时间")
    private LocalDateTime sponsorAccountingLinkTime;

    @ApiModelProperty(value = "企业认定")
    @TableField(fill = FieldFill.UPDATE)
    private String companyIdentification;

    @ApiModelProperty("是否为风险客户")
    private Boolean riskFlag;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;

}
