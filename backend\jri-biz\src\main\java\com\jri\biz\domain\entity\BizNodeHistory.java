package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 流程历史
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("biz_node_history")
@ApiModel(value = "BizNodeHistory对象", description = "流程历史")
public class BizNodeHistory implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 审批人id
     */
    @ApiModelProperty("审批人id")
    @TableField(fill = FieldFill.UPDATE)
    private Long userId;

    /**
     * 节点key
     */
    @ApiModelProperty("节点key")
    private Integer nodeKey;

    /**
     * 父节点key
     */
    @ApiModelProperty("父节点key")
    private Integer parentKey;

    /**
     * 主表id
     */
    @ApiModelProperty("主表id")
    private Long mainId;

    /**
     * 类型0-模板审批流程1-合同借阅2-合同评审
     */
    @ApiModelProperty("类型0-模板审批流程1-合同借阅2-合同评审")
    private String type;

    /**
     * 合同状态0-待审批 1-通过 2-驳回
     */
    @ApiModelProperty("审批结果0-待审批 1-通过 2-驳回")
    private String reviewStatus;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String reason;

    /**
     * 是否当前节点1-是0-否
     */
    @ApiModelProperty("是否当前节点1-是0-否")
    private String isNow;

    /**
     * 审批人姓名
     */
    @ApiModelProperty("审批人姓名")
    @TableField(exist = false)
    private String nickName;
}
