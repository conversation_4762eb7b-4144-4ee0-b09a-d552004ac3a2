package com.jri.biz.domain.vo.turnoverStatement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "客户账单详情VO")
public class TurnoverStatementMonthInfoVO implements Serializable {


    @ApiModelProperty("欠费大类名字")
    private String feeTypeName;

    @ApiModelProperty("欠费金额")
    private BigDecimal arrearageAmount;


}
