package com.jri.biz.domain.vo.fund;

import com.jri.biz.domain.entity.fund.MonthlyTurnover;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="TurnoverOfMonthDetailVo视图对象")
public class MonthlyTurnoverDetailPreVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerNo;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "当月营业额")
    private BigDecimal monthlyTurnover;

    @ApiModelProperty(value = "记账当月营业额")
    private BigDecimal bookkeepingMonthlyTurnover;

    @ApiModelProperty(value = "地址当月营业额")
    private BigDecimal addressMonthlyTurnover;

    @ApiModelProperty(value = "客户经理")
    private String manger;

    @ApiModelProperty(value = "财税顾问")
    private String counselor;

    @ApiModelProperty(value = "客户成功")
    private String customerSuccess;

    @ApiModelProperty(value = "主办会计")
    private String sponsorAccounting;

    @ApiModelProperty(value = "每个月的月营业额")
    private List<MonthlyTurnover> MonthlyTurnoverList;

    @ApiModelProperty(value = "账单的月营业额")
    private List<MonthlyTurnoverDetailPreChildVo> monthlyTurnoverDetailPreChildVoList;

//    @ApiModelProperty(value = "收款单对象")
//    private List<FinanceReceiptVO> financeReceiptVOList;
}
