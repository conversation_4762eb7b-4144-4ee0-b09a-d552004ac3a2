package com.jri.biz.domain.vo.fund;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "TurnoverOfMonthDetailVo视图对象")
public class MonthlyTurnoverDetailPreChildVo implements Serializable {

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "账期")
    private Integer paymentDate;

    @ApiModelProperty(value = "账单id")
    private Long paymentId;

    @ApiModelProperty(value = "账单NO")
    private String paymentNo;

    @ApiModelProperty(value = "费用类别")
    private String typeName;

    @ApiModelProperty(value = "账期开始时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date paymentStartTime;

    @ApiModelProperty(value = "账期结束时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date paymentEndTime;

    @ApiModelProperty(value = "账单金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "总收款金额")
    private BigDecimal sumReceiptAmount;

    @ApiModelProperty(value = "平均到月的收款额(未算账册费)")
    private BigDecimal monthAmount;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "收齐账款日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String lastReceiptDate;

    @ApiModelProperty(value = "优惠类型")
    private String discount;

    @ApiModelProperty(value = "优惠时长")
    private Integer discountTime;


//    @ApiModelProperty(value = "收款单对象")
//    private List<FinanceReceiptVO> financeReceiptVOList;
}
