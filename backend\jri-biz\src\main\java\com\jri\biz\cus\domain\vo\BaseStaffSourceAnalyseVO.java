package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/4 14:26
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "人员基础来源统计VO")
public class BaseStaffSourceAnalyseVO extends BaseClueSourceAnalyseVO {

    @ApiModelProperty("部门名称")
    private String deptName;

}
