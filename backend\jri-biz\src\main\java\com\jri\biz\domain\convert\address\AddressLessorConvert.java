package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressLessor;
import com.jri.biz.domain.request.address.AddressLessorForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 出租人对象转换
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Mapper
public interface AddressLessorConvert {
    AddressLessorConvert INSTANCE = Mappers.getMapper(AddressLessorConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    AddressLessor convert(AddressLessorForm form);

}