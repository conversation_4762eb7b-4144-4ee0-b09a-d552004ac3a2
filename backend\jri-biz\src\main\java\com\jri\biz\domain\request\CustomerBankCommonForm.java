package com.jri.biz.domain.request;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 一般户信息 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-13
 */

@Data
@NoArgsConstructor
@ApiModel(value="一般户信息表单请求对象")
public class CustomerBankCommonForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 银行信息id
     */
    private Long bankId;

    /**
     * 一般户开户银行
     */
    private String commonBankName;

    /**
     * 一般户账号
     */
    private String commonBankAccount;

    /**
     * 是否有一般户网银(0-没有 1-有)
     */
    private String commonInternetbankFlag;

    /**
     * 一般户回单卡账号
     */
    private String commonInternetbankAccount;

    /**
     * 一般户回单卡密码
     */
    private String commonReceiptCardPassword;

    /**
     * 一般户回单卡类型
     */
    private String commonInternetbankType;

    /**
     * 是否有一般户回单卡(0-没有 1-有)
     */
    private String commonReceiptCardFlag;
}
