package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Valid
@NoArgsConstructor
@ApiModel(value = "合同审批表单对象")
public class ReviewAuditForm {

    /**
     * 主表id
     */
    @ApiModelProperty("主表id(合同id)")
    @NotNull(message = "主表id不能为空")
    private Long mainId;

    /**
     * 类型0-模板审批流程1-合同借阅2-合同评审
     */
    @ApiModelProperty("类型0-模板审批流程1-合同借阅2-合同评审")
    @NotEmpty(message = "类型不能为空")
    private String type;

    /**
     * 合同状态0-待审批 1-通过 2-驳回
     */
    @ApiModelProperty("审批结果0-待审批 1-通过 2-驳回")
    @NotEmpty(message = "审批结果不能为空")
    private String reviewStatus;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String reason;

    /**
     * 自动审批标志
     */
    private Boolean autoAuditFlag = false;
}
