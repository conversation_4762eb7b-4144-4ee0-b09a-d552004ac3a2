package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourcePlatform;
import com.jri.biz.cus.domain.request.CusSourcePlatformForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客资来源 平台 关联对象转换
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Mapper
public interface CusSourcePlatformConvert {
    CusSourcePlatformConvert INSTANCE = Mappers.getMapper(CusSourcePlatformConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusSourcePlatform convert(CusSourcePlatformForm form);

}