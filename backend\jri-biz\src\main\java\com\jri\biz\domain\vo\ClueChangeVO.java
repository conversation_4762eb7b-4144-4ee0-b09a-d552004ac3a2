package com.jri.biz.domain.vo;

import com.jri.biz.domain.common.ECharsPair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "ClueChangeVO视图对象")
public class ClueChangeVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索转换率")
    private BigDecimal clueChangeRate;

    @ApiModelProperty("线索转换金额")
    private Long amount;

    @ApiModelProperty("来源列表")
    private List<ECharsPair<String, ClueChangeECharVO>> sourceList;
}
