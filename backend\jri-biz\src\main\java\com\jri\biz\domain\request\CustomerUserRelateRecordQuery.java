package com.jri.biz.domain.request;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2024/4/18 9:46
 *
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "人员分配查询对象")
public class CustomerUserRelateRecordQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long customerId;

    private String role;

}
