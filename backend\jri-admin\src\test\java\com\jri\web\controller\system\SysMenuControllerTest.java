package com.jri.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.jri.framework.security.context.AuthenticationContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @since 2023/6/5 15:17
 */
@SpringBootTest
class SysMenuControllerTest {
    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private SysMenuController controller;

    @BeforeEach
    void before() {
        var authenticationToken = new UsernamePasswordAuthenticationToken("admin", "123456Aa.");
        AuthenticationContextHolder.setContext(authenticationToken);
        var authentication = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }

    @Test
    void list() throws Exception {
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/system/menu/list");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString(StandardCharsets.UTF_8);
        System.out.println(result);
    }

    @Test
    void roleMenuTreeselect() throws Exception {
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/system/menu/roleMenuTreeselect/2")
                .param("roleId", "2");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString(StandardCharsets.UTF_8);
        System.out.println(result);
    }
}