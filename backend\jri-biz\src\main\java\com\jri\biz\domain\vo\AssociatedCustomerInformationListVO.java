package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class AssociatedCustomerInformationListVO {
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 客户经理
     */
    @ApiModelProperty(value = "客户经理")
    private String manger;


    /**
     * 客户状态
     */
    @ApiModelProperty(value = "客户状态")
    private String customerStatus;

    /**
     * 客户性质
     */
    @ApiModelProperty(value = "客户性质")
    private String customerProperty;

    @ApiModelProperty(value = "所属行业")
    private String industry;

}
