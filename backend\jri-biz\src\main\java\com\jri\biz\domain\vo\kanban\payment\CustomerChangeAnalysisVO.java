package com.jri.biz.domain.vo.kanban.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/9/4 13:49
 */
@Getter
@Setter
@ApiModel(value = "NewCustomerVO", description = "记账企业/办证企业 客户数量变化")
public class CustomerChangeAnalysisVO {

    @ApiModelProperty("记账企业")
    private CustomerChangeVO bookkeeping;

    @ApiModelProperty("办证企业")
    private CustomerChangeVO license;

}
