package com.jri.biz.domain.request.address;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 地址申请 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-12-01
 */

@Data
@NoArgsConstructor
@ApiModel(value = "地址申请表单请求对象")
public class AddressApplyForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("账单id")
    private Long paymentId;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("托管分类")
    private String hostingType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("图片附件")
    private List<CommonBizFile> imageFileList;

    @ApiModelProperty("文件附件")
    private List<CommonBizFile> fileList;

}
