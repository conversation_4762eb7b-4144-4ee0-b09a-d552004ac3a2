package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcTags;
import com.jri.biz.cus.domain.request.CusCcTagsForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 线索/客户标签对象转换
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Mapper
public interface CusCcTagsConvert {
    CusCcTagsConvert INSTANCE = Mappers.getMapper(CusCcTagsConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCcTags convert(CusCcTagsForm form);

}