package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerTaxRebateIdentified;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedListVO;
import com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedVO;
import com.jri.biz.domain.request.CustomerTaxRebateIdentifiedQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerTaxRebateIdentifiedMapper extends BaseMapper<CustomerTaxRebateIdentified> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerTaxRebateIdentifiedListVO> listPage(@Param("query") CustomerTaxRebateIdentifiedQuery query, Page<CustomerTaxRebateIdentifiedListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerTaxRebateIdentifiedVO getDetailById(@Param("id") Long id);

    CustomerTaxRebateIdentified selectByMainId(@Param("id") Long id);
}
