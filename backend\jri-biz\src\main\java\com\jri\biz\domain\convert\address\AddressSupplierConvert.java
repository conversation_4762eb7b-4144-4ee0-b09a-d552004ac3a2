package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressSupplier;
import com.jri.biz.domain.request.address.AddressSupplierForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 地址供应商对象转换
 *
 * <AUTHOR>
 * @since 2023-11-30
 */

@Mapper
public interface AddressSupplierConvert {
    AddressSupplierConvert INSTANCE = Mappers.getMapper(AddressSupplierConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    AddressSupplier convert(AddressSupplierForm form);

}