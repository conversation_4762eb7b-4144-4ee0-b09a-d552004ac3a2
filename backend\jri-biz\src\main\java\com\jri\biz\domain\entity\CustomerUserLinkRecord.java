package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2023/10/19 10:11
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_user_link_record")
@ApiModel(value = "CustomerUserLinkRecord对象", description = "客户用户关联表")
public class CustomerUserLinkRecord implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "角色")
    private String role;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "开始关联时间")
    private LocalDate linkTime;

    @ApiModelProperty(value = "结束关联时间")
    private LocalDate unlinkTime;
}
