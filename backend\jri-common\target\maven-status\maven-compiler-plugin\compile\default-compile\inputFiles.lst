C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\entity\SysDept.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\CheckedException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\filter\RepeatedlyRequestWrapper.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\vo\ChildrenVO.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\datascope\DataScope.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\constant\CacheConstants.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\ServiceException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\CoordinateTransformUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\redis\RedisCache.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\file\FileUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\xss\XssValidator.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\entity\SysRole.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\page\PageDomain.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\model\LoginBody.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\BaseQuery.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\ip\IpUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\Anonymous.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\uuid\UUID.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\user\CaptchaExpireException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\reflect\ReflectUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\uuid\IdUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\base\BaseException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\Excel.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\R.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\page\TableSupport.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\AjaxResult.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\UtilException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\vo\SysBaseConfigVO.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\enums\HttpMethod.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\constant\ScheduleConstants.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\user\UserPasswordNotMatchException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\file\FileNameLengthLimitExceededException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\PageUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\enums\DataSourceType.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\user\UserException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\DemoModeException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\entity\SysBaseConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\ExceptionUtil.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\entity\SysDictType.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\file\ImageUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\user\UserNotExistsException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\page\TableDataInfo.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\sql\SqlUtil.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\file\FileException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\entity\SysMenu.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\RedisService.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\constant\UserConstants.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\TreeNode.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\query\PageQuery.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\text\Convert.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\OrderNoUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\RepeatSubmit.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\user\BlackListException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\model\RegisterBody.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\enums\LimitType.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\text\CharsetKit.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\constant\Constants.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\CommonBizFile.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\service\UserDept.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\http\HttpUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\DateUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\GeoUtil.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\DataScope.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\text\StrFormatter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\job\TaskException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\page\TableDataInfoRes.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\datascope\DataScopeInnerInterceptor.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\bean\BeanValidators.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\spring\SpringUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\file\MimeTypeUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\DataSource.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\bean\BeanUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\Excels.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\SecurityUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\FileInfo.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\http\HttpHelper.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\file\FileTypeUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\html\HTMLFilter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\entity\SysDictData.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\enums\OperatorType.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\MessageUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\file\FileUploadUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\config\JRIConfig.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\file\FileSizeLimitExceededException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\poi\ExcelUtil.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\sign\Md5Utils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\enums\UserStatus.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\sign\Base64.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\Log.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\LogUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\constant\GenConstants.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\poi\ExcelHandlerAdapter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\filter\RepeatableFilter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\filter\XssHttpServletRequestWrapper.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\file\FileUploadException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\enums\BusinessStatus.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\enums\BusinessType.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\BaseEntity.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\ip\AddressUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\constant\HttpStatus.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\entity\SysUser.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\annotation\RateLimiter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\AliyunOSSUtil.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\ServletUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\Arith.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\uuid\Seq.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\xss\Xss.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\filter\PropertyPreExcludeFilter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\model\LoginUser.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\html\EscapeUtil.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\TreeSelect.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\user\UserPasswordRetryLimitExceedException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\Threads.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\user\CaptchaException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\poi\NewExcelUtil.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\domain\TreeEntity.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\GlobalException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\core\controller\BaseController.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\exception\file\InvalidExtensionException.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\StringUtils.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\datascope\DataScopeType.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\filter\XssFilter.java
C:\Users\<USER>\Desktop\jri-work\sanyou-pro\backend\jri-common\src\main\java\com\jri\common\utils\DictUtils.java
