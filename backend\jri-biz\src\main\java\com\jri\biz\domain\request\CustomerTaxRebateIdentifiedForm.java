package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Data
@NoArgsConstructor
@ApiModel(value = "出口退税表单请求对象")
public class CustomerTaxRebateIdentifiedForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

}
