package com.jri.biz.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.UserCustomerBindRecord;
import com.jri.biz.domain.request.UserCustomerBindRecordQuery;
import com.jri.biz.domain.vo.UserCustomerBindRecordListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户客户绑定记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public interface UserCustomerBindRecordMapper extends BaseMapper<UserCustomerBindRecord> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<UserCustomerBindRecordListVO> listPage(@Param("query") UserCustomerBindRecordQuery query, Page<UserCustomerBindRecordListVO> page);

    /**
     * 根据客户列表和用户id更新
     *
     * @param customerIdList 客户id列表
     * @param userId         用户id
     * @param bindStatus     绑定状态
     */
    default void addOrUpdateByCustomerIdAndUserId(List<Long> customerIdList, Long userId, String bindStatus) {
        var wrapper = new LambdaQueryWrapper<UserCustomerBindRecord>();
        for (Long customerId : customerIdList) {
            wrapper.eq(UserCustomerBindRecord::getUserId, userId);
            wrapper.eq(UserCustomerBindRecord::getCustomerId, customerId);
            var record = selectOne(wrapper);
            if (ObjectUtil.isNull(record)) {
                record = new UserCustomerBindRecord();
                record.setUserId(userId);
                record.setCustomerId(customerId);
                record.setBindStatus(bindStatus);
                insert(record);
            } else {
                record.setBindStatus(bindStatus);
                updateById(record);
            }
            wrapper.clear();
        }
    }

    /**
     * 批量解绑
     *
     * @param idList id列表
     */
    void batchUnbind(@Param("idList") List<Long> idList, @Param("bindStatus") String bindStatus);
}
