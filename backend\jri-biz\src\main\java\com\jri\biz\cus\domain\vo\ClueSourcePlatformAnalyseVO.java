package com.jri.biz.cus.domain.vo;

import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/1/3 10:22
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "直投来源统计VO")
public class ClueSourcePlatformAnalyseVO extends BaseClueSourceAnalyseVO {

    @Excel(name = "所属部门")
    private String deptName;

    @ApiModelProperty(value = "申诉量")
    private Long appealCount = 0L;

    @ApiModelProperty(value = "申诉成功量")
    private Long appealSuccessCount = 0L;

    @ApiModelProperty(value = "有效线索量")
    private Long credibleClueCount = 0L;

    @ApiModelProperty(value = "线索单价")
    private BigDecimal unitPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "线索成本")
    private BigDecimal cost = BigDecimal.ZERO;

}
