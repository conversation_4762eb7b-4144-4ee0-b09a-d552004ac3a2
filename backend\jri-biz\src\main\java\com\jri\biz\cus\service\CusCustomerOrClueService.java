package com.jri.biz.cus.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.cus.delay.RedisDelayQueueEnum;
import com.jri.biz.cus.delay.util.RedisDelayQueueUtil;
import com.jri.biz.cus.domain.convert.CusCustomerOrClueConvert;
import com.jri.biz.cus.domain.entity.*;
import com.jri.biz.cus.domain.request.*;
import com.jri.biz.cus.domain.vo.*;
import com.jri.biz.cus.mapper.CusCcContactMapper;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.biz.domain.entity.BusinessProduct;
import com.jri.biz.domain.entity.BusinessType;
import com.jri.biz.domain.entity.CustomerContact;
import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.domain.request.CustomerInformationForm;
import com.jri.biz.mapper.BusinessProductMapper;
import com.jri.biz.mapper.BusinessTypeMapper;
import com.jri.biz.mapper.CustomerContactMapper;
import com.jri.biz.service.CommonBizFileService;
import com.jri.biz.service.CustomerInformationService;
import com.jri.common.annotation.DataScope;
import com.jri.common.core.domain.R;
import com.jri.common.core.domain.entity.SysUser;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.SecurityUtils;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.bean.BeanUtils;
import com.jri.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 线索/客户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class CusCustomerOrClueService extends ServiceImpl<CusCustomerOrClueMapper, CusCustomerOrClue> {

    @Resource
    private CusCcContactMapper cusCcContactMapper;

    @Resource
    private CusCcRecordService cusCcRecordService;

    @Resource
    private CusCcTagsService cusCcTagsService;

    @Resource
    private CusCcFollowService cusCcFollowService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private CusCcShareService cusCcShareService;

    @Resource
    private CusSeaService cusSeaService;

    @Resource
    private CustomerInformationService customerInformationService;

    @Resource
    private CusCcBusinessService cusCcBusinessService;

    @Resource
    private CusBusinessBizService cusBusinessBizService;

    @Resource
    private CusSeaInventoryService cusSeaInventoryService;

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Resource
    private CustomerContactMapper customerContactMapper;

    @Resource
    private BusinessProductMapper businessProductMapper;

    @Resource
    private BusinessTypeMapper businessTypeMapper;

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CusCcShareholderInfoService cusCcShareholderInfoService;

    /**
     * 我的线索
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCustomerOrClueListVO> myClueList(CusCustomerOrClueQuery query) {
        var page = new Page<CusCustomerOrClueListVO>(query.getPageNum(), query.getPageSize());
        query.setUserId(SecurityUtils.getUserId());
        IPage<CusCustomerOrClueListVO> res = getBaseMapper().myClueList(query, page);
        List<CusCustomerOrClueListVO> records = res.getRecords();
        appealStatusHandle(records);
        records.forEach(item -> {
            List<String> tagsList = cusCcTagsService.getListByCcId(item.getId());
            item.setTagsName(String.join(",", tagsList));
        });
        return res;
    }

    /**
     * 共享线索
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCustomerOrClueListVO> shareClueList(CusCustomerOrClueQuery query) {
        var page = new Page<CusCustomerOrClueListVO>(query.getPageNum(), query.getPageSize());
        query.setUserId(SecurityUtils.getUserId());
        IPage<CusCustomerOrClueListVO> res = getBaseMapper().shareClueList(query, page);
        List<CusCustomerOrClueListVO> records = res.getRecords();
        appealStatusHandle(records);
        records.forEach(item -> {
            List<String> tagsList = cusCcTagsService.getListByCcId(item.getId());
            item.setTagsName(String.join(",", tagsList));
        });
        return res;
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CusCustomerOrClueVO getDetailById(Long id) {
        CusCustomerOrClueVO res = getBaseMapper().getDetailById(id);
        if (null != res) {
            handleCustomer(res);
            // 标签
            res.setTags(cusCcTagsService.getTagsListByCcId(res.getId()));

            // 是否有新注册业务
            List<CusBusinessBizVO> businessBizList = cusBusinessBizService.getAllBiz(id);
            for (CusBusinessBizVO item : businessBizList) {
                if ("内资注册".equals(item.getProductName()) || "内资注册 - 单办证".equals(item.getProductName())
                        || "外资注册".equals(item.getProductName()) || "外资注册 - 单办证".equals(item.getProductName())) {
                    res.setIsExtra(true);
                }
            }
            // 不在公海中 且跟进状态为 未跟进或者跟进中
            if ("0".equals(res.getIsSea()) && ("0".equals(res.getFollowStatus()) || "1".equals(res.getFollowStatus()))) {
                Integer duration = res.getDuration();
                if (ObjectUtil.isNotEmpty(duration) && duration > 0) {
                    res.setTimeStr(getTimeStr(res.getProtectionStartTime(), duration));
                }
            }

            BusinessProduct businessProduct = businessProductMapper.selectById(res.getProductId());
            if (null != businessProduct) {
                res.setProductName(businessProduct.getProductName());
            } else {
                BusinessType businessType = businessTypeMapper.selectById(res.getProductId());
                if (null != businessType) {
                    res.setProductName(businessType.getTypeName());
                }
            }


        }
        return res;
    }

    /**
     * 计算掉保时长
     *
     * @param protectionStartTime 保护开始时间
     * @param days                回收时长
     * @return 保护结束时间
     */
    private String getTimeStr(LocalDateTime protectionStartTime, int days) {
        LocalDateTime max = protectionStartTime;
        max = max.plusDays(days);
        LocalDateTime now = LocalDateTime.now();
        if (max.isAfter(now)) {
            // 获取两个日期相差秒
            long betweenSs = ChronoUnit.SECONDS.between(now, max);
            long day = TimeUnit.SECONDS.toDays(betweenSs);
            long hours = TimeUnit.SECONDS.toHours(betweenSs) - TimeUnit.DAYS.toHours(TimeUnit.SECONDS.toDays(betweenSs));
            long minutes = TimeUnit.SECONDS.toMinutes(betweenSs) - TimeUnit.HOURS.toMinutes(TimeUnit.SECONDS.toHours(betweenSs));
            long seconds = TimeUnit.SECONDS.toSeconds(betweenSs) - TimeUnit.MINUTES.toSeconds(TimeUnit.SECONDS.toMinutes(betweenSs));
            return StringUtils.format("{}天{}小时{}分钟{}秒", day, hours, minutes, seconds);
        } else {
            return "";
        }
    }


    /**
     * 保存
     *
     * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(CusCustomerOrClueForm form) {
        final LocalDateTime now = LocalDateTime.now();
        CusCustomerOrClue cusCustomerOrClue = CusCustomerOrClueConvert.INSTANCE.convert(form);
        cusCustomerOrClue.setLastModifiedTime(now);

        // 联系人表
        CusCcContact cusCcContact = new CusCcContact();
        cusCcContact.setCcId(cusCustomerOrClue.getId());
        cusCcContact.setContactName(form.getContactName());
        cusCcContact.setContactPhone(form.getContactPhone());
        cusCcContact.setBirthday(form.getBirthday());
        cusCcContact.setPost(form.getPost());
        cusCcContact.setWx(form.getWx());
        cusCcContact.setQq(form.getQq());
        cusCcContact.setSex(form.getSex());
        cusCcContact.setEmail(form.getEmail());
        cusCcContact.setClueSource(form.getSource());

        if (null == cusCustomerOrClue.getId()) {
            // 如果手动录入 设置当前处理人为自己
            if ("0".equals(cusCustomerOrClue.getEntryType())) {
                // 判断私海数量是否超过保有量上限
                isOver(SecurityUtils.getUserId(), form.getType());
                cusCustomerOrClue.setCurrentUserId(SecurityUtils.getUserId());
                cusCustomerOrClue.setProtectionStartTime(now);
                cusCustomerOrClue.setIsSea("0");
            }
            // 如果公海录入 设置是否公海为1-是
            if ("1".equals(cusCustomerOrClue.getEntryType())) {
                cusCustomerOrClue.setIsSea("1");
                if (!isMemberOfSea(form.getSeaId(), SecurityUtils.getUserId())) {
                    throw new ServiceException("用户不是该公海成员");
                }
            }
            // 如果是新建客户 填充成为客户时间
            if ("1".equals(cusCustomerOrClue.getType())) {
                cusCustomerOrClue.setBecomeTime(now);
            }
            save(cusCustomerOrClue);

            cusCcContactMapper.insert(cusCcContact);
            cusCustomerOrClue.setMainContactId(cusCcContact.getId());
            updateById(cusCustomerOrClue);
            // 标签
            List<CusCcTags> tags = form.getTags();
            if (ObjectUtil.isNotEmpty(tags)) {
                tags.forEach(item -> item.setCcId(cusCustomerOrClue.getId()));
                cusCcTagsService.saveBatch(tags);
            }
            // 操作记录
            if ("1".equals(cusCustomerOrClue.getType())) {
                cusCcRecordService.save("新增客户", null, cusCustomerOrClue.getId());
            } else {
                cusCcRecordService.save("新增线索", null, cusCustomerOrClue.getId());
            }

            // 手动录入 加入队列
            if ("0".equals(cusCustomerOrClue.getEntryType())) {
                // 加入队列
                CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
                if (null != inventory) {
                    Integer duration;
                    Integer recovery;
                    if ("0".equals(cusCustomerOrClue.getType())) {
                        duration = inventory.getClueDuration();
                        recovery = inventory.getClueRecovery();
                    } else {
                        duration = inventory.getCusDuration();
                        recovery = inventory.getCusRecovery();
                    }
                    recoveryToSeaHandle(cusCustomerOrClue, duration, recovery);
                }
            }

        } else {
            String isSeaByForm = form.getIsSea();
            CusCustomerOrClue byId = getById(form.getId());
            String isSeaBySql = byId.getIsSea();
            if ("0".equals(isSeaByForm) && "1".equals(isSeaBySql)) {
                throw new ServiceException("线索已回收至公海");
            }
            cusCustomerOrClue.setSeaId(byId.getSeaId());
            updateById(cusCustomerOrClue);
            // 联系人表
            cusCcContact.setId(cusCustomerOrClue.getMainContactId());
            cusCcContactMapper.updateById(cusCcContact);
            // 标签
            tagUpdateHandle(form.getTags(), cusCustomerOrClue.getId());
            // 操作记录
            if ("1".equals(cusCustomerOrClue.getType())) {
                cusCcRecordService.save("编辑客户", null, cusCustomerOrClue.getId());
            } else {
                cusCcRecordService.save("编辑线索", null, cusCustomerOrClue.getId());
            }
        }
    }

    /**
     * 回收公海队列处理
     *
     * @param cusCustomerOrClue 线索客户信息
     * @param duration          掉保时长
     * @param recovery          客户回收提醒提前天数
     */
    public void recoveryToSeaHandle(CusCustomerOrClue cusCustomerOrClue, Integer duration, Integer recovery) {
        if (ObjectUtil.isNotEmpty(duration) && duration > 0) {
            getBaseMapper().updateDuration(cusCustomerOrClue.getId(), duration);
            redisDelayQueueUtil.addDelayQueue(cusCustomerOrClue.getId(), duration, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
            if (ObjectUtil.isNotEmpty(recovery) && recovery > 0) {
                if (duration - recovery > 0) {
                    redisDelayQueueUtil.addDelayQueue(cusCustomerOrClue.getId(), duration - recovery, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                }
            }
        }
    }


    /**
     * 根据id删除
     *
     * @param id 主键id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        removeById(id);
        // 标签
        LambdaQueryWrapper<CusCcTags> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCcTags::getCcId, id);
        cusCcTagsService.remove(wrapper);
        // 跟进记录
        LambdaQueryWrapper<CusCcFollow> followWrapper = new LambdaQueryWrapper<>();
        followWrapper.eq(CusCcFollow::getCcId, id);
        cusCcFollowService.remove(followWrapper);
        // 联系人
        LambdaQueryWrapper<CusCcContact> contactWrapper = new LambdaQueryWrapper<>();
        contactWrapper.eq(CusCcContact::getCcId, id);
        cusCcContactMapper.delete(contactWrapper);
        // 商机
        LambdaQueryWrapper<CusCcBusiness> businessWrapper = new LambdaQueryWrapper<>();
        businessWrapper.eq(CusCcBusiness::getCcId, id);
        cusCcBusinessService.remove(businessWrapper);
        // 共享记录
        LambdaQueryWrapper<CusCcShare> shareWrapper = new LambdaQueryWrapper<>();
        shareWrapper.eq(CusCcShare::getCcId, id);
        cusCcShareService.remove(shareWrapper);
        // 删除队列
        redisDelayQueueUtil.removeDelayedQueue(id, RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(id, RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        getBaseMapper().updateDuration(id, 0);
    }

    /**
     * 修改标签
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTagsForm(UpdateTagsForm form) {
        CusCustomerOrClue byId = getById(form.getId());
        if ("1".equals(byId.getIsSea())) {
            throw new ServiceException("线索/客户已回收至公海");
        }
        // 标签更新
        tagUpdateHandle(form.getTags(), form.getId());
    }

    private void tagUpdateHandle(List<CusCcTags> tags, Long id) {
        LambdaQueryWrapper<CusCcTags> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCcTags::getCcId, id);
        cusCcTagsService.remove(wrapper);
        if (ObjectUtil.isNotEmpty(tags)) {
            tags.forEach(item -> item.setCcId(id));
            cusCcTagsService.saveBatch(tags);
        }
    }

    /**
     * 线索客户转让
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void ccChange(CcChangeForm form) {
        // 判断私海数量是否超过保有量上限
        isOver(form.getCurrentUserId(), form.getType());

        CusCustomerOrClue customerOrClue = getById(form.getId());
        if ("1".equals(customerOrClue.getIsSea())) {
            throw new ServiceException("线索/客户已回收至公海");
        }
        if (!isMemberOfSea(customerOrClue.getSeaId(), form.getCurrentUserId())) {
            throw new ServiceException("用户不是该公海成员");
        }
        customerOrClue.setCurrentUserId(form.getCurrentUserId());
        updateById(customerOrClue);
        if ("0".equals(form.getType())) {
            // 操作人
            String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
            SysUser sysUser = sysUserService.selectUserById(form.getCurrentUserId());
            // 转让给
            String nickName1 = sysUser.getNickName();
            CusCustomerOrClueVO detailById = getDetailById(form.getId());
            String remark = nickName + " 将线索【" + detailById.getContactName() + "】转让给现跟进人 " + nickName1;
            cusCcRecordService.save("转让线索", remark, customerOrClue.getId());
        } else {
            // 操作人
            String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
            SysUser sysUser = sysUserService.selectUserById(form.getCurrentUserId());
            // 转让给
            String nickName1 = sysUser.getNickName();
            CusCustomerOrClueVO detailById = getDetailById(form.getId());
            String remark = nickName + " 将客户【" + detailById.getCompanyName() + "】转让给现跟进人 " + nickName1;
            cusCcRecordService.save("转让客户", remark, customerOrClue.getId());
        }
    }

    /**
     * 线索共享
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void ccShare(CcShareForm form) {
        CusCustomerOrClue customerOrClue = getById(form.getId());
        if ("1".equals(customerOrClue.getIsSea())) {
            throw new ServiceException("线索已回收至公海");
        }
        List<Long> userIds = form.getUserIds();
        LambdaQueryWrapper<CusCcShare> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCcShare::getCcId, form.getId());
        cusCcShareService.remove(wrapper);

        if (null != userIds) {
            List<CusCcShare> list = new ArrayList<>();
            userIds.forEach(item -> {
                CusCcShare cusCcShare = new CusCcShare();
                cusCcShare.setCcId(form.getId());
                cusCcShare.setUserId(item);
                list.add(cusCcShare);
            });
            cusCcShareService.saveBatch(list);
        }
    }

    /**
     * 查询线索共享人id列表
     *
     * @param ccId 线索客户id
     */
    public List<Long> getUserIds(Long ccId) {
        return getBaseMapper().getUserIds(ccId);
    }

    /**
     * 线索客户回收公海
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void ccRecovery(CcRecoveryForm form) {
        CusCustomerOrClue customerOrClue = getById(form.getId());
        if ("1".equals(customerOrClue.getIsSea())) {
            throw new ServiceException("线索/客户已回收至公海");
        }
        if (!isMemberOfSea(form.getSeaId(), SecurityUtils.getUserId())) {
            throw new ServiceException("用户不是该公海成员");
        }
        customerOrClue.setSeaId(form.getSeaId());
        customerOrClue.setIsSea("1");
        customerOrClue.setReason(form.getReason());
        updateById(customerOrClue);
        if ("0".equals(form.getType())) {
            CusCustomerOrClueVO detailById = getDetailById(form.getId());
            String remark = "因" + form.getReason() + "将线索【" + detailById.getContactName() + "】回收至线索公海";
            cusCcRecordService.save("回收公海", remark, customerOrClue.getId());
            // 共享记录删除
            LambdaQueryWrapper<CusCcShare> shareWrapper = new LambdaQueryWrapper<>();
            shareWrapper.eq(CusCcShare::getCcId, form.getId());
            cusCcShareService.remove(shareWrapper);
        } else {
            CusCustomerOrClueVO detailById = getDetailById(form.getId());
            String remark = "因" + form.getReason() + "将客户【" + detailById.getCompanyName() + "】回收至客户公海";
            cusCcRecordService.save("回收公海", remark, customerOrClue.getId());
        }

        // 删除队列
        redisDelayQueueUtil.removeDelayedQueue(form.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(form.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        getBaseMapper().updateDuration(form.getId(), 0);
    }

    /**
     * 转为客户
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeToCustomer(CusCustomerOrClueForm form) {

        CusCustomerOrClue byId = getById(form.getId());
        if ("1".equals(byId.getIsSea())) {
            throw new ServiceException("线索已回收至公海");
        }
        isOver(SecurityUtils.getUserId(), "1");
        CusCustomerOrClue cusCustomerOrClue = CusCustomerOrClueConvert.INSTANCE.convert(form);
        cusCustomerOrClue.setSeaId(null);
        cusCustomerOrClue.setBecomeTime(LocalDateTime.now());
        cusCustomerOrClue.setType("1");// 类型修改为客户
        cusCustomerOrClue.setProtectionStartTime(LocalDateTime.now());
        updateById(cusCustomerOrClue);
        // 联系人表
        CusCcContact cusCcContact = new CusCcContact();
        cusCcContact.setId(cusCustomerOrClue.getMainContactId());
        cusCcContact.setCcId(cusCustomerOrClue.getId());
        cusCcContact.setContactName(form.getContactName());
        cusCcContact.setContactPhone(form.getContactPhone());
        cusCcContact.setBirthday(form.getBirthday());
        cusCcContact.setPost(form.getPost());
        cusCcContact.setWx(form.getWx());
        cusCcContact.setQq(form.getQq());
        cusCcContact.setSex(form.getSex());
        cusCcContact.setEmail(form.getEmail());
        cusCcContact.setSource("1");// 0-直接创建1-由线索转为客户联系人
        cusCcContact.setChangeTime(LocalDateTime.now());
        cusCcContactMapper.updateById(cusCcContact);
        // 标签
        tagUpdateHandle(form.getTags(), cusCustomerOrClue.getId());
        // 操作记录
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        String remark = nickName + " 将线索【" + form.getContactName() + "】转为客户【" + form.getCompanyName() + "】";
        cusCcRecordService.save("转为客户", remark, cusCustomerOrClue.getId());

        // 加入队列
        CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
        redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        getBaseMapper().updateDuration(cusCustomerOrClue.getId(), 0);
        if (null != inventory) {
            Integer duration = inventory.getCusDuration();
            Integer recovery = inventory.getCusRecovery();
            recoveryToSeaHandle(cusCustomerOrClue, duration, recovery);
        }
    }

    /**
     * 转为客户(关联已有客户)
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void clueToCustomer(ClueToCustomerForm form) {
        // 线索状态修改为2-线索关联已有客户
        CusCustomerOrClue clue = getById(form.getClueId());
        if ("1".equals(clue.getIsSea())) {
            throw new ServiceException("线索已回收至公海");
        }
        clue.setBecomeTime(LocalDateTime.now());
        clue.setType("2");
        updateById(clue);
        // 已有客户新增一条联系人记录
        CusCcContact cusCcContact = cusCcContactMapper.selectById(clue.getMainContactId());
        CusCustomerOrClue customer = getById(form.getCustomerId());
        cusCcContact.setId(null);
        cusCcContact.setCcId(customer.getId());
        cusCcContact.setSource("1");// 来源(统计用)0-直接创建1-由线索转为客户联系人
        cusCcContact.setChangeTime(LocalDateTime.now());
        cusCcContactMapper.insert(cusCcContact);

        updateById(customer);
        // 操作记录
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        String remark = nickName + " 将线索【" + cusCcContact.getContactName() + "】关联至客户【" + customer.getCompanyName() + "】";
        cusCcRecordService.save("关联客户", remark, customer.getId());

        // 加入队列
        Integer duration = 0;
        Integer recovery = 0;
        if (ObjectUtil.isNotEmpty(customer.getSeaId())) {
            CusSea byId = cusSeaService.getById(customer.getSeaId());
            if (null != byId) {
                duration = byId.getDuration();
                recovery = byId.getRecovery();
            }
        } else {
            CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
            if (null != inventory) {
                duration = inventory.getCusDuration();
                recovery = inventory.getCusRecovery();
            }
        }
        redisDelayQueueUtil.removeDelayedQueue(clue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(clue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        redisDelayQueueUtil.removeDelayedQueue(customer.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(customer.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        getBaseMapper().updateDuration(clue.getId(), 0);
        getBaseMapper().updateDuration(customer.getId(), 0);
        recoveryToSeaHandle(customer, duration, recovery);
    }

    /**
     * 线索公海列表
     *
     * @param query 查询条件
     */
    public IPage<CusClueInSeaListVO> clueInSeaList(CusClueInSeaListQuery query) {
        var page = new Page<CusCustomerOrClueListVO>(query.getPageNum(), query.getPageSize());
        IPage<CusClueInSeaListVO> res = getBaseMapper().clueInSeaList(query, page);
        List<CusClueInSeaListVO> records = res.getRecords();
        appealStatusHandle(records);
        Long userId = SecurityUtils.getUserId();
        records.forEach(item -> {
            List<String> tagsList = cusCcTagsService.getListByCcId(item.getId());
            item.setTagsName(String.join(",", tagsList));
            String rule = item.getRule();
            // 0-员工领取1-仅管理员分配2-员工领取+管理员分配
            if ("0".equals(rule) || "2".equals(rule)) {
                item.setIsGet(true);
            }
            if ("1".equals(rule) || "2".equals(rule)) {
                if (userId.equals(item.getManageId())) {
                    item.setIsDivide(true);
                }
            }
        });
        return res;
    }

    /**
     * 领取线索或客户
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void ccGet(CcGetForm form) {

        // 判断私海数量是否超过保有量上限
        isOver(SecurityUtils.getUserId(), form.getType());

        CusCustomerOrClue customerOrClue = getById(form.getId());
        if ("0".equals(customerOrClue.getIsSea())) {
            throw new ServiceException("线索/客户已被领取或分配");
        }
        if (!isMemberOfSea(customerOrClue.getSeaId(), SecurityUtils.getUserId())) {
            throw new ServiceException("用户不是该公海成员");
        }

        customerOrClue.setProtectionStartTime(LocalDateTime.now());
        customerOrClue.setCurrentUserId(SecurityUtils.getUserId());
        customerOrClue.setGetTime(LocalDateTime.now());
        customerOrClue.setIsSea("0");
        updateById(customerOrClue);
        // 操作记录
        if ("1".equals(form.getType())) {
            cusCcRecordService.save("领取客户", null, form.getId());
        } else {
            cusCcRecordService.save("领取线索", null, form.getId());
        }

        // 加入队列
        CusSea byId = cusSeaService.getById(customerOrClue.getSeaId());
        Integer duration = byId.getDuration();
        Integer recovery = byId.getRecovery();
        recoveryToSeaHandle(customerOrClue, duration, recovery);
    }

    /**
     * 判断私海数量是否超过保有量上限
     *
     * @param userId 用户id
     * @param type   类型 0-线索转让 1-客户转让
     */
    private void isOver(Long userId, String type) {
        CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
        if (ObjectUtil.isNull(inventory)) {
            return;
        }
        LambdaQueryWrapper<CusCustomerOrClue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCustomerOrClue::getCurrentUserId, userId);
        wrapper.eq(CusCustomerOrClue::getIsSea, "0");
        wrapper.eq(CusCustomerOrClue::getType, type);
        // 排除已转企业
        wrapper.ne(CusCustomerOrClue::getFollowStatus, "2");
        long count = count(wrapper);
        // 判断是否是管理员
        Integer clueNum;
        String clueStatus;
        Integer cusNum;
        String cusStatus;

        if (isAdmin(userId)) {
            clueNum = inventory.getClueAdminNum();
            clueStatus = inventory.getClueAdminStatus();
            cusNum = inventory.getCusAdminNum();
            cusStatus = inventory.getCusAdminStatus();
        } else {
            clueNum = inventory.getClueStaffNum();
            clueStatus = inventory.getClueStaffStatus();
            cusNum = inventory.getCusStaffNum();
            cusStatus = inventory.getCusStaffStatus();
        }

        // 线索
        if ("0".equals(type)) {
            // 启用状态需要校验
            if (ObjectUtil.notEqual(clueStatus, "1")) {
                return;
            }
            if (ObjectUtil.isNotEmpty(clueNum) && clueNum > 0) {
                if (count >= clueNum) {
                    throw new ServiceException("私海数量已达上限");
                }
            }
        }
        // 客户
        if ("1".equals(type)) {
            // 启用状态需要校验
            if (ObjectUtil.notEqual(cusStatus, "1")) {
                return;
            }
            if (ObjectUtil.isNotEmpty(cusNum) && cusNum > 0) {
                if (count >= cusNum) {
                    throw new ServiceException("私海数量已达上限");
                }
            }
        }

    }

    // 判断用户是不是公海管理员
    private boolean isAdmin(Long userId) {
        LambdaQueryWrapper<CusSea> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusSea::getManageId, userId);
        return cusSeaService.count(wrapper) > 0;
    }

    /**
     * 分配线索或客户
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void ccDivide(CcDivideForm form) {

        // 判断私海数量是否超过保有量上限
        isOver(form.getCurrentUserId(), form.getType());

        CusCustomerOrClue customerOrClue = getById(form.getId());
        if ("0".equals(customerOrClue.getIsSea())) {
            throw new ServiceException("线索/客户已被领取或分配");
        }
        if (!isMemberOfSea(customerOrClue.getSeaId(), form.getCurrentUserId())) {
            throw new ServiceException("用户不是该公海成员");
        }
        LocalDateTime now = LocalDateTime.now();
        customerOrClue.setCurrentUserId(form.getCurrentUserId());
        customerOrClue.setProtectionStartTime(now);
        customerOrClue.setGetTime(now);
        customerOrClue.setIsSea("0");
        updateById(customerOrClue);
        if ("0".equals(form.getType())) {
            // 操作人
            String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
            SysUser sysUser = sysUserService.selectUserById(form.getCurrentUserId());
            // 分配给
            String nickName1 = sysUser.getNickName();
            CusCustomerOrClueVO detailById = getDetailById(form.getId());
            String remark = nickName + " 将线索【" + detailById.getContactName() + "】分配给 " + nickName1 + " 进行跟进";
            cusCcRecordService.save("分配线索", remark, customerOrClue.getId());
        } else {
            // 操作人
            String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
            SysUser sysUser = sysUserService.selectUserById(form.getCurrentUserId());
            // 分配给
            String nickName1 = sysUser.getNickName();
            CusCustomerOrClueVO detailById = getDetailById(form.getId());
            String remark = nickName + " 将客户【" + detailById.getCompanyName() + "】分配给 " + nickName1 + " 进行跟进";
            cusCcRecordService.save("分配客户", remark, customerOrClue.getId());
        }

        // 加入队列
        CusSea byId = cusSeaService.getById(customerOrClue.getSeaId());
        Integer duration = byId.getDuration();
        Integer recovery = byId.getRecovery();
        recoveryToSeaHandle(customerOrClue, duration, recovery);
    }

    /**
     * 线索客户转移公海
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void ccTransfer(CcTransferForm form) {
        if (!isMemberOfSea(form.getSeaId(), SecurityUtils.getUserId())) {
            throw new ServiceException("用户不是该公海成员");
        }

        CusCustomerOrClue customerOrClue = getById(form.getId());
        CusSea org = cusSeaService.getById(customerOrClue.getSeaId());
        String orgName = org.getName();
        customerOrClue.setSeaId(form.getSeaId());
        updateById(customerOrClue);
        CusSea now = cusSeaService.getById(form.getSeaId());
        String nowName = now.getName();

        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        CusCustomerOrClueVO detailById = getDetailById(form.getId());
        String remark;
        if ("0".equals(form.getType())) {
            // 操作人
            remark = nickName + " 将线索【" + detailById.getContactName() + "】从 " + orgName + " 转移至 " + nowName;
        } else {
            // 操作人
            remark = nickName + " 将客户【" + detailById.getCompanyName() + "】从 " + orgName + " 转移至 " + nowName;
        }
        cusCcRecordService.save("转移公海", remark, customerOrClue.getId());
    }

    /**
     * 客户列表
     *
     * @param query 查询条件
     */
    public IPage<CusCustomerListVO> customerList(CusCustomerListQuery query) {
        var page = new Page<CusCustomerListVO>(query.getPageNum(), query.getPageSize());
        query.setUserId(SecurityUtils.getUserId());
        IPage<CusCustomerListVO> res = getBaseMapper().customerList(query, page);
        List<CusCustomerListVO> records = res.getRecords();
        appealStatusHandle(records);
        records.forEach(item -> {
            List<String> tagsList = cusCcTagsService.getListByCcId(item.getId());
            item.setTagsName(String.join(",", tagsList));
            // 商机数量
            item.setWinNum(cusCcBusinessService.getWinNum(item.getId()));
            item.setLoseNum(cusCcBusinessService.getLoseNum(item.getId()));
            item.setOtherNum(cusCcBusinessService.getOtherNum(item.getId()));
        });
        return res;
    }

    /**
     * 编辑附加信息
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOtherInfo(CusCustomerOtherInfoForm form) {
        CusCustomerOrClue byId = getById(form.getId());
        if ("1".equals(byId.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }
        byId.setLegalIdCardFront(form.getLegalIdCardFront());
        byId.setLegalIdCardBack(form.getLegalIdCardBack());
        byId.setLegalPhone(form.getLegalPhone());
        byId.setSupervisorIdCardFront(form.getSupervisorIdCardFront());
        byId.setSupervisorIdCardBack(form.getSupervisorIdCardBack());
        byId.setSupervisorPhone(form.getSupervisorPhone());
        byId.setShare(form.getShare());
        updateById(byId);

        // 法人监事身份证附件
        commonBizFileService.fileSaveHandler(form.getLegalIdentityFileList(), form.getId(), BizType.CLUE_LEGAL_IDENTITY);
        commonBizFileService.fileSaveHandler(form.getSupervisorIdentityFileList(), form.getId(), BizType.CLUE_SUPERVISOR_IDENTITY);
        commonBizFileService.fileSaveHandler(form.getOtherDocumentFileList(), form.getId(), BizType.CLUE_OTHER_DOCUMENT);

        // 股东信息
        cusCcShareholderInfoService.saveOrUpdateBatch(form.getShareholderInfoList(), form.getId());
    }

    /**
     * 查询附加信息
     *
     * @param id id
     */
    public CusCustomerOtherInfoForm getOtherInfo(Long id) {
        CusCustomerOtherInfoForm res = getBaseMapper().getOtherInfo(id);
        res.setLegalIdentityFileList(commonBizFileService.selectByMainIdAndBizType(id, BizType.CLUE_LEGAL_IDENTITY));
        res.setSupervisorIdentityFileList(commonBizFileService.selectByMainIdAndBizType(id, BizType.CLUE_SUPERVISOR_IDENTITY));
        res.setOtherDocumentFileList(commonBizFileService.selectByMainIdAndBizType(id, BizType.CLUE_OTHER_DOCUMENT));
        // 股东信息
        res.setShareholderInfoList(cusCcShareholderInfoService.getListByCcId(id));
        return res;
    }

    /**
     * 客户建档
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public R<Long> establish(EstablishForm form) {
        CusCustomerOrClue byId = getById(form.getId());
        if ("1".equals(byId.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }

        CusCcBusinessForm businessForm = form.getForm();
        if (ObjectUtil.isEmpty(businessForm)) {
            throw new ServiceException("商机信息表单不能为空");
        }
        String customerName = form.getCustomerName();
        LambdaQueryWrapper<CustomerInformation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerInformation::getCustomerName, customerName);
        if (customerInformationService.count(wrapper) > 0) {
            return R.ok(null, "该企业档案已存在，是否直接关联该企业？");
        }
        // 新增企业档案
        CustomerInformationForm informationForm = new CustomerInformationForm();
        BeanUtils.copyBeanProp(informationForm, form);
        // 主联系人信息
        CusCcContact cusCcContact = cusCcContactMapper.selectById(byId.getMainContactId());
        informationForm.setPost(cusCcContact.getPost());
        informationForm.setBirthday(cusCcContact.getBirthday());
        informationForm.setIsLeader(cusCcContact.getIsLeader());
        informationForm.setIsOften(cusCcContact.getIsOften());
        informationForm.setWx(cusCcContact.getWx());
        informationForm.setQq(cusCcContact.getQq());
        informationForm.setEmail(cusCcContact.getEmail());
        informationForm.setSex(cusCcContact.getSex());

        Long customerId = customerInformationService.addOrUpdate(informationForm);
        // 更新客户信息 办证资料等
        byId.setCustomerId(customerId);
        byId.setLegalIdCardFront(form.getLegalIdCardFront());
        byId.setLegalIdCardBack(form.getLegalIdCardBack());
        byId.setLegalPhone(form.getLegalPhone());
        byId.setSupervisorIdCardFront(form.getSupervisorIdCardFront());
        byId.setSupervisorIdCardBack(form.getSupervisorIdCardBack());
        byId.setSupervisorPhone(form.getSupervisorPhone());
        byId.setShare(form.getShare());
        updateById(byId);
        // 法人监事身份证附件
        commonBizFileService.fileSaveHandler(form.getLegalIdentityFileList(), form.getId(), BizType.CLUE_LEGAL_IDENTITY);
        commonBizFileService.fileSaveHandler(form.getSupervisorIdentityFileList(), form.getId(), BizType.CLUE_SUPERVISOR_IDENTITY);
        commonBizFileService.fileSaveHandler(form.getOtherDocumentFileList(), form.getId(), BizType.CLUE_OTHER_DOCUMENT);
        // 股东信息
        cusCcShareholderInfoService.saveOrUpdateBatch(form.getShareholderInfoList(), form.getId());

        // 其他联系人信息
        List<CusCcContact> list = cusCcContactMapper.getListNotMain(byId.getId(), byId.getMainContactId());
        list.forEach(item -> {
            CustomerContact customerContact = new CustomerContact();
            customerContact.setName(item.getContactName());
            customerContact.setPhone(item.getContactPhone());
            customerContact.setWechat(item.getWx());
            BeanUtil.copyProperties(form, customerContact);
            customerContact.setCiId(customerId);
            customerContactMapper.insert(customerContact);
        });

        // 新增或编辑商机
        cusCcBusinessService.establishSaveOrUpdate(businessForm);
        return R.ok(customerId);
    }

    /**
     * 客户建档(关联已有客户)
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public R<Long> associationEstablish(EstablishForm form) {
        CusCustomerOrClue byId = getById(form.getId());
        if ("1".equals(byId.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }
        CusCcBusinessForm businessForm = form.getForm();
        if (ObjectUtil.isEmpty(businessForm)) {
            throw new ServiceException("商机信息表单不能为空");
        }
        String customerName = form.getCustomerName();
        LambdaQueryWrapper<CustomerInformation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerInformation::getCustomerName, customerName);
        wrapper.last("limit 1");
        CustomerInformation one = customerInformationService.getOne(wrapper);
        Long customerId = one.getCustomerId();

        // 更新客户信息 办证资料等
        byId.setCustomerId(customerId);
        byId.setLegalIdCardFront(form.getLegalIdCardFront());
        byId.setLegalIdCardBack(form.getLegalIdCardBack());
        byId.setLegalPhone(form.getLegalPhone());
        byId.setSupervisorIdCardFront(form.getSupervisorIdCardFront());
        byId.setSupervisorIdCardBack(form.getSupervisorIdCardBack());
        byId.setSupervisorPhone(form.getSupervisorPhone());
        byId.setShare(form.getShare());
        updateById(byId);
        // 法人监事身份证附件
        commonBizFileService.fileSaveHandler(form.getLegalIdentityFileList(), form.getId(), BizType.CLUE_LEGAL_IDENTITY);
        commonBizFileService.fileSaveHandler(form.getSupervisorIdentityFileList(), form.getId(), BizType.CLUE_SUPERVISOR_IDENTITY);
        commonBizFileService.fileSaveHandler(form.getOtherDocumentFileList(), form.getId(), BizType.CLUE_OTHER_DOCUMENT);
        // 股东信息
        cusCcShareholderInfoService.saveOrUpdateBatch(form.getShareholderInfoList(), form.getId());

        // 联系人信息
        List<CusCcContactListVO> list = cusCcContactMapper.listByCcId(byId.getId());
        list.forEach(item -> {
            CustomerContact customerContact = new CustomerContact();
            customerContact.setName(item.getContactName());
            customerContact.setPhone(item.getContactPhone());
            customerContact.setWechat(item.getWx());
            BeanUtil.copyProperties(form, customerContact);
            customerContact.setCiId(customerId);
            customerContactMapper.insert(customerContact);
        });

        // 新增或编辑商机
        cusCcBusinessService.establishSaveOrUpdate(businessForm);
        return R.ok(customerId);
    }

    /**
     * 客户公海列表
     *
     * @param query 查询条件
     */
    public IPage<CusCustomerInSeaListVO> customerInSeaList(CusCustomerListQuery query) {
        var page = new Page<CusCustomerListVO>(query.getPageNum(), query.getPageSize());
        IPage<CusCustomerInSeaListVO> res = getBaseMapper().customerInSeaList(query, page);
        List<CusCustomerInSeaListVO> records = res.getRecords();
        appealStatusHandle(records);
        Long userId = SecurityUtils.getUserId();
        records.forEach(item -> {
            List<String> tagsList = cusCcTagsService.getListByCcId(item.getId());
            item.setTagsName(String.join(",", tagsList));
            String rule = item.getRule();
            // 0-员工领取1-仅管理员分配2-员工领取+管理员分配
            if ("0".equals(rule) || "2".equals(rule)) {
                item.setIsGet(true);
            }
            if ("1".equals(rule) || "2".equals(rule)) {
                if (userId.equals(item.getManageId())) {
                    item.setIsDivide(true);
                }
            }
        });
        return res;
    }

    /**
     * 所有私海线索
     *
     * @param query 查询条件
     */
    @DataScope(deptAlias = "d", userAlias = "su")
    public IPage<CusCustomerOrClueListVO> allClueList(AllCusCustomerOrClueQuery query) {
        var page = new Page<CusCustomerOrClueListVO>(query.getPageNum(), query.getPageSize());
        IPage<CusCustomerOrClueListVO> res = getBaseMapper().allClueList(query, page);
        List<CusCustomerOrClueListVO> records = res.getRecords();
        appealStatusHandle(records);
        records.forEach(item -> {
            List<String> tagsList = cusCcTagsService.getListByCcId(item.getId());
            item.setTagsName(String.join(",", tagsList));
        });
        return res;
    }

    /**
     * 所有私海客户列表
     *
     * @param query 查询条件
     */
    @DataScope(deptAlias = "d", userAlias = "su")
    public IPage<CusCustomerListVO> allCustomerList(AllCusCustomerListQuery query) {
        var page = new Page<CusCustomerListVO>(query.getPageNum(), query.getPageSize());
        query.setUserId(SecurityUtils.getUserId());
        IPage<CusCustomerListVO> res = getBaseMapper().allCustomerList(query, page);
        List<CusCustomerListVO> records = res.getRecords();
        appealStatusHandle(records);
        records.forEach(item -> {
            List<String> tagsList = cusCcTagsService.getListByCcId(item.getId());
            item.setTagsName(String.join(",", tagsList));
            // 商机数量
            item.setWinNum(cusCcBusinessService.getWinNum(item.getId()));
            item.setLoseNum(cusCcBusinessService.getLoseNum(item.getId()));
            item.setOtherNum(cusCcBusinessService.getOtherNum(item.getId()));
        });
        return res;
    }

    /**
     * 判断当前用户是否是所选公海成员
     *
     * @param seaId  公海id
     * @param userId 用户id
     */
    public Boolean isMemberOfSea(Long seaId, Long userId) {
        CusSeaVO detailById = cusSeaService.getDetailById(seaId);
        if (null == detailById) {
            return true;
        }
        List<Integer> deptIds = detailById.getDeptIds();
        if (ObjectUtil.isEmpty(deptIds)) {
            return true;
        }
        SysUser sysUser = sysUserService.selectUserById(userId);
        if (sysUser.isAdmin()) {
            return true;
        }
        Integer deptId = sysUser.getDeptId().intValue();
        return deptIds.contains(deptId);
    }

    /**
     * 线索申诉开始
     *
     * @param clueId 线索id
     * @param remark 备注
     */
    public void clueAppealStartHandle(Long clueId, String remark) {
        // 查询线索
        var clue = getById(clueId);
        // 修改线索状态
        clue.setAppealFlag(true);
        updateById(clue);
        // 记录操作
        cusCcRecordService.save("线索申述", remark, clueId);
        // 不再计算掉保时间（不会自动掉回公海）
        getBaseMapper().updateDuration(clueId, 0);
        // 删除队列
        redisDelayQueueUtil.removeDelayedQueue(clueId, RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(clueId, RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
    }

    /**
     * 申诉结束
     *
     * @param clueId  线索id
     * @param success 是否成功
     */
    public void clueAppealEndHandle(Long clueId, Boolean success) {
        // 查询线索
        var clue = getById(clueId);
        // 修改线索状态
        clue.setAppealFlag(false);
        clue.setProtectionStartTime(LocalDateTime.now());
        updateById(clue);
        // 记录操作
        String recordName = "申述失败";
        if (success) {
            recordName = "申述成功";
        }
        cusCcRecordService.save(recordName, null, clueId);
        // 重新计算掉保时间
        clueRecoveryHandle(clue);
    }

    /**
     * 线索回收 延迟队列计算
     *
     * @param customerOrClue 线索
     */
    public void clueRecoveryHandle(CusCustomerOrClue customerOrClue) {
        Integer duration = 0;
        Integer recovery = 0;
        CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
        if (ObjectUtil.isNotNull(inventory)) {
            if ("0".equals(customerOrClue.getType())) {
                duration = inventory.getClueDuration();
                recovery = inventory.getClueRecovery();
            } else {
                duration = inventory.getCusDuration();
                recovery = inventory.getCusRecovery();
            }
        }
        if (ObjectUtil.isNotNull(customerOrClue.getSeaId())) {
            CusSea cusSea = cusSeaService.getById(customerOrClue.getSeaId());
            if (ObjectUtil.isNotNull(cusSea)) {// 公海存在
                duration = cusSea.getDuration();
                recovery = cusSea.getRecovery();
            }
        }
        redisDelayQueueUtil.removeDelayedQueue(customerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(customerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        getBaseMapper().updateDuration(customerOrClue.getId(), 0);
        if (ObjectUtil.isNotEmpty(duration) && duration > 0) {
            getBaseMapper().updateDuration(customerOrClue.getId(), duration);
            redisDelayQueueUtil.addDelayQueue(customerOrClue.getId(), duration, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
            if (ObjectUtil.isNotEmpty(recovery) && recovery > 0) {
                if (duration - recovery > 0) {
                    redisDelayQueueUtil.addDelayQueue(customerOrClue.getId(), duration - recovery, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                }
            }
        }
    }

    /**
     * 申述状态维护
     * 申述状态是线索表中的另外一个字段
     * 申述完成还要还原为以前的状态
     * 故特殊处理
     *
     * @param recordList 结果列表
     */
    public void appealStatusHandle(List<?> recordList) {
        if (ObjectUtil.isEmpty(recordList)) {
            return;
        }
        for (Object record : recordList) {
            if (record instanceof CusCustomerOrClueListVO) {
                handleCustomer((CusCustomerOrClueListVO) record);
            } else if (record instanceof CusCustomerListVO) {
                handleCustomer((CusCustomerListVO) record);
            } else if (record instanceof CusClueInSeaListVO) {
                handleCustomer((CusClueInSeaListVO) record);
            }
        }
    }

    private void handleCustomer(CusCustomerOrClueListVO record) {
        if (ObjectUtil.isNotNull(record.getAppealFlag()) && record.getAppealFlag()) {
            record.setFollowStatus("3");
        }
    }

    private void handleCustomer(CusCustomerListVO record) {
        if (ObjectUtil.isNotNull(record.getAppealFlag()) && record.getAppealFlag()) {
            record.setFollowStatus("3");
        }
    }

    private void handleCustomer(CusClueInSeaListVO record) {
        if (ObjectUtil.isNotNull(record.getAppealFlag()) && record.getAppealFlag()) {
            record.setFollowStatus("3");
        }
    }

    private void handleCustomer(CusCustomerOrClueVO record) {
        if (ObjectUtil.isNotNull(record.getAppealFlag()) && record.getAppealFlag()) {
            record.setFollowStatus("3");
        }
    }

    /**
     * 线索管理页面头部统计
     */
    public ClueHeaderStatisticVO clueHeaderStatisticVO() {
        ClueHeaderStatisticVO vo = new ClueHeaderStatisticVO();
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        LambdaQueryWrapper<CusCustomerOrClue> wrapper = new LambdaQueryWrapper<>();
        // 总待分配数
        wrapper.eq(CusCustomerOrClue::getCreateBy, nickName);
        wrapper.eq(CusCustomerOrClue::getIsSea, "1");
        vo.setTotalCreateWaitAssign(count(wrapper));
        // 本日待分配
        LocalDateTime now = LocalDateTime.now();
        wrapper.between(CusCustomerOrClue::getCreateTime, LocalDateTimeUtil.beginOfDay(now), LocalDateTimeUtil.endOfDay(now));
        vo.setTodayCreateWaitAssign(count(wrapper));

        wrapper.clear();
        // 总创建数
        wrapper.eq(CusCustomerOrClue::getCreateBy, nickName);
        vo.setTotalCreate(count(wrapper));
        // 今日创建数
        wrapper.between(CusCustomerOrClue::getCreateTime, LocalDateTimeUtil.beginOfDay(now), LocalDateTimeUtil.endOfDay(now));
        vo.setTodayCreate(count(wrapper));
        return vo;
    }
}
