package com.jri.biz.domain.request.suggestion;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 企业用户建议查询类
 *
 * <AUTHOR>
 * @since 2024-01-25
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="企业用户建议查询对象")
public class UserSuggestionQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("处理状态 pending - 待处理, complete - 已处理")
    private String handleStatus;

    @ApiModelProperty("我的建议标志")
    private Boolean mySuggestionFlag;

    @ApiModelProperty("用户id")
    private Long userId;

}
