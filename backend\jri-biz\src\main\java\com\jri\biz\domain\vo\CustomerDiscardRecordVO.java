package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-06-01
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerDiscardRecordVO视图对象")
public class CustomerDiscardRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    
        private Long id;
        private Long ciId;
        private Integer discard;
        private String discardReason;


}