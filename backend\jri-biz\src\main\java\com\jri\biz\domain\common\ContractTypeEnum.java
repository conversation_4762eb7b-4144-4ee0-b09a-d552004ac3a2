package com.jri.biz.domain.common;

public enum ContractTypeEnum {

    SPRING("0", "记账合同"), SUMMER("1", "一次性合同"), AUTUMN("2", "地址服务协议合同");

    private String value;
    private String lab;

    private ContractTypeEnum(String value, String lab){
        this.value = value;
        this.lab = lab;
    }

    public static String getContractTypeEnumByValue(String value){
        for(ContractTypeEnum s : ContractTypeEnum.values()){
            if(s.value.equals(value)){
                return s.lab;
            }
        }
        return null;
    }
}
