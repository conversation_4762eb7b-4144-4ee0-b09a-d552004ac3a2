import request from '@/utils/request'

// "name": "任务完成",
// "method": "post",
// "path": "/riskCustomer/audit/nodeComplete",
export const riskCustomerAuditNodeComplete = params => {
  return request({
    url: '/riskCustomer/audit/nodeComplete',
    method: 'post',
    data: params
  })
}

// "name": "任务保存",
// "method": "post",
// "path": "/riskCustomer/audit/nodeSave",
export const riskCustomerAuditNodeSave = params => {
  return request({
    url: '/riskCustomer/audit/nodeSave',
    method: 'post',
    data: params
  })
}

// "name": "审核列表查询",
// "method": "get",
// "path": "/riskCustomer/auditList",
export const riskCustomerAuditList = params => {
  return request({
    url: '/riskCustomer/auditList',
    method: 'get',
    params
  })
}

// "name": "详情",
// "method": "get",
// "path": "/riskCustomer/getById",
export const riskCustomerGetById = params => {
  return request({
    url: '/riskCustomer/getById',
    method: 'get',
    params
  })
}

// "name": "列表查询",
// "method": "get",
// "path": "/riskCustomer/list",
export const riskCustomerList = params => {
  return request({
    url: '/riskCustomer/list',
    method: 'get',
    params
  })
}

export const riskCustomerListExport = params => {
  return request({
    url: '/riskCustomer/listExport',
    method: 'post',
    data: params
  })
}

// "name": "流失清理列表查询",
// "method": "get",
// "path": "/riskCustomer/loseHandleList",
export const riskCustomerLoseHandleList = params => {
  return request({
    url: '/riskCustomer/loseHandleList',
    method: 'get',
    params
  })
}

// "name": "正式停账完成",
// "method": "post",
// "path": "/riskCustomer/loseHandle/accountCloseComplete",
export const riskCustomerLoseHandleAccountCloseComplete = params => {
  return request({
    url: '/riskCustomer/loseHandle/accountCloseComplete',
    method: 'post',
    data: params
  })
}

// "name": "正式停账保存",
// "method": "post",
// "path": "/riskCustomer/loseHandle/accountCloseSave",
export const riskCustomerLoseHandleAccountCloseSave = params => {
  return request({
    url: '/riskCustomer/loseHandle/accountCloseSave',
    method: 'post',
    data: params
  })
}

// "name": "出纳确认完成",
// "method": "post",
// "path": "/riskCustomer/loseHandle/cashierConfirmComplete",
export const riskCustomerLoseHandleCashierConfirmComplete = params => {
  return request({
    url: '/riskCustomer/loseHandle/cashierConfirmComplete',
    method: 'post',
    data: params
  })
}

// "name": "出纳确认保存",
// "method": "post",
// "path": "/riskCustomer/loseHandle/cashierConfirmSave",
export const riskCustomerLoseHandleCashierConfirmSave = params => {
  return request({
    url: '/riskCustomer/loseHandle/cashierConfirmSave',
    method: 'post',
    data: params
  })
}

// "name": "工作清理完成",
// "method": "post",
// "path": "/riskCustomer/loseHandle/workClearComplete",
export const riskCustomerLoseHandleWorkClearComplete = params => {
  return request({
    url: '/riskCustomer/loseHandle/workClearComplete',
    method: 'post',
    data: params
  })
}

// "name": "工作清理保存",
// "method": "post",
// "path": "/riskCustomer/loseHandle/workClearSave",
export const riskCustomerLoseHandleWorkClearSave = params => {
  return request({
    url: '/riskCustomer/loseHandle/workClearSave',
    method: 'post',
    data: params
  })
}

// "name": "保存数据",
// "method": "post",
// "path": "/riskCustomer/save",
export const riskCustomerSave = params => {
  return request({
    url: '/riskCustomer/save',
    method: 'post',
    data: params
  })
}

// "name": "更新数据",
// "method": "post",
// "path": "/riskCustomer/update",
export const riskCustomerUpdate = params => {
  return request({
    url: '/riskCustomer/update',
    method: 'post',
    data: params
  })
}

// "name": "回退",
// "method": "post",
// "path": "/riskCustomer/rollback",
export const riskCustomerRollback = params => {
  return request({
    url: '/riskCustomer/rollback?nodeId=' + params.nodeId,
    method: 'post'
  })
}
