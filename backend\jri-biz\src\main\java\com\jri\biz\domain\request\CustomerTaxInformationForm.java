package com.jri.biz.domain.request;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.jri.biz.domain.entity.CustomerTaxRebateIdentified;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Data
@NoArgsConstructor
@Setter
@Getter
@ApiModel(value = "税务信息表单请求对象")
public class CustomerTaxInformationForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 税务信息表id
     */
    private Long id;

    /**
     * 客户主表id
     */
    private Long ciId;


    /**
     * 身份证号码
     */
    private String identityNumber;

    /**
     * 登记税务机关
     */
    private String taxRegistrationOrgan;

    /**
     * 税务机关地址
     */
    private String taxOrganAddress;

    /**
     * 税率登记
     */
    private String rateRegistration;

    /**
     * 法人是否实名登记
     */
    private Boolean legalRealNameFlag;

    /**
     * 办税员实名认证
     */
    private Boolean taxRealNameFlag;

    /**
     * 个体户核定
     */
    private Boolean individualCheckFlag;

    /**
     * 是否网上税务局注册
     */
    private Boolean onlineRevenueRegistrationFlag;

    /**
     * 预留手机号
     */
    private String reservedPhoneNumber;

    /**
     * 税企银三方协议
     */
    private Boolean tripleAgreementFlag;

    /**
     * 所得税认定方式
     */
    private Boolean identificationMethodFlag;

    /**
     * 证书账号
     */
    private String certificateAccount;

    /**
     * 证书密码
     */
    private String certificatePassword;

    /**
     * 开票盘
     */
    private String drawingSheetFlag;

    /**
     * 发票
     */
    private String invoiceFlag;

    /**
     * 发票章
     */
    private String invoiceSealFlag;

    /**
     * 开票盘归属部门
     */
    private String drawingSheetDept;

    /**
     * 发票归属部门
     */
    private String invoiceDept;

    /**
     * 发票章归属部门
     */
    private String invoiceSealDept;

    /**
     * 发票额度
     */
    private String invoiceLimit;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 开票盘类型
     */
    private String drawingSheetType;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
//    @TableField(fill = FieldFill.INSERT)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
//    @TableField(fill = FieldFill.INSERT_UPDATE)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime updateTime;

    @ApiModelProperty("税务资料上传")
    private List<CommonBizFile> taxInformationFileList;

    @ApiModelProperty("客户开票资料")
    private List<CommonBizFile> customerBillingInformationFileList;

    @ApiModelProperty("一般纳税人认定表")
    private List<CommonBizFile> taxpayerIdentificationFormFileList;

    @ApiModelProperty("客户首次确认")
    private List<CommonBizFile> customerFirstConfirmList;

    @ApiModelProperty("出口退税认定")
    private CustomerTaxRebateIdentified customerTaxRebateIdentified;

    @ApiModelProperty("出口明细表")
    private List<CustomerTaxExportDetail> customerTaxExportDetail;

    /**
     * 自然人密码
     */
    private String naturalPersonPassword;
}
