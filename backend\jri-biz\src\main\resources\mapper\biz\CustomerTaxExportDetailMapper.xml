<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerTaxExportDetailMapper">
    <delete id="deleteByIds">
        update customer_tax_export_detail set is_deleted=1,update_by =#{updateBy} where id in
        <foreach item="id" collection="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByMainId" >
         delete from customer_tax_export_detail where main_id= #{mainId}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerTaxExportDetailListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerTaxExportDetailVO">

    </select>

    <select id="selectByMainId" resultType="com.jri.biz.domain.entity.CustomerTaxExportDetail" >
        select * from customer_tax_export_detail where is_deleted=0 and main_id=#{id}
    </select>
</mapper>
