package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * 产品活动价查询类
 *
 * <AUTHOR>
 * @since 2023-11-02
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="产品活动价查询对象")
public class BusinessProductActivityQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品id")
    @NotNull(message = "产品id不能为空")
    private Long productId;
}
