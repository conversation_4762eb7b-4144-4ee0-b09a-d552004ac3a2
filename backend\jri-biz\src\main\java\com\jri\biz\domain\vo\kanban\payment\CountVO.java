package com.jri.biz.domain.vo.kanban.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/9/4 14:19
 */
@Getter
@Setter
@ApiModel(value = "YoYPrice", description = "数量VO")
public class CountVO {

    @ApiModelProperty("当前")
    private BigDecimal count;

    @ApiModelProperty("环比")
    private BigDecimal moMCount;

    @ApiModelProperty("同比")
    private BigDecimal yoYCount;

}
