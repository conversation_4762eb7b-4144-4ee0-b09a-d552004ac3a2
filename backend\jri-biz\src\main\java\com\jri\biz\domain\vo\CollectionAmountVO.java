package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CollectionAmountVO视图对象")
public class CollectionAmountVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("本年度")
    private BigDecimal nowAmount;

    @ApiModelProperty("上一年度")
    private BigDecimal lastAmount;
}
