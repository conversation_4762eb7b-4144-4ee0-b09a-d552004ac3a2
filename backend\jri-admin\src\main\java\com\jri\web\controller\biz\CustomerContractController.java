package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.request.*;
import com.jri.biz.domain.vo.ContractListVO;
import com.jri.biz.domain.vo.CustomerContractListVO;
import com.jri.biz.domain.vo.CustomerContractVO;
import com.jri.biz.domain.vo.ReviewCustomerContractListVO;
import com.jri.biz.service.CustomerContractService;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.ProgressService;
import com.jri.common.core.domain.R;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerContract")
@Api(tags = "客户合同信息")
public class CustomerContractController {
    @Resource
    private CustomerContractService customerContractService;

    @Resource
    private ProgressService progressService;

    @Resource
    private ExportService exportService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerContractListVO>> listPage(CustomerContractQuery query) {
        return R.ok(customerContractService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerContractVO> getDetailById(@RequestParam("id") Long id,
                                               @RequestParam(value = "showFlag", required = false) Boolean showFlag) {
        return R.ok(customerContractService.getDetailById(id, showFlag));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Long> save(@RequestBody @Valid CustomerContractForm form) {
        return R.ok(customerContractService.add(form));
    }

    @PostMapping("/saveOrUpdateBatch")
    @ApiOperation("批量保存数据")
    public R<Void> saveOrUpdateBatch(@RequestBody @Valid List<CustomerContractForm> list) {
        customerContractService.saveOrUpdateBatch(list);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerContractService.deleteById(id));
    }

    @DeleteMapping("/deleteByIds")
    @ApiOperation("批量删除")
    public R<Void> deleteByIds(@RequestParam("ids") List<Long> ids) {
        customerContractService.deleteByIds(ids);
        return R.ok();
    }

    @GetMapping("/listByCiId")
    @ApiOperation("根据客户id列表查询")
    public R<IPage<ContractListVO>> listByCiId(CustomerContractQuery query) {
        return R.ok(customerContractService.listByCiId(query));
    }

    @Deprecated
    @GetMapping("/listMyCreate")
    @ApiOperation("我提交的")
    public R<IPage<ReviewCustomerContractListVO>> listMyCreate(ReviewCustomerContractQuery query) {
        return R.ok(customerContractService.listMyCreate(query));
    }

    @GetMapping("/auditList")
    @ApiOperation("审核列表")
    public R<IPage<ReviewCustomerContractListVO>> auditList(ReviewCustomerContractQuery query) {
        return R.ok(customerContractService.auditList(query));
    }

    @PostMapping("/auditListExport")
    @ApiOperation("审核列表")
    public R<Long> auditListExport(@RequestBody ReviewCustomerContractQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "合同评审.xlsx");
        var id = progressService.create(ProgressType.CONTRACT_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(fileName,
                () -> customerContractService.auditList(query).getRecords(),
                "合同评审",
                ReviewCustomerContractListVO.class,
                id);
        return R.ok(id);
    }

    @GetMapping("/listMyAudit")
    @ApiOperation("由我审批")
    public R<IPage<ReviewCustomerContractListVO>> listMyAudit(ReviewCustomerContractQuery query) {
        return R.ok(customerContractService.listMyAudit(query));
    }

    @PostMapping("/listMyAuditExport")
    @ApiOperation("由我审批导出")
    public R<Long> listMyAuditExport(@RequestBody ReviewCustomerContractQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "合同评审.xlsx");
        var id = progressService.create(ProgressType.CONTRACT_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        query.setPageNum(1);
        query.setPageSize(-1);
        exportService.exportTemplate(fileName,
                () -> customerContractService.listMyAudit(query).getRecords(),
                "合同评审",
                ReviewCustomerContractListVO.class,
                id);
        return R.ok(id);
    }

    @GetMapping("/getByIdCheck")
    @ApiOperation("详情(查看权限校验)")
    public R<CustomerContractVO> getByIdCheck(@RequestParam("id") Long id) {
        ContractDetailQuery query = new ContractDetailQuery();
        query.setId(id);
        return customerContractService.getByIdCheck(query);
    }

    @GetMapping("/getByIdCheckChange")
    @ApiOperation("详情(查看权限校验,不校验是否借阅)")
    public R<CustomerContractVO> getByIdCheckChange(@RequestParam("id") Long id) {
        ContractDetailQuery query = new ContractDetailQuery();
        query.setId(id);
        return customerContractService.getByIdCheckChange(query);
    }

    @PostMapping("/addFile")
    @ApiOperation("添加合同附件")
    public R<Void> addFile(@RequestBody @Valid ContractAddFileForm form) {
        customerContractService.addFile(form);
        return R.ok();
    }

    @PostMapping("/changeToFormal")
    @ApiOperation("转正式合同")
    public R<Long> changeToFormal(@RequestBody @Valid CustomerContractForm form) {
        return R.ok(customerContractService.changeToFormal(form));
    }

    @PostMapping("/updateDocumentNo")
    @ApiOperation("编辑归档号")
    public R<Void> updateDocumentNo(@RequestBody @Valid DocumentNoForm form) {
        customerContractService.updateDocumentNo(form);
        return R.ok();
    }

    @PostMapping("/isExistBill")
    @ApiOperation("校验合同是否存在关联账单")
    public R<Boolean> isExistBill(@RequestParam("contractId") Long contractId) {
        return R.ok(customerContractService.isExistBill(contractId));
    }

    @PostMapping("/terminateContract")
    @ApiOperation("终止合同")
    public R<Void> terminateContract(@RequestParam("contractId") Long contractId) {
        customerContractService.terminateContract(contractId);
        return R.ok();
    }
}

