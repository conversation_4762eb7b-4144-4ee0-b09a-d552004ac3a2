package com.jri.web.controller.biz;

import com.alibaba.fastjson2.JSON;
import com.jri.biz.domain.request.CustomerContactForm;
import com.jri.framework.security.context.AuthenticationContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2023/6/1 13:30
 */
@SpringBootTest
class CustomerContactControllerTest {
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private CustomerContactController controller;

    @BeforeEach
    void before() {
        var authenticationToken = new UsernamePasswordAuthenticationToken("admin", "admin123");
        AuthenticationContextHolder.setContext(authenticationToken);
        var authentication = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }

    @Test
    void listPage() throws Exception {
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/customerContact/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .param("ciId", "5");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void saveBatch() throws Exception {
        var list = new ArrayList<CustomerContactForm>();
        var a1 = new CustomerContactForm();
        a1.setName("a");
        var a2 = new CustomerContactForm();
        a2.setCiId(2L);
        a2.setName("c");
        list.add(a1);
        list.add(a2);

        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .post("/customerContact/saveBatch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(list));
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }
}
