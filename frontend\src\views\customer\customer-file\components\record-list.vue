<!--
 * @Description: 跟进记录
 * @Author: thb
 * @Date: 2023-05-30 11:12:10
 * @LastEditTime: 2023-11-07 10:21:57
 * @LastEditors: thb
-->
<template>
  <div class="list-wrap">
    <el-input v-model="searchText" placeholder="搜索跟进记录">
      <!-- <template #append>
        <el-button :icon="Search" @click="getList" />
      </template> -->
      <template #suffix>
        <el-icon style="cursor: pointer">
          <Search @click="getList" />
        </el-icon>
      </template>
    </el-input>
    <div class="list">
      <div class="list-item" v-for="(record, index) in list" :key="index">
        <div class="item-title">
          <span class="icon arrow-left-sl"></span>
          <span class="name">{{ record?.followUpName || '--' }}</span>
          <span class="time">{{ record?.date }}</span>
        </div>
        <p>
          <span class="label">联系人：</span>
          <span class="text">{{ record?.contactName || '--' }}</span>
        </p>
        <p>
          <span class="label">跟进方式：</span>
          <span class="text">{{ record?.mode }}</span>
        </p>
        <p>
          <span class="label">跟进内容：</span>
          <ClickMore class="text" :des="record?.followUpRecord" />
        </p>
      </div>
    </div>
  </div>
</template>
<script setup>
import { Search } from '@element-plus/icons-vue'
import { getRecordList } from '@/api/customer/file'
import ClickMore from '@/components/ClickMore'
const searchText = ref('')

const list = ref([])
const getList = async () => {
  const { data } = await getRecordList({
    text: searchText.value,
    ciId: props.ciId,
    pageSize: 999
  })
  list.value = data.records || []
}

const props = defineProps({
  ciId: Number
})

onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.list-wrap {
  :deep(.el-input-group__append) {
    background-color: transparent;
    border-top-right-radius: 25px;
    border-bottom-right-radius: 25px;
  }
}
.list {
  padding: 20px;
  .item-title {
    .icon {
      margin-right: 10px;
    }
    .name {
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      color: #333333;
      flex: 1;
    }
    .time {
      font-size: 14px;
      font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
      font-weight: 400;
      color: #6a7697;
    }

    display: flex;
    align-items: center;
    // justify-content: space-between;
  }
  .list-item {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.label {
  font-weight: 700;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #6a7697;
}
.text {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #333333;
}
</style>
