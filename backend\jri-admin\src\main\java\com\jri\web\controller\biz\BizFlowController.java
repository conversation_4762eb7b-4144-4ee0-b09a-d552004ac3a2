package com.jri.web.controller.biz;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.service.BizFlowService;
import com.jri.biz.domain.vo.BizFlowListVO;
import com.jri.biz.domain.vo.BizFlowVO;
import com.jri.biz.domain.request.BizFlowForm;
import com.jri.biz.domain.request.BizFlowQuery;


import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 流程 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Validated
@RestController
@RequestMapping("/bizFlow")
@Api(tags = "流程")
public class BizFlowController {
    @Resource
    private BizFlowService bizFlowService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<BizFlowListVO>> listPage(BizFlowQuery query) {
        return R.ok(bizFlowService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BizFlowVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(bizFlowService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> saveOrUpdate(@RequestBody @Valid BizFlowForm form) {
        return R.ok(bizFlowService.add(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(bizFlowService.deleteById(id));
    }

    @PostMapping("/setStatus")
    @ApiOperation("状态设置")
    public R<Void> setStatus(@RequestParam("id") Long id) {
        bizFlowService.setStatus(id);
        return R.ok();
    }

    @DeleteMapping("/deleteByIds")
    @ApiOperation("批量删除")
    public R<Boolean> deleteByIds(@RequestParam("ids") List<Long> ids) {
        return R.ok(bizFlowService.deleteByIds(ids));
    }
}

