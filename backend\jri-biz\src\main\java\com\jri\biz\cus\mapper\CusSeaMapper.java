package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusSea;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusSeaListVO;
import com.jri.biz.cus.domain.vo.CusSeaVO;
import com.jri.biz.cus.domain.request.CusSeaQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 公海配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
public interface CusSeaMapper extends BaseMapper<CusSea> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusSeaListVO> listPage(@Param("query") CusSeaQuery query, Page<CusSeaListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusSeaVO getDetailById(@Param("id") Long id);
}
