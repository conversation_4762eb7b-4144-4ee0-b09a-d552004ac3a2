package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 工单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("order_info")
@ApiModel(value = "Order对象", description = "工单")
public class Order implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 客户id
     */
    private Long ciId;

    /**
     * 紧急状态0-一般 1-紧急
     */
    private String isUrgent;

    /**
     * 期望完成时间
     */
    private LocalDate expectTime;

    /**
     * 工单类型id
     */
    private Long orderTypeId;

    /**
     * 工单标题
     */
    private String orderTitle;

    /**
     * 地区
     */
    private String address;

    /**
     * 工单内容
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 完成反馈/回退说明
     */
    private String feedbackOrDescription;

    /**
     * 指派给
     */
    private Long executor;

    /**
     * 工单状态0-待完成 1-完成 2-回退
     */
    private String orderStatus;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    @ApiModelProperty("合同id")
    private Long contractId;

    /**
     * 行政区划id
     */
    private Long administrativeId;
}
