package com.jri.biz.cus.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 线索/客户标签视图对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCcTagsVO视图对象")
public class CusCcTagsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}