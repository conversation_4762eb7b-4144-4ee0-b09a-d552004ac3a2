package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 借阅记录查询类
 *
 * <AUTHOR>
 * @since 2023-07-11
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="借阅记录查询对象")
public class BorrowRecordQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty("借阅状态0-待审批 1-通过 2-驳回")
    private String status;

    private Long userId;
}
