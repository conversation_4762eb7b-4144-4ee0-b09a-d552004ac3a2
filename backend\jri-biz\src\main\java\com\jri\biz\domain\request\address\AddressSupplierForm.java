package com.jri.biz.domain.request.address;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 地址供应商 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-11-30
 */

@Data
@NoArgsConstructor
@ApiModel(value="地址供应商表单请求对象")
public class AddressSupplierForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("供应商名称")
    private String supplier;

    @ApiModelProperty("地址成本")
    private String addressCost;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("银行账户")
    private String bankAccount;

    @ApiModelProperty("收费类型")
    private String feeType;

    @ApiModelProperty("有效期至")
    private LocalDate validTo;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态0-停用 1-启用")
    private String enable;

}
