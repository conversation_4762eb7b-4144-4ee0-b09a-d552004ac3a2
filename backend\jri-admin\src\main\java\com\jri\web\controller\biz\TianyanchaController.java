package com.jri.web.controller.biz;

import com.alibaba.fastjson2.JSONObject;
import com.jri.biz.domain.vo.CustomerBusinessInformationVO;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

@Validated
@RestController
@RequestMapping("/tianyancha")
@Api(tags = "天眼查")
public class TianyanchaController {

    @Resource
    private RestTemplate restTemplate;

    @Value("${tianyantoken}")
    public String token;

    @GetMapping("/getInfo")
    @ApiOperation("查询企业工商信息")
    public R<CustomerBusinessInformationVO> getInfo(@RequestParam("keyword") String keyword) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", token);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        String url = "http://open.api.tianyancha.com/services/open/ic/baseinfo/normal?keyword=" + keyword;
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.GET, httpEntity, JSONObject.class);
        JSONObject body = exchange.getBody();
        CustomerBusinessInformationVO res = new CustomerBusinessInformationVO();
        if (null != body) {
            Integer error_code = body.getInteger("error_code");
            if (0 == error_code) {
                JSONObject result = body.getJSONObject("result");
                if (null != result) {
                    res.setCrediCode(result.getString("creditCode"));
                    res.setType(result.getString("companyOrgType"));
                    res.setLegalPerson(result.getString("legalPersonName"));
                    res.setRegisteredAddress(result.getString("regLocation"));
                    res.setScope(result.getString("businessScope"));
                    res.setBussinessStatus(result.getString("regStatus"));
                    res.setEstablishDate(getDate(result.getLong("estiblishTime")));
                    res.setRegisteredCapital(result.getString("regCapital"));
                    res.setRegistrationAuthority(result.getString("regInstitute"));
                    res.setRegistrationNumber(result.getString("regNumber"));
                    res.setOpenDate(getDate(result.getLong("fromTime")));
                    res.setOpenEnd(getDate(result.getLong("toTime")));
                    res.setApprovalDate(getDate(result.getLong("approvedTime")));
                    res.setOrganizationCode(result.getString("orgNumber"));
                    res.setIndustry(result.getString("industry"));
//                    res.setCustomerName(result.getString("name"));
                }
            } else {
                throw new ServiceException(body.getString("reason"));
            }
        }
        System.out.println(body);
        return R.ok(res);
    }

    /**
     * 时间戳转指定时间格式
     *
     * @param timeStamp 时间戳
     */
    public String getDate(Long timeStamp) {
        if (null == timeStamp) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        return sdf.format(new Date(timeStamp));
    }

}
