package com.jri.biz.domain.vo.kanban.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * 月营业额明细条线图
 *
 * <AUTHOR>
 * @since 2024/2/28 11:14
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MonthlySalesBreakdownBarGraph", description = "月营业额明细条线图")
public class MonthlySalesBreakdownBarGraph {

    @ApiModelProperty("记账营业额")
    private BigDecimal addressMonthTotal = BigDecimal.ZERO;

    @ApiModelProperty("地址营业额")
    private BigDecimal bookkeepingMonthTotal = BigDecimal.ZERO;
}
