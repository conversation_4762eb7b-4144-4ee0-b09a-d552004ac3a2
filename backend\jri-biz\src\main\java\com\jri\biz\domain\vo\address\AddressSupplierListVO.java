package com.jri.biz.domain.vo.address;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 地址供应商视图列表对象
 *
 * <AUTHOR>
 * @since 2023-11-30
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="AddressSupplierListVO视图列表对象")
public class AddressSupplierListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("供应商名称")
    private String supplier;

    @ApiModelProperty("地址成本")
    private String addressCost;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("银行账户")
    private String bankAccount;

    @ApiModelProperty("收费类型")
    private String feeType;

    @ApiModelProperty("有效期至")
    private LocalDate validTo;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态0-停用 1-启用")
    private String enable;

    @ApiModelProperty("是否过期 0-否 1-是")
    private String isExpired;
}