package com.jri.biz.service;

import com.jri.biz.domain.entity.CustomerTaxRebateIdentified;
import com.jri.biz.domain.convert.CustomerTaxRebateIdentifiedConvert;
import com.jri.biz.domain.request.CustomerTaxRebateIdentifiedForm;
import com.jri.biz.domain.request.CustomerTaxRebateIdentifiedQuery;
import com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedListVO;
import com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedVO;
import com.jri.biz.mapper.CustomerTaxRebateIdentifiedMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerTaxRebateIdentifiedService extends ServiceImpl<CustomerTaxRebateIdentifiedMapper, CustomerTaxRebateIdentified> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerTaxRebateIdentifiedListVO> listPage(CustomerTaxRebateIdentifiedQuery query) {
        var page = new Page<CustomerTaxRebateIdentifiedListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CustomerTaxRebateIdentifiedVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CustomerTaxRebateIdentifiedForm form) {
        // todo 完善新增/更新逻辑
        CustomerTaxRebateIdentified customerTaxRebateIdentified = CustomerTaxRebateIdentifiedConvert.INSTANCE.convert(form);
        return save(customerTaxRebateIdentified);
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerTaxRebateIdentifiedForm form) {
        // todo 完善新增/更新逻辑
        CustomerTaxRebateIdentified customerTaxRebateIdentified = CustomerTaxRebateIdentifiedConvert.INSTANCE.convert(form);
        return updateById(customerTaxRebateIdentified);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }
}
