package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;


/**
 *  业务类型表单请求对象
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Data
@NoArgsConstructor
@ApiModel(value="业务类型表单请求对象")
public class BusinessTypeForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String typeName;

    /**
     * 业务代码
     */
    @NotBlank(message = "业务代码不能为空")
    private String code;

    /**
     * 合同类型
     */
    @NotBlank(message = "合同类型不能为空")
    private String contractType;

    /**
     * 排序号
     */
    private Integer sort;


    /**
     * 是否给企业用户显示
     */
    private Boolean enterpriseShowFlag;

    @ApiModelProperty("使用状态0-停用 1-启用")
    private Boolean enable;

}
