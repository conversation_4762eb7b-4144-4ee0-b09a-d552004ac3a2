package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;


/**
 * 线索申诉记录表 表单请求对象
 *
 * <AUTHOR>
 * @since 2024-01-10
 */

@Data
@NoArgsConstructor
@ApiModel(value="线索申诉记录表表单请求对象")
public class ClueAppealRecordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "线索id不能为空")
    @ApiModelProperty("线索id")
    private Long clueId;

    @ApiModelProperty("申述原因")
    private String remark;

}
