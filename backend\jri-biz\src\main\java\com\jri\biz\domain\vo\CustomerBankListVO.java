package com.jri.biz.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 视图列表对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerBankListVO视图列表对象")
public class CustomerBankListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 基本户开户银行
     */
    private String bankBaseName;

    /**
     * 基本户账号
     */
    private String bankBaseAccount;

    /**
     * 是否有网银(0-没有 1-有)
     */
    private String internetbankFlag;

    /**
     * 是否有结算卡(0-没有 1-有)
     */
    private String debitCardFlag;

    /**
     * 是否有回单卡(0-没有 1-有)
     */
    private String receiptCardFlag;

    /**
     * 回单卡账户
     */
    private String receiptCardAccount;

    /**
     * 回单卡密码
     */
    private String receiptCardPassword;

    /**
     * 回单卡类型
     */
    private String receiptCardType;

    /**
     * 一般户开户银行
     */
    private String commonBankName;

    /**
     * 一般户账号
     */
    private String commonBankAccount;

    /**
     * 是否有一般户网银(0-没有 1-有)
     */
    private String commonInternetbankFlag;

    /**
     * 一般户回单卡账号
     */
    private String commonInternetbankAccount;

    /**
     * 一般户回单卡密码
     */
    private String commonReceiptCardPassword;

    /**
     * 对账周期
     */
    private String cycle;


}
