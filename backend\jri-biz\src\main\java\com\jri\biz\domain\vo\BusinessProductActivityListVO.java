package com.jri.biz.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品活动价视图列表对象
 *
 * <AUTHOR>
 * @since 2023-11-02
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BusinessProductActivityListVO视图列表对象")
public class BusinessProductActivityListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}