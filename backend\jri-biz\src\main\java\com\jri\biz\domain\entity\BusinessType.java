package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("business_type")
@ApiModel(value = "BusinessType对象", description = "")
public class BusinessType implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 业务类型
     */
    private String typeName;

    /**
     * 业务代码
     */
    private String code;

    /**
     * 合同类型
     */
    private String contractType;

    @TableField(exist = false)
    private List<BusinessProduct> child;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 是否给企业用户显示
     */
    private Boolean enterpriseShowFlag;

    /**
     * 使用状态0-停用 1-启用
     */
    private Boolean enable;

}
