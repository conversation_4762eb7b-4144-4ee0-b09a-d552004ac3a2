package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.ContractTemp;
import com.jri.biz.domain.request.ContractTempForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 合同模板对象转换
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

@Mapper
public interface ContractTempConvert {
    ContractTempConvert INSTANCE = Mappers.getMapper(ContractTempConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    ContractTemp convert(ContractTempForm form);

}