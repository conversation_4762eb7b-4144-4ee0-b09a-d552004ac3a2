package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 合同模板
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("contract_temp")
@ApiModel(value = "ContractTemp对象", description = "合同模板")
public class ContractTemp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 模板名称
     */
    private String tempName;

    /**
     * 合同类型0-记账合同1-一次性合同2-地址服务协议合同
     */
    private String contractType;

    /**
     * 审批流程id
     */
    private Long flowId;

    /**
     * 备注
     */
    private String remark;

    /**
     * html字符串
     */
    private String htmlStr;

    /**
     * 字段列表json
     */
    private String fieldList;

    /**
     * 状态0-待审批 1-通过 2-驳回
     */
    private String status;

    /**
     * 使用状态0-停用 1-启用
     */
    private String enable;

    /**
     * 附件
     */
    private String urls;
}
