package com.jri.biz.constants;

/**
 * 材料相关常量
 *
 * <AUTHOR>
 * @since 2023/11/30 14:30
 */
public class MaterialConstant {

    /**
     * 材料类型常量
     */
    public static final String[] CATEGORY_LIST = {"营业执照正本", "营业执照副本", "股东会议决议", "章程", "法人身份证",
            "非法人身份证", "公章", "财务章", "法人章", "发票章", "税控盘", "税控钥匙", "空白普票", "空白增票", "开户信息", "网银U盾",
            "结算卡", "密码器", "回单卡", "海关登记证", "电子口岸", "中英文条形章", "报关章", "手签章", "许可证件", "合同章", "凭证",
            "账册", "其他账务资料", "其他资料", "预包装备案"};


    /**
     * 入库类型
     */
    public static class INBOUND_TYPE {
        public static final String ADD = "新增入库";
        public static final String HANDOVER = "交接入库";
    }

    /**
     * 交接状态
     */
    public static class HANDOVER_STATUS {

        /**
         * 待交接
         */
        public static final String PENDING = "pending";

        /**
         * 接收
         */
        public static final String ACCEPT = "accept";

        /**
         * 拒绝
         */
        public static final String REFUSE = "refuse";

    }


}
