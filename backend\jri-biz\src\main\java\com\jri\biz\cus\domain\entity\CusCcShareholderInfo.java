package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 股东信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_cc_shareholder_info")
@ApiModel(value = "CusCcShareholderInfo对象", description = "股东信息")
public class CusCcShareholderInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("股东姓名")
    private String shareholderName;

    @ApiModelProperty("股东手机号")
    private String shareholderPhone;

    @ApiModelProperty("线索/客户id")
    private Long ccId;


}
