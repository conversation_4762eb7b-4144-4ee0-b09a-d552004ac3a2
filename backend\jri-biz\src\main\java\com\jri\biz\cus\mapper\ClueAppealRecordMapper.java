package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.cus.domain.entity.ClueAppealRecord;
import com.jri.biz.cus.domain.request.ClueAppealRecordQuery;
import com.jri.biz.cus.domain.vo.ClueAppealRecordListVO;
import com.jri.biz.cus.domain.vo.ClueAppealRecordVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 线索申诉记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
public interface ClueAppealRecordMapper extends BaseMapper<ClueAppealRecord> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<ClueAppealRecordListVO> listPage(@Param("query") ClueAppealRecordQuery query, Page<ClueAppealRecordListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    ClueAppealRecordVO getDetailById(@Param("id") Long id);

    /**
     * 根据线索查询数量
     *
     * @param ClueId 线索id
     * @return 数量
     */
    default Long getCountByClueId(Long ClueId) {
        var wrapper = new LambdaQueryWrapper<ClueAppealRecord>();
        wrapper.eq(ClueAppealRecord::getClueId, ClueId);
        return selectCount(wrapper);
    }

    /**
     * 根据线索id查询线索
     *
     * @param ClueId 线索id
     * @return 数量
     */
    default ClueAppealRecord getByClueId(Long ClueId) {
        var wrapper = new LambdaQueryWrapper<ClueAppealRecord>();
        wrapper.eq(ClueAppealRecord::getClueId, ClueId);
        return selectOne(wrapper);
    }
}
