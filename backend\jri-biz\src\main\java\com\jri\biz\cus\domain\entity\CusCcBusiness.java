package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商机
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_cc_business")
@ApiModel(value = "CusCcBusiness对象", description = "商机")
public class CusCcBusiness implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 商机名称
     */
    private String name;

    /**
     * 客户id
     */
    private Long ccId;

    /**
     * 预计成交金额
     */
    private String expectAmount;

    /**
     * 预计成交时间
     */
    private LocalDate expectTime;

    /**
     * 销售阶段
     */
    private String stage;

    /**
     * 阶段百分比
     */
    private String stagePercentage;

    /**
     * 状态
     */
    private String status;

    /**
     * 实际成交金额
     */
    private BigDecimal actualAmount;

    /**
     * 记账实际成交金额
     */
    private BigDecimal bookkeepingActualAmount;

    /**
     * 地址实际成交金额
     */
    private BigDecimal addressActualAmount;

    /**
     * 其他实际成交金额
     */
    private BigDecimal otherActualAmount;

    /**
     * 赢单时间
     */
    private LocalDateTime winTime;

    /**
     * 输单时间
     */
    private LocalDateTime loseTime;

    /**
     * 输单原因
     */
    private String reason;

    /**
     * 输单描述
     */
    private String remark;

    /**
     * 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
     */
    private String followStatus;

    /**
     * 账单id列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> paymentIds;
}
