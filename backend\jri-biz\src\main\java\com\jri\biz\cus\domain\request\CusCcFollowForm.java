package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 跟进记录 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Data
@NoArgsConstructor
@ApiModel(value="跟进记录表单请求对象")
public class CusCcFollowForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("跟进内容")
    private String content;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("跟进方式")
    private String type;

    @ApiModelProperty("线索/客户id(商机id)")
    private Long ccId;
}
