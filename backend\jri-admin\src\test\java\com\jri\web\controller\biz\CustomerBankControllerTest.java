package com.jri.web.controller.biz;

import com.alibaba.fastjson2.JSON;
import com.jri.biz.domain.request.CustomerBankForm;
import com.jri.biz.domain.request.CustomerInformationForm;
import com.jri.framework.security.context.AuthenticationContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @since 2023/6/1 15:19
 */
@SpringBootTest
class CustomerBankControllerTest {
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private CustomerBankController controller;

    @BeforeEach
    void before() {
        var authenticationToken = new UsernamePasswordAuthenticationToken("admin", "admin123");
        AuthenticationContextHolder.setContext(authenticationToken);
        var authentication = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }

    @Test
    void save() throws Exception{
        var body = new CustomerBankForm();
        body.setCiId(1L);
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .post("/customerBank/save")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(body));
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }
}