package com.jri.biz.cus.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.biz.cus.domain.entity.CusBusinessBiz;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;


/**
 * 商机 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Data
@NoArgsConstructor
@ApiModel(value="商机表单请求对象")
public class CusCcBusinessForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商机名称")
    private String name;

    @ApiModelProperty("客户id")
    private Long ccId;

    @ApiModelProperty("预计成交金额")
    private String expectAmount;

    @ApiModelProperty("预计成交时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("销售阶段")
    private String stage;

    @ApiModelProperty("阶段百分比")
    private String stagePercentage;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("实际成交金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("输单原因")
    private String reason;

    @ApiModelProperty("输单描述")
    private String remark;

    @ApiModelProperty("业务列表")
    private List<CusBusinessBiz> list;
}
