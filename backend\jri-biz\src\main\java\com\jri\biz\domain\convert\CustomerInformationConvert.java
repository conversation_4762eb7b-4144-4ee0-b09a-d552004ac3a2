package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.domain.request.CustomerInformationForm;
import com.jri.biz.domain.vo.CustomerInformationVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Mapper
public interface CustomerInformationConvert {
    CustomerInformationConvert INSTANCE = Mappers.getMapper(CustomerInformationConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerInformation convert(CustomerInformationForm form);

    CustomerInformation convert2(CustomerInformationVO civ);
}