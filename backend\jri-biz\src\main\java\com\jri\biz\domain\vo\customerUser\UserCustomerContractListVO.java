package com.jri.biz.domain.vo.customerUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/1/25 11:28
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "UserCustomerContractListVO视图列表对象")
public class UserCustomerContractListVO {

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("客户信息主表id")
    private Long ciId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("0-录入合同1-模板创建合同")
    private String type;

    @ApiModelProperty("起始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("合同总金额")
    private BigDecimal totalCost;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("产品名称")
    private String productName;

}
