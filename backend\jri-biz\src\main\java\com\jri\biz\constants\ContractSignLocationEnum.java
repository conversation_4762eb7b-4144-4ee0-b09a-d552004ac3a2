package com.jri.biz.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 合同pdf签名位置常量
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ContractSignLocationEnum {

    /**
     * 代理记账合同
     */
    ACCOUNTING("accounting", 0, 130F, 95F, 80F, 45F),

    /**
     * 代理记账合同 零申报
     */
    ACCOUNTING_ZERO("accounting", 0, 130F, 80F, 90F, 48F),

    /**
     * 地址服务协议合同 园区服务协议
     */
    ADDRESS("accounting", 1, 125F, 442F, 90F, 48F),

    /**
     * 一次性合同
     */
    DISPOSABLE("disposable", 0, 155F, 210F, 90F, 48F);


    private String contractType;
    private Integer page;
    private Float x;
    private Float y;
    private Float width;
    private Float height;


}
