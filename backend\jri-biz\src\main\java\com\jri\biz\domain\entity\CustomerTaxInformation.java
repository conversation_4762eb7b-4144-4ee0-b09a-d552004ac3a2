package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_tax_information")
@ApiModel(value = "CustomerTaxInformation对象", description = "")
public class CustomerTaxInformation implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 税务信息表id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 客户主表id
     */
    private Long ciId;

    /**
     * 身份证号码
     */
    private String identityNumber;

    /**
     * 登记税务机关
     */
    private String taxRegistrationOrgan;

    /**
     * 税务机关地址
     */
    private String taxOrganAddress;

    /**
     * 税率登记
     */
    private String rateRegistration;

    /**
     * 法人是否实名登记
     */
    private Boolean legalRealNameFlag;

    /**
     * 办税员实名认证
     */
    private Boolean taxRealNameFlag;

    /**
     * 个体户核定
     */
    private Boolean individualCheckFlag;

    /**
     * 是否网上税务局注册
     */
    private Boolean onlineRevenueRegistrationFlag;

    /**
     * 预留手机号
     */
    private String reservedPhoneNumber;

    /**
     * 税企银三方协议
     */
    private Boolean tripleAgreementFlag;

    /**
     * 所得税认定方式
     */
    private Boolean identificationMethodFlag;

    /**
     * 证书账号
     */
    private String certificateAccount;

    /**
     * 证书密码
     */
    private String certificatePassword;

    /**
     * 开票盘
     */
    private String drawingSheetFlag;

    /**
     * 发票
     */
    private String invoiceFlag;

    /**
     * 发票章
     */
    private String invoiceSealFlag;

    /**
     * 开票盘归属部门
     */
    private String drawingSheetDept;

    /**
     * 发票归属部门
     */
    private String invoiceDept;

    /**
     * 发票章归属部门
     */
    private String invoiceSealDept;

    /**
     * 发票额度
     */
    private String invoiceLimit;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 开票盘类型
     */
    private String drawingSheetType;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 自然人密码
     */
    private String naturalPersonPassword;
}
