<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-06-02 15:24:38
 * @LastEditTime: 2023-11-20 15:19:48
 * @LastEditors: thb
-->
<template>
  <FormTable :formData="formData" :option="option">
    <template #action="{ row }">
      <el-button v-hasPermi="['customer:customer-file:file_download']" type="primary" @click="handleUpload(row)">下载</el-button>
      <el-button type="primary" v-if="isRadio(row)" @click="handlePreview(row)">预览</el-button>
      <template v-if="discard !== 1">
        <el-button v-hasPermi="['customer:customer-file:file_delete']" type="danger" @click="handleDelete(row)">删除</el-button>
      </template>
    </template>
  </FormTable>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import { useHandleData } from '@/hooks/useHandleData'
import FormTable from '@/components/FormTable'
import { getRelateFiles, deleteCustomerFile } from '@/api/customer/file'
import { getFileUrlByOss } from '@/api/file/file.js'
// import { usePreview } from '@/hooks/usePreview'
import iFrame from '@/components/iFrame'
const formData = ref({
  tableData: []
})

const props = defineProps({
  ciId: Number,
  discard: Number
})

const option = [
  {
    prop: 'fileKey',
    label: '附件名称'
  },
  {
    prop: 'fileNames',
    label: '文件名称'
  },
  {
    prop: 'uploadTime',
    width: '180',
    label: '上传时间'
  },
  {
    prop: 'uploadBy',
    label: '上传人'
  },
  {
    prop: 'fileSize',
    label: '文件大小(Kb)'
  },
  {
    prop: 'action',
    width: '250',
    label: '操作'
  }
]

const { proxy } = getCurrentInstance()
const handleUpload = async row => {
  // const url = row.urls.split('/').slice(3).join('/')
  // proxy.download(
  //   '/common/download2Oss', // 走oss
  //   {
  //     fileName: row.urls,
  //     delete: false
  //   },
  //   row.fileNames
  // )
  const { data } = await getFileUrlByOss(row.urls)
  window.open(data)
}
// 音频的格式mp3、m4a、wav
const mpList = ['mp3', 'wav', 'm4a']
// 判断是否是radio
const isRadio = file => {
  const name = file.name || file.fileNames
  // 判断 文件后缀 如果是音频 不需要预览
  const fileName = name.split('.')
  const fileExt = fileName[fileName.length - 1]
  if (mpList.includes(fileExt)) return false
  return true
}

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const handlePreview = row => {
  previewShow.value = true
  // console.log('row', row)
  // console.log('url', url)
  previewUrl.value = row.urls
  console.log('urls', row.urls)
  // const url = row.urls.split('/').slice(3).join('/')
  // http://**************:20780/profile/upload/2023/06/12/5.1类和对象_20230612153105A005.pdf
  // window.open('http://**************:20780/' + url)
}

const handleDelete = async row => {
  await useHandleData(deleteCustomerFile, row.id, `删除${row.fileKey}:${row.fileNames} 文件`)
  await getAllFiles()
}

const getAllFiles = async () => {
  const { data } = await getRelateFiles(props.ciId)
  formData.value.tableData = data || []
}
onMounted(() => {
  getAllFiles()
})
</script>
<style lang="scss" scoped></style>
