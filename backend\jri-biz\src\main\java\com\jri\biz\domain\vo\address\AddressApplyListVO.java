package com.jri.biz.domain.vo.address;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 地址申请视图列表对象
 *
 * <AUTHOR>
 * @since 2023-12-01
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "AddressApplyListVO视图列表对象")
public class AddressApplyListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("房本id")
    private Long propertyId;

    @ApiModelProperty("房本")
    private String propertyName;

    @ApiModelProperty("账单id")
    private Long paymentId;

    @ApiModelProperty("账单编号")
    private String paymentNo;

    @ApiModelProperty("状态 待选地址 暂无地址 已完成 已结束")
    private String status;

    @ApiModelProperty("区域id")
    private String areaId;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty(value = "账期开始时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date paymentStartTime;

    @ApiModelProperty(value = "账期结束时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date paymentEndTime;

}