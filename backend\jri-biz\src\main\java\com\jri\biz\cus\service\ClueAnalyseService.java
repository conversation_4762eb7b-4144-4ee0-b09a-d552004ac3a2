package com.jri.biz.cus.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.cus.constants.AppealStatusConstant;
import com.jri.biz.cus.constants.SourceConstant;
import com.jri.biz.cus.domain.entity.ClueAppealRecord;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.entity.CusSourceDirect;
import com.jri.biz.cus.domain.entity.CusSourcePlatform;
import com.jri.biz.cus.domain.request.ClueAnalyseQuery;
import com.jri.biz.cus.domain.vo.*;
import com.jri.biz.cus.mapper.ClueAnalyseMapper;
import com.jri.biz.cus.mapper.CusSourceDirectMapper;
import com.jri.biz.cus.mapper.CusSourcePlatformMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;

/**
 * 线索分析 service
 *
 * <AUTHOR>
 * @since 2024/1/2 14:01
 */
@Service
public class ClueAnalyseService {

    @Resource
    private ClueAnalyseMapper clueAnalyseMapper;

    @Resource
    private CusSourceDirectMapper cusSourceDirectMapper;

    @Resource
    private CusSourcePlatformMapper cusSourcePlatformMapper;

    @Resource
    private ClueAppealRecordService clueAppealRecordService;


    /**
     * 渠道来源分析
     *
     * @param query 线索分析查询
     * @return 线索分析
     */
    public IPage<BaseClueSourceAnalyseVO> channelAnalyse(ClueAnalyseQuery query) {
        var page = new Page<BaseClueSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.CHANNEL);
        var selectPage = clueAnalyseMapper.baseSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, null);
        recordList.parallelStream().forEach(vo -> {
            // 查询人员对应sourceId统计情况
            var staffQuery = BeanUtil.copyProperties(query, ClueAnalyseQuery.class);
            staffQuery.setSourceId(vo.getId());
            staffQuery.setPageSize(99999);
            vo.setUserList(this.staffChannelAnalyse(staffQuery).getRecords());
        });
        return selectPage;
    }

    /**
     * 客户介绍分析
     *
     * @param query 查询条件
     * @return 分析
     */
    public IPage<ClueSourceCustomerIntroductionAnalyseVO> customerIntroductionAnalyse(ClueAnalyseQuery query) {
        var page = new Page<ClueSourceCustomerIntroductionAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.CUSTOMER_INTRODUCTION);
        var selectPage = clueAnalyseMapper.CustomerIntroductionSourcePage(query, page);
        var recordList = selectPage.getRecords();
        YearMonth yearMonth = query.getYearMonth();
        var startTime = yearMonth.atDay(1).atStartOfDay();
        var endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
        recordList.parallelStream().forEach(vo -> {
            vo.setClueCount(clueAnalyseMapper.customerIntroductionClueCount(vo.getId(), startTime, endTime, false));
            vo.setClueConversionCount(clueAnalyseMapper.customerIntroductionClueCount(vo.getId(), startTime, endTime, true));
            vo.setGmv(clueAnalyseMapper.getCustomerIntroductionGMV(vo.getId(), startTime, endTime, "business.actual_amount"));
            vo.setBookkeepingGmv(clueAnalyseMapper.getCustomerIntroductionGMV(vo.getId(), startTime, endTime, "business.bookkeeping_actual_amount"));
            vo.setAddressGmv(clueAnalyseMapper.getCustomerIntroductionGMV(vo.getId(), startTime, endTime, "business.address_actual_amount"));
            vo.setOtherGmv(clueAnalyseMapper.getCustomerIntroductionGMV(vo.getId(), startTime, endTime, "business.other_actual_amount"));
            vo.setIntroductionCount(clueAnalyseMapper.customerIntroductionCount(vo.getId()));
            // 查询人员对应sourceId统计情况
            var staffQuery = BeanUtil.copyProperties(query, ClueAnalyseQuery.class);
            staffQuery.setSourceId(3L);
            staffQuery.setPageSize(99999);
            vo.setUserList(this.staffCustomerIntroductionAnalyse(staffQuery).getRecords());
        });
        return selectPage;
    }

    /**
     * 直投来源分析
     *
     * @param query 查询条件
     * @return 分析
     */
    public IPage<ClueSourceDirectAnalyseVO> directAnalyse(ClueAnalyseQuery query) {
        String isSea = null;
        var page = new Page<BaseClueSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.DIRECT);
        var selectPage = clueAnalyseMapper.baseSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, isSea);
        var resultPage = copyPage(page, ClueSourceDirectAnalyseVO.class);
        // 线索单价 线索成本计算
        YearMonth yearMonth = query.getYearMonth();
        var startTime = yearMonth.atDay(1).atStartOfDay();
        var endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
        resultPage.getRecords().parallelStream().forEach(vo -> {
            List<Long> chargeIdList = new ArrayList<>();
            List<BigDecimal> unitPriceList = new ArrayList<>();
            var clueList = clueAnalyseMapper.getClueList(startTime, endTime, null, vo.getId(), SourceConstant.DIRECT, isSea);
            BigDecimal cost = BigDecimal.ZERO;
            for (var clue : clueList) {
                cost = cost.add(unitPriceHandle(clue, chargeIdList, unitPriceList, isSea));
            }

            vo.setCost(cost);
            vo.setUnitPriceList(unitPriceList);
            // 查询人员对应sourceId统计情况
            var staffQuery = BeanUtil.copyProperties(query, ClueAnalyseQuery.class);
            staffQuery.setSourceId(vo.getId());
            staffQuery.setPageSize(99999);
            vo.setUserList(this.staffDirectAnalyse(staffQuery).getRecords());
        });
        return resultPage;
    }

    /**
     * 计算这个每个客资的单价
     *
     * @param clue 客资
     * @return 单价
     */
    private BigDecimal unitPriceHandle(CusCustomerOrClue clue, List<Long> chargeIdList, List<BigDecimal> unitPriceList, String isSea) {
        Long sourceId = clue.getSourceId();
        LocalDate startDate = clue.getCreateTime().toLocalDate();
        var cusSourceDirectList = cusSourceDirectMapper.selectListByRecentDate(startDate, sourceId);
        if (ObjectUtil.isEmpty(cusSourceDirectList)) {
            return BigDecimal.ZERO;
        }
        LocalDate chargeTime = cusSourceDirectList.get(0).getChargeTime();
        // sum
        BigDecimal amount = cusSourceDirectList.stream().map(CusSourceDirect::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        LocalDate minChargeTime = cusSourceDirectMapper.getMinChargeTime(startDate, sourceId);
        // 判断是否有最近充值时间 也就是查询线索的结束时间
        LocalDateTime endTime;
        if (ObjectUtil.isNull(minChargeTime)) {
            endTime = null;
        } else {
            endTime = minChargeTime.plusDays(-1).atTime(23, 59, 59);
        }
        // 查询充值时间内线索数量
        Long clueCount = clueAnalyseMapper.countClueBySourceId(sourceId, chargeTime.atStartOfDay(), endTime, isSea);
        if (clueCount == 0) {
            return BigDecimal.ZERO;
        }
        // 计算单价
        BigDecimal avg = amount.divide(BigDecimal.valueOf(clueCount), 2, RoundingMode.HALF_UP);
        // 记录充值过的id
        if (ObjectUtil.isNotNull(chargeIdList)) {
            for (CusSourceDirect cusSourceDirect : cusSourceDirectList) {
                if (chargeIdList.contains(cusSourceDirect.getId())) {
                    continue;
                }
                chargeIdList.add(cusSourceDirect.getId());
                unitPriceList.add(avg);
            }
        }
        return avg;
    }

    /**
     * 平台来源分析
     *
     * @param query 查询条件
     * @return 分析
     */
    public IPage<ClueSourcePlatformAnalyseVO> platformAnalyse(ClueAnalyseQuery query) {
        var page = new Page<BaseClueSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.PLATFORM);
        var selectPage = clueAnalyseMapper.baseSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, null);
        var resultPage = copyPage(page, ClueSourcePlatformAnalyseVO.class);
        YearMonth yearMonth = query.getYearMonth();
        var startTime = yearMonth.atDay(1).atStartOfDay();
        var endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
        resultPage.getRecords().parallelStream().forEach(vo -> {
            var sourcePlatform = cusSourcePlatformMapper.getByMainId(vo.getId());
            // 申诉量
            vo.setAppealCount(clueAnalyseMapper.platformAppealCount(startTime, endTime, null, vo.getId(), null, null));
            // 申诉成功量
            vo.setAppealSuccessCount(clueAnalyseMapper.platformAppealCount(startTime, endTime, null, vo.getId(), null, AppealStatusConstant.APPEAL_SUCCESS));
            // 有效线索：线索量-申诉成功
            vo.setCredibleClueCount(vo.getClueCount() - vo.getAppealSuccessCount());
            // 创建平台时定的线索单价
            if (ObjectUtil.isNotNull(sourcePlatform.getPrice())) {
                vo.setUnitPrice(sourcePlatform.getPrice());
            }
            // 有效线索*单价
            if (ObjectUtil.isNotNull(sourcePlatform.getPrice())) {
                vo.setCost(sourcePlatform.getPrice().multiply(BigDecimal.valueOf(vo.getCredibleClueCount())));
            }
            // 查询人员对应sourceId统计情况
            var staffQuery = BeanUtil.copyProperties(query, ClueAnalyseQuery.class);
            staffQuery.setSourceId(vo.getId());
            staffQuery.setPageSize(99999);
            vo.setUserList(this.staffPlatformAnalyse(staffQuery).getRecords());
        });
        return resultPage;
    }

    /**
     * 其他来源分析
     *
     * @param query 查询条件
     * @return 分析
     */
    public IPage<BaseClueSourceAnalyseVO> otherAnalyse(ClueAnalyseQuery query) {
        var page = new Page<BaseClueSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.OTHER);
        var selectPage = clueAnalyseMapper.baseSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, null);
        return selectPage;
    }

    /**
     * 人员&渠道统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public IPage<BaseStaffSourceAnalyseVO> staffChannelAnalyse(ClueAnalyseQuery query) {
        var page = new Page<BaseStaffSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.CHANNEL);
        var selectPage = clueAnalyseMapper.baseStaffSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, true, "0");
        return selectPage;
    }

    /**
     * 人员&客户介绍统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public IPage<BaseStaffSourceAnalyseVO> staffCustomerIntroductionAnalyse(ClueAnalyseQuery query) {
        var page = new Page<BaseStaffSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.CUSTOMER_INTRODUCTION);
        var selectPage = clueAnalyseMapper.baseStaffSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, true, "0");
        return selectPage;
    }

    /**
     * 人员&直投统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public IPage<ClueStaffSourceDirectAnalyseVO> staffDirectAnalyse(ClueAnalyseQuery query) {
        String isSea = "0";
        var page = new Page<BaseStaffSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.DIRECT);
        var selectPage = clueAnalyseMapper.baseStaffSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, true, isSea);
        var resultPage = copyPage(page, ClueStaffSourceDirectAnalyseVO.class);
        YearMonth yearMonth = query.getYearMonth();
        var startTime = yearMonth.atDay(1).atStartOfDay();
        var endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
        Long sourceId = query.getSourceId();
        for (var vo : resultPage.getRecords()) {
            List<BigDecimal> unitPriceList = new ArrayList<>();
            List<Long> chargeIdList = new ArrayList<>();
            // 查询属于这个用户这段时间的直投来源的客资
            var clueList = clueAnalyseMapper.getClueList(startTime, endTime, vo.getId(), sourceId, SourceConstant.DIRECT, isSea);
            BigDecimal cost = BigDecimal.ZERO;
            for (var clue : clueList) {
                cost = cost.add(unitPriceHandle(clue, chargeIdList, unitPriceList, null));
            }
            vo.setCost(cost);
            vo.setUnitPriceList(unitPriceList);
        }
        return resultPage;
    }

    /**
     * 人员&平台统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public IPage<ClueSourcePlatformAnalyseVO> staffPlatformAnalyse(ClueAnalyseQuery query) {
        String isSea = "0";
        var page = new Page<BaseStaffSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.PLATFORM);
        var selectPage = clueAnalyseMapper.baseStaffSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, true, isSea);
        var resultPage = copyPage(page, ClueSourcePlatformAnalyseVO.class);
        YearMonth yearMonth = query.getYearMonth();
        var startTime = yearMonth.atDay(1).atStartOfDay();
        var endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
        Long sourceId = query.getSourceId();
        CusSourcePlatform cusSourcePlatform = null;
        if (ObjectUtil.isNotNull(sourceId)) {
            cusSourcePlatform = cusSourcePlatformMapper.getByMainId(sourceId);
        }
        for (var vo : resultPage.getRecords()) {
            // 申诉量
            vo.setAppealCount(clueAnalyseMapper.platformAppealCount(startTime, endTime, vo.getId(), sourceId, "0", null));
            // 申诉成功量
            vo.setAppealSuccessCount(clueAnalyseMapper.platformAppealCount(startTime, endTime, vo.getId(), sourceId, "0", AppealStatusConstant.APPEAL_SUCCESS));
            // 有效线索：线索量-申诉成功
            vo.setCredibleClueCount(vo.getClueCount() - vo.getAppealSuccessCount());
            // 查询属于这个用户这段时间的平台来源的客资
            var clueList = clueAnalyseMapper.getClueList(startTime, endTime, vo.getId(), sourceId, SourceConstant.PLATFORM, isSea);
            BigDecimal cost = BigDecimal.ZERO;
            Map<Long, CusSourcePlatform> platformMap = new HashMap<>();
            for (var clue : clueList) {
                // 判断这条线索是否申述成功过 申述成功跳过
                ClueAppealRecord record = clueAppealRecordService.getBaseMapper().getByClueId(clue.getId());
                if (ObjectUtil.isNotNull(record) && StrUtil.equals(record.getStatus(), AppealStatusConstant.APPEAL_SUCCESS)) {
                    continue;
                }
                var platform = platformMap.get(clue.getSourceId());
                if (ObjectUtil.isNull(platform)) {
                    platform = cusSourcePlatformMapper.getByMainId(clue.getSourceId());
                    platformMap.put(clue.getSourceId(), platform);
                }
                cost = cost.add(Optional.ofNullable(platform)
                        .map(CusSourcePlatform::getPrice)
                        .orElse(BigDecimal.ZERO));
            }
            vo.setCost(cost);
            if (ObjectUtil.isNotNull(cusSourcePlatform)) {
                vo.setUnitPrice(cusSourcePlatform.getPrice());
            }

        }
        return resultPage;
    }

    /**
     * 人员&其他统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public IPage<BaseStaffSourceAnalyseVO> staffOtherAnalyse(ClueAnalyseQuery query) {
        var page = new Page<BaseStaffSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        query.setBiz(SourceConstant.OTHER);
        var selectPage = clueAnalyseMapper.baseStaffSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, true, "0");
        return selectPage;
    }

    /**
     * 人员&全部统计
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public IPage<ClueStaffSourceAllAnalyseVO> staffAllAnalyse(ClueAnalyseQuery query) {
        String isSea = "0";
        var page = new Page<BaseStaffSourceAnalyseVO>(query.getPageNum(), query.getPageSize());
        var selectPage = clueAnalyseMapper.baseStaffSourcePage(query, page);
        var recordList = selectPage.getRecords();
        baseInfoHandle(recordList, query, true, isSea);
        var resultPage = copyPage(page, ClueStaffSourceAllAnalyseVO.class);
        YearMonth yearMonth = query.getYearMonth();
        var startTime = yearMonth.atDay(1).atStartOfDay();
        var endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
        for (var vo : resultPage.getRecords()) {
            Long clueConversionCount = vo.getClueConversionCount();
            if (clueConversionCount == 0L) {
                continue;
            }
            Long dealDays = clueAnalyseMapper.selectClueDealDays(vo.getId(), startTime, endTime);
            if (ObjectUtil.isNull(dealDays)) {
                dealDays = 0L;
            }
            vo.setDealDays(dealDays);
            BigDecimal avg = NumberUtil.div(vo.getDealDays(), clueConversionCount, 1);
            if (ObjectUtil.isNull(avg)) {
                avg = BigDecimal.ZERO;
            }
            vo.setAvgDealDays(avg.toString());
        }

        return resultPage;
    }


    /**
     * 分页属性复制
     *
     * @param sourcePage 原分页
     * @param clazz      目标类型
     * @param <T>        Bean类型
     * @return 复制后的分页
     */
    public static <T> Page<T> copyPage(Page<?> sourcePage, Class<T> clazz) {
        if (sourcePage == null) {
            return null;
        }

        Page<T> result = new Page<>();
        result.setCurrent(sourcePage.getCurrent());
        result.setSize(sourcePage.getSize());
        result.setTotal(sourcePage.getTotal());
        result.setPages(sourcePage.getPages());
        result.setRecords(BeanUtil.copyToList(sourcePage.getRecords(), clazz));
        result.setSearchCount(sourcePage.searchCount());
        result.setOptimizeCountSql(sourcePage.optimizeCountSql());
        return result;
    }

    /**
     * 通用数据处理
     * 线索数量 线索转换数量 收款额
     *
     * @param recordList 列表
     * @param query      查询条件
     * @param userFlag   查询用户标志 id视为用户id
     * @param isSea      是否在公海
     */
    private void baseInfoHandle(List<? extends BaseClueSourceAnalyseVO> recordList, ClueAnalyseQuery query,
                                Boolean userFlag, String isSea) {
        YearMonth yearMonth = query.getYearMonth();
        var startTime = yearMonth.atDay(1).atStartOfDay();
        var endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
        String biz = query.getBiz();
        for (var vo : recordList) {
            Long sourceId = query.getSourceId();
            Long userId = null;
            if (userFlag) {
                userId = vo.getId();
            } else {
                sourceId = vo.getId();
            }
            vo.setClueCount(clueAnalyseMapper.clueCount(sourceId, startTime, endTime, userId, biz, isSea, false));
            vo.setClueConversionCount(clueAnalyseMapper.clueCount(sourceId, startTime, endTime, userId, biz, isSea, true));
            vo.setGmv(clueAnalyseMapper.getGMV(sourceId, startTime, endTime, userId, biz, isSea, "business.actual_amount"));
            vo.setBookkeepingGmv(clueAnalyseMapper.getGMV(sourceId, startTime, endTime, userId, biz, isSea, "business.bookkeeping_actual_amount"));
            vo.setAddressGmv(clueAnalyseMapper.getGMV(sourceId, startTime, endTime, userId, biz, isSea, "business.address_actual_amount"));
            vo.setOtherGmv(clueAnalyseMapper.getGMV(sourceId, startTime, endTime, userId, biz, isSea, "business.other_actual_amount"));
        }
    }

    /**
     * 通用数据处理
     * 线索数量 线索转换数量 收款额
     *
     * @param recordList 列表
     * @param query      查询条件
     */
    private void baseInfoHandle(List<? extends BaseClueSourceAnalyseVO> recordList, ClueAnalyseQuery query, String isSea) {
        baseInfoHandle(recordList, query, false, isSea);
    }

}
