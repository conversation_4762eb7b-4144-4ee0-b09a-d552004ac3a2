package com.jri.biz.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 合同模板 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

@Data
@NoArgsConstructor
@ApiModel(value="合同模板表单请求对象")
public class ContractTempForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("模板名称")
    private String tempName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("审批流程id")
    private Long flowId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("html字符串")
    private String htmlStr;

    @ApiModelProperty("字段列表json")
    private String fieldList;

    @ApiModelProperty("状态0-待审批 1-通过 2-驳回")
    private String status;

    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;

    @ApiModelProperty("附件")
    private String urls;
}
