package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSea;
import com.jri.biz.cus.domain.request.CusSeaForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公海配置对象转换
 *
 * <AUTHOR>
 * @since 2023-08-09
 */

@Mapper
public interface CusSeaConvert {
    CusSeaConvert INSTANCE = Mappers.getMapper(CusSeaConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusSea convert(CusSeaForm form);

}