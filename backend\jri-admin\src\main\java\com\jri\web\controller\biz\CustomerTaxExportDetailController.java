package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.jri.biz.domain.request.CustomerTaxExportDetailForm;
import com.jri.biz.domain.request.CustomerTaxExportDetailQuery;
import com.jri.biz.domain.request.TaxExportDetailExportQuery;
import com.jri.biz.domain.vo.CustomerTaxExportDetailListVO;
import com.jri.biz.domain.vo.CustomerTaxExportDetailVO;
import com.jri.biz.mapper.CustomerTaxExportDetailMapper;
import com.jri.biz.service.CustomerTaxExportDetailService;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerTaxExportDetail")
@Api(tags = "退税详情")
public class CustomerTaxExportDetailController {
    @Resource
    private CustomerTaxExportDetailService customerTaxExportDetailService;

    @Resource
    private CustomerTaxExportDetailMapper customerTaxExportDetailMapper;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerTaxExportDetailListVO>> listPage(CustomerTaxExportDetailQuery query) {
        return R.ok(customerTaxExportDetailService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerTaxExportDetailVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerTaxExportDetailService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CustomerTaxExportDetailForm form) {
        return R.ok(customerTaxExportDetailService.add(form));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CustomerTaxExportDetailForm form) {
        return R.ok(customerTaxExportDetailService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long[] ids) {
        return R.ok(customerTaxExportDetailService.deleteById(ids));
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public void export(HttpServletResponse response, @Validated @RequestBody TaxExportDetailExportQuery query) {
        List<CustomerTaxExportDetail> CustomerTaxExportDetail =customerTaxExportDetailMapper.selectByMainId(query.getMainId());
        if (ObjectUtils.isEmpty(CustomerTaxExportDetail)) {
            throw new ServiceException("没有数据可导出");
        }
        ExcelUtil<CustomerTaxExportDetail> util = new ExcelUtil<>(CustomerTaxExportDetail.class);
        util.exportExcel(response, CustomerTaxExportDetail, "出口明细");
    }
}

