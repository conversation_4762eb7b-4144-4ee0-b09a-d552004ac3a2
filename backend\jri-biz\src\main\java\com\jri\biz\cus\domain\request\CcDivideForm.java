package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value="分配线索客户表单请求对象")
public class CcDivideForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索客户id")
    private Long id;

    @ApiModelProperty("接收人id")
    private Long currentUserId;

    @ApiModelProperty("类型0-线索分配1-客户分配")
    private String type;
}
