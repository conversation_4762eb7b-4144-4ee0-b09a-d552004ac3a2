package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusSeaInventory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusSeaInventoryListVO;
import com.jri.biz.cus.domain.vo.CusSeaInventoryVO;
import com.jri.biz.cus.domain.request.CusSeaInventoryQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 保有量设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public interface CusSeaInventoryMapper extends BaseMapper<CusSeaInventory> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusSeaInventoryListVO> listPage(@Param("query") CusSeaInventoryQuery query, Page<CusSeaInventoryListVO> page);

    /**
     * 根据id查询明细
     *
     * @return 结果
     */
    CusSeaInventoryVO getDetailById();
}
