package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业用户建议
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("user_suggestion")
@ApiModel(value = "UserSuggestion对象", description = "企业用户建议")
public class UserSuggestion implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty("建议内容")
    private String suggestionData;

    @ApiModelProperty("处理状态")
    private String handleStatus;

    @ApiModelProperty("回复用户id")
    private Long responseUserId;

    @ApiModelProperty("回复时间")
    private LocalDateTime responseTime;

    @ApiModelProperty("回复内容")
    private String responseData;

    @ApiModelProperty("用户评价")
    private Integer star;


}
