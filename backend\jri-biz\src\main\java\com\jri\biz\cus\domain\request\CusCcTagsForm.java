package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 线索/客户标签 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Data
@NoArgsConstructor
@ApiModel(value="线索/客户标签表单请求对象")
public class CusCcTagsForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

}
