package com.jri.biz.cus.domain.request;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 联系人 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Data
@NoArgsConstructor
@ApiModel(value="联系人表单请求对象")
public class CusCcContactForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String contactName;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String contactPhone;

    /**
     * 职位
     */
    @ApiModelProperty("职位")
    private String post;

    /**
     * 微信
     */
    @ApiModelProperty("微信")
    private String wx;

    /**
     * QQ
     */
    @ApiModelProperty("QQ")
    private String qq;

    /**
     * 性别0-未知 1-男 2-女
     */
    @ApiModelProperty("性别0-未知 1-男 2-女")
    private String sex;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 生日
     */
    @ApiModelProperty("生日")
    private String birthday;

    /**
     * 来源(统计用)0-直接创建1-由线索转为客户联系人
     */
    @ApiModelProperty("来源(统计用)0-直接创建1-由线索转为客户联系人")
    private String source;

    /**
     * 线索/客户id
     */
    @ApiModelProperty("线索/客户id")
    private Long ccId;

    /**
     * 转为客户时间
     */
    @ApiModelProperty("转为客户时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;

    /**
     * 是否决策人0-否1-是
     */
    @ApiModelProperty("是否决策人0-否1-是")
    private Integer isLeader;

    /**
     * 是否常用联系人0-否1-是
     */
    @ApiModelProperty("是否常用联系人0-否1-是")
    private Integer isOften;
}
