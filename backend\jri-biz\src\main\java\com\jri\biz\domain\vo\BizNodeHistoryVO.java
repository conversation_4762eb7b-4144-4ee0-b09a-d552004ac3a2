package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 流程历史视图对象
 *
 * <AUTHOR>
 * @since 2023-07-04
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BizNodeHistoryVO视图对象")
public class BizNodeHistoryVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}