package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 *  表单请求对象
 *
 * <AUTHOR>
 * @since 2023-06-01
 */

@Data
@NoArgsConstructor
@ApiModel(value="废弃记录请求对象")
public class CustomerDiscardRecordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "客户表主键")
    private Long ciId;
    @ApiModelProperty(value = "是否弃用")
    private Integer discard;
    @ApiModelProperty(value = "弃用原因")
    private String discardReason;

    @ApiModelProperty(value = "客户性质")
    private String customerProperty;

    @ApiModelProperty(value = "客户状态")
    private String customerStatus;
}
