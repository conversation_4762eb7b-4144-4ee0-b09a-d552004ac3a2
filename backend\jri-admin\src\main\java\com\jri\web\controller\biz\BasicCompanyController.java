package com.jri.web.controller.biz;


import com.jri.common.exception.ServiceException;
import com.jri.common.utils.file.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.util.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.jri.biz.service.BasicCompanyService;
import com.jri.biz.domain.vo.BasicCompanyListVO;
import com.jri.biz.domain.vo.BasicCompanyVO;
import com.jri.biz.domain.request.BasicCompanyForm;
import com.jri.biz.domain.request.BasicCompanyQuery;


import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * <p>
 * 分公司信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Validated
@RestController
@RequestMapping("/basicCompany")
@Api(tags = "分公司信息")
public class BasicCompanyController {
    @Resource
    private BasicCompanyService basicCompanyService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<BasicCompanyListVO>> listPage(BasicCompanyQuery query) {
        return R.ok(basicCompanyService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BasicCompanyVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(basicCompanyService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid BasicCompanyForm form) {
        basicCompanyService.add(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(basicCompanyService.deleteById(id));
    }

    @PostMapping("/enable")
    @ApiOperation("状态设置")
    public R<Void> enable(@RequestParam("id") Long id) {
        basicCompanyService.enable(id);
        return R.ok();
    }

    @PostMapping("/importTemp")
    @ApiOperation(value = "获取导入模板")
    public void download2Oss(HttpServletResponse response, HttpServletRequest request) {
        try {
            ClassPathResource classPathResource = new ClassPathResource("importTemp/分公司.xls");
            InputStream inputStream = classPathResource.getInputStream();
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, "分公司.xls");
            ServletOutputStream outputStream = response.getOutputStream();
            IOUtils.copy(inputStream, outputStream);
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    public R<Void> upload(@RequestPart("file") MultipartFile file) throws IOException {
        if(file == null || file.isEmpty()){
            return R.fail("文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        if (null != originalFilename) {
            if (!(originalFilename.contains(".xlsx") || originalFilename.contains(".xls"))) {
                throw new ServiceException("导入失败！文件类型错误");
            }
        }
        basicCompanyService.upload(file.getInputStream());
        return R.ok();
    }
}

