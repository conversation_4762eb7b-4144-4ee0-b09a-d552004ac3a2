package com.jri.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.domain.convert.CustomerBankCommonConvert;
import com.jri.biz.domain.entity.CustomerBankCommon;
import com.jri.biz.domain.request.CustomerBankCommonForm;
import com.jri.biz.domain.request.CustomerBankCommonQuery;
import com.jri.biz.domain.vo.CustomerBankCommonListVO;
import com.jri.biz.domain.vo.CustomerBankCommonVO;
import com.jri.biz.mapper.CustomerBankCommonMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 一般户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Service
public class CustomerBankCommonService extends ServiceImpl<CustomerBankCommonMapper, CustomerBankCommon> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerBankCommonListVO> listPage(CustomerBankCommonQuery query) {
        var page = new Page<CustomerBankCommonListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CustomerBankCommonVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CustomerBankCommonForm form) {
        // todo 完善新增/更新逻辑
        CustomerBankCommon customerBankCommon = CustomerBankCommonConvert.INSTANCE.convert(form);
        return save(customerBankCommon);
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerBankCommonForm form) {
        // todo 完善新增/更新逻辑
        CustomerBankCommon customerBankCommon = CustomerBankCommonConvert.INSTANCE.convert(form);
        return updateById(customerBankCommon);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeByBankId(Long bankId) {
        getBaseMapper().removeByBankId(bankId);
    }
}
