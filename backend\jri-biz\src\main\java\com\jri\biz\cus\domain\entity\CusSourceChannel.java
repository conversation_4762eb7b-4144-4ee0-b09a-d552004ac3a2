package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 客资来源 渠道 关联
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_source_channel")
@ApiModel(value = "CusSourceChannel对象", description = "客资来源 渠道 关联")
public class CusSourceChannel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "main_id", type = IdType.NONE)
    @ApiModelProperty("主表id")
    private Long mainId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("手机号")
    private String phone;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("银行账户")
    private String bankAccount;

}
