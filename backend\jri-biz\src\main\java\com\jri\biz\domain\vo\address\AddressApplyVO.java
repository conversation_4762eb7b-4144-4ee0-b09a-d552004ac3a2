package com.jri.biz.domain.vo.address;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 地址申请视图对象
 *
 * <AUTHOR>
 * @since 2023-12-01
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "AddressApplyVO视图对象")
public class AddressApplyVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("托管分类")
    private String hostingType;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("房本id")
    private Long propertyId;

    @ApiModelProperty("房本名称")
    private String propertyName;

    @ApiModelProperty("账单id")
    private Long paymentId;

    @ApiModelProperty("账单编号")
    private String paymentNo;

    @ApiModelProperty(value = "账款金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "账期开始时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date paymentStartTime;

    @ApiModelProperty(value = "账期结束时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date paymentEndTime;

    @ApiModelProperty("审批状态")
    private String status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("区域祖级列表")
    private String areaAncestors;

    @ApiModelProperty("图片附件")
    private List<CommonBizFile> imageFileList;

    @ApiModelProperty("文件附件")
    private List<CommonBizFile> fileList;

}