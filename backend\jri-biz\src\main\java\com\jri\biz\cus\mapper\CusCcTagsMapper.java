package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusCcTags;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusCcTagsListVO;
import com.jri.biz.cus.domain.vo.CusCcTagsVO;
import com.jri.biz.cus.domain.request.CusCcTagsQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 线索/客户标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface CusCcTagsMapper extends BaseMapper<CusCcTags> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusCcTagsListVO> listPage(@Param("query") CusCcTagsQuery query, Page<CusCcTagsListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCcTagsVO getDetailById(@Param("id") Long id);

    List<String> getListByCcId(@Param("ccId") Long ccId);

    List<CusCcTags> getTagsListByCcId(@Param("ccId") Long ccId);
}
