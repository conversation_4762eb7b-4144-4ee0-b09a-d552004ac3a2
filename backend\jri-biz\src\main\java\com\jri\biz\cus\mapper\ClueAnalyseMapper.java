package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.request.ClueAnalyseQuery;
import com.jri.biz.cus.domain.vo.BaseClueSourceAnalyseVO;
import com.jri.biz.cus.domain.vo.BaseStaffSourceAnalyseVO;
import com.jri.biz.cus.domain.vo.ClueSourceCustomerIntroductionAnalyseVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/2 15:35
 */
@Component
public interface ClueAnalyseMapper {

    /**
     * 基础来源分页查询。
     *
     * @param query 包含搜索查询参数的对象
     * @param page  表示分页参数的页面对象
     * @return 匹配查询参数的BaseClueSourceAnalyseVO对象的分页信息
     */
    IPage<BaseClueSourceAnalyseVO> baseSourcePage(@Param("query") ClueAnalyseQuery query, Page<BaseClueSourceAnalyseVO> page);

    /**
     * 客户介绍来源分页查询
     *
     * @param query 包含搜索查询参数的对象
     * @param page  表示分页参数的页面对象
     * @return 分页信息
     */
    IPage<ClueSourceCustomerIntroductionAnalyseVO> CustomerIntroductionSourcePage(@Param("query") ClueAnalyseQuery query, Page<ClueSourceCustomerIntroductionAnalyseVO> page);


    /**
     * 线索数量查询
     *
     * @param sourceId    来源id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param companyFlag 查询转换成公司标志
     * @param isSea       是否在公海
     * @return 线索数量
     */
    Long clueCount(@Param("sourceId") Long sourceId,
                   @Param("startTime") LocalDateTime startTime,
                   @Param("endTime") LocalDateTime endTime,
                   @Param("userId") Long userId,
                   @Param("biz") String biz,
                   @Param("isSea") String isSea,
                   @Param("companyFlag") Boolean companyFlag);

    /**
     * 查询实际成交金额总和(gmv)
     *
     * @param sourceId              来源id
     * @param startTime             开始时间
     * @param endTime               结束时间
     * @param isSea                 是否在公海
     * @param actualAmountColumn    实际成交金额查询列名
     * @return 金额
     */
    BigDecimal getGMV(@Param("sourceId") Long sourceId,
                      @Param("startTime") LocalDateTime startTime,
                      @Param("endTime") LocalDateTime endTime,
                      @Param("userId") Long userId,
                      @Param("biz") String biz,
                      @Param("isSea") String isSea,
                      @Param("actual_amount_column") String actualAmountColumn);

    /**
     * 线索数量查询
     *
     * @param customerId  来源id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param companyFlag 查询转换成公司标志
     * @return 线索数量
     */
    Long customerIntroductionClueCount(@Param("customerId") Long customerId,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime,
                                       @Param("companyFlag") Boolean companyFlag);

    /**
     * 查询实际成交金额总和(gmv)
     *
     * @param customerId            来源id
     * @param startTime             开始时间
     * @param endTime               结束时间
     * @param actualAmountColumn    实际成交金额查询列名
     * @return 金额
     */
    BigDecimal getCustomerIntroductionGMV(@Param("customerId") Long customerId,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime,
                                          @Param("actual_amount_column") String actualAmountColumn);

    /**
     * 客户介绍总数查询
     *
     * @param customerId 客户id
     * @return 数量
     */
    Long customerIntroductionCount(@Param("customerId") Long customerId);

    /**
     * 根据来源查询线索的数量 用于成本计算
     *
     * @param sourceId  来源id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param isSea     是否在公海
     * @return 线索数量
     */
    Long countClueBySourceId(@Param("sourceId") Long sourceId,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime,
                             @Param("isSea") String isSea);

    /**
     * 基础人员&来源分页查询。
     *
     * @param query 包含搜索查询参数的对象
     * @param page  表示分页参数的页面对象
     * @return 分页信息
     */
    IPage<BaseStaffSourceAnalyseVO> baseStaffSourcePage(@Param("query") ClueAnalyseQuery query, Page<BaseStaffSourceAnalyseVO> page);


    /**
     * 根据条件获取客资
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userId    用户id
     * @param sourceId  来源id
     * @param sourceBiz 来源业务
     * @param isSea     是否在公海
     * @return 客资列表
     */
    List<CusCustomerOrClue> getClueList(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime,
                                        @Param("userId") Long userId,
                                        @Param("sourceId") Long sourceId,
                                        @Param("sourceBiz") String sourceBiz,
                                        @Param("isSea") String isSea);

    /**
     * 总成交天数查询
     *
     * @param userId    用户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 天数
     */
    Long selectClueDealDays(@Param("userId") Long userId,
                            @Param("startTime") LocalDateTime startTime,
                            @Param("endTime") LocalDateTime endTime);


    /**
     * 平台申诉数量统计
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param userId       用户id
     * @param sourceId     来源id
     * @param isSea        是否在公海
     * @param appealStatus 审核状态
     * @return 数量
     */
    Long platformAppealCount(@Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime,
                             @Param("userId") Long userId,
                             @Param("sourceId") Long sourceId,
                             @Param("isSea") String isSea,
                             @Param("appealStatus") String appealStatus);


}
