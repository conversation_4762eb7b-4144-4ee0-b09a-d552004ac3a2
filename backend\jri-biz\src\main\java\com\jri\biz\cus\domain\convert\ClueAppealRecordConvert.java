package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.ClueAppealRecord;
import com.jri.biz.cus.domain.request.ClueAppealRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 线索申诉记录表对象转换
 *
 * <AUTHOR>
 * @since 2024-01-10
 */

@Mapper
public interface ClueAppealRecordConvert {
    ClueAppealRecordConvert INSTANCE = Mappers.getMapper(ClueAppealRecordConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    ClueAppealRecord convert(ClueAppealRecordForm form);

}