package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 公海配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_sea")
@ApiModel(value = "CusSea对象", description = "公海配置")
public class CusSea implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 公海类型0-线索公海1-客户公海
     */
    private String type;

    /**
     * 公海名称
     */
    private String name;

    /**
     * 公海管理员id
     */
    private Long manageId;

    /**
     * 掉保时长
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer duration;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资源分配规则0-员工领取1-仅管理员分配2-员工领取+管理员分配
     */
    private String rule;

    /**
     * 状态0-停用1-正常
     */
    private String status;

    /**
     * 回收提醒提前天数
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer recovery;

    /**
     * 部门id列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> deptIds;
}
