package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusBusinessBiz;
import com.jri.biz.cus.domain.request.CusBusinessBizForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机业务关系对象转换
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Mapper
public interface CusBusinessBizConvert {
    CusBusinessBizConvert INSTANCE = Mappers.getMapper(CusBusinessBizConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusBusinessBiz convert(CusBusinessBizForm form);

}