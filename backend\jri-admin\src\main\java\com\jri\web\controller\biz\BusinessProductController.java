package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.entity.BusinessProduct;
import com.jri.biz.domain.entity.BusinessProductActivity;
import com.jri.biz.domain.request.BusinessProductActivityQuery;
import com.jri.biz.domain.request.BusinessProductForm;
import com.jri.biz.domain.request.BusinessProductQuery;
import com.jri.biz.domain.vo.BusinessProductListVO;
import com.jri.biz.service.BusinessProductService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Validated
@RestController
@RequestMapping("/businessProduct")
@Api(tags = "产品")
public class BusinessProductController {
    @Resource
    private BusinessProductService businessProductService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<BusinessProductListVO>> listPage(BusinessProductQuery query) {
        return R.ok(businessProductService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BusinessProduct> getDetailById(@RequestParam("id") Long id) {
        return R.ok(businessProductService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid BusinessProductForm form) {
        return R.ok(businessProductService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid BusinessProductForm form) {
        return R.ok(businessProductService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(businessProductService.deleteById(id));
    }

    @PostMapping("/enable")
    @ApiOperation("活动价状态设置")
    public R<Void> enable(@RequestParam("activityId") Long activityId) {
        businessProductService.enable(activityId);
        return R.ok();
    }

    @GetMapping("/getActivityListByProductId")
    @ApiOperation("根据产品id查询启用的活动列表")
    public R<IPage<BusinessProductActivity>> getActivityListByProductId(BusinessProductActivityQuery query) {
        return R.ok(businessProductService.getActivityListByProductId(query));
    }
}

