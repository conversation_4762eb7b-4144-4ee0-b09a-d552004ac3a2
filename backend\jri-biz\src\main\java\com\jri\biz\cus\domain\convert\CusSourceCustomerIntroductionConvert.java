package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourceCustomerIntroduction;
import com.jri.biz.cus.domain.request.CusSourceCustomerIntroductionForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客资来源 客户介绍 关联对象转换
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Mapper
public interface CusSourceCustomerIntroductionConvert {
    CusSourceCustomerIntroductionConvert INSTANCE = Mappers.getMapper(CusSourceCustomerIntroductionConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusSourceCustomerIntroduction convert(CusSourceCustomerIntroductionForm form);

}