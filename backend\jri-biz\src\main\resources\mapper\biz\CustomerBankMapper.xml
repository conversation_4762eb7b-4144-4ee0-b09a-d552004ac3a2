<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerBankMapper">
    <delete id="deleteByIdAndUpdateBy" >
        update customer_bank set is_deleted='1',update_by=#{updateBy} where bank_id= #{id}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerBankListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerBankVO">
        select * from customer_bank where is_deleted=0 and bank_id= #{id}
    </select>
    <select id="getDetailByCiId" resultType="com.jri.biz.domain.vo.CustomerBankVO">
        select * from customer_bank where is_deleted=0 and ci_id=#{ciId} limit 1
    </select>
</mapper>
