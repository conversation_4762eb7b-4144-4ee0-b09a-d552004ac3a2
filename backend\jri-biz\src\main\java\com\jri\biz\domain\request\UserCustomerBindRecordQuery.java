package com.jri.biz.domain.request;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户客户绑定记录查询类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="用户客户绑定记录查询对象")
public class UserCustomerBindRecordQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "绑定状态")
    private String bindStatus;

}
