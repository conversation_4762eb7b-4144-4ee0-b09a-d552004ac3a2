package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerLicenseForm;
import com.jri.biz.domain.request.CustomerLicenseQuery;
import com.jri.biz.domain.request.LicenseSaveBatchForm;
import com.jri.biz.domain.vo.CustomerLicenseListVO;
import com.jri.biz.domain.vo.CustomerLicenseVO;
import com.jri.biz.service.CustomerLicenseService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerLicense")
@Api(tags = "客户许可信息")
public class CustomerLicenseController {

    @Resource
    private CustomerLicenseService customerLicenseService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerLicenseListVO>> listPage(CustomerLicenseQuery query) {
        return R.ok(customerLicenseService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerLicenseVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerLicenseService.getDetailById(id));
    }

    @GetMapping("/getByCiId")
    @ApiOperation("详情")
    public R<List<CustomerLicenseVO>> getDetailByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerLicenseService.getDetailByCiId(ciId));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Long> save(@RequestBody @Valid CustomerLicenseForm form) {
        return R.ok(customerLicenseService.addOrUpdate(form));
    }

    @PostMapping("/saveBatch")
    @ApiOperation("批量保存数据")
    public R<Boolean> saveBatch(@RequestBody @Valid LicenseSaveBatchForm form) {
        return R.ok(customerLicenseService.addOrUpdateBatch(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerLicenseService.deleteById(id));
    }

}

