package com.jri.biz.domain.vo.kanban.license;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/9/5 9:25
 */
@Getter
@Setter
@ApiModel(value = "LicenseAnalysisVO", description = "办证数量统计")
public class LicenseAnalysisVO {

    @ApiModelProperty("办证数量")
    private Long license;

    @ApiModelProperty("单注册")
    private Long singleRegistration;

    @ApiModelProperty("内资注册")
    private Long domesticBusinessRegistration;

    @ApiModelProperty("外资注册")
    private Long foreignBusinessRegistration;

    @ApiModelProperty("跨区变更")
    private Long regionModification;

    @ApiModelProperty("工商变更")
    private Long businessModification;

    @ApiModelProperty("注销")
    private Long cancellation;

    @ApiModelProperty("进出口经营权")
    private Long importAndExportLicense;

    @ApiModelProperty("劳务派遣许可证")
    private Long laborDispatchLicense;

    @ApiModelProperty("验资")
    private Long capitalVerificationLicense;

    @ApiModelProperty("食品经营许可证")
    private Long foodBusinessLicense;

    @ApiModelProperty("道路运输许可证")
    private Long transportationLicense;

    @ApiModelProperty("其它业务")
    private Long otherBusiness;

}
