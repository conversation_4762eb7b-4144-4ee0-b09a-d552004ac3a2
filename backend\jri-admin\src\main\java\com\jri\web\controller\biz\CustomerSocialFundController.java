package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerSocialFundForm;
import com.jri.biz.domain.request.CustomerSocialFundQuery;
import com.jri.biz.domain.vo.CustomerSocialFundListVO;
import com.jri.biz.domain.vo.CustomerSocialFundVO;
import com.jri.biz.service.CustomerSocialFundService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerSocialFund")
@Api(tags = "客户社保")
public class CustomerSocialFundController {
    @Resource
    private CustomerSocialFundService customerSocialFundService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerSocialFundListVO>> listPage(CustomerSocialFundQuery query) {
        return R.ok(customerSocialFundService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerSocialFundVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerSocialFundService.getDetailById(id));
    }

    @GetMapping("/getByCiId")
    @ApiOperation("详情")
    public R<CustomerSocialFundVO> getDetailByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerSocialFundService.getDetailByCiId(ciId));
    }


    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Long> saveOrUpdate(@RequestBody @Valid CustomerSocialFundForm form) {
        return R.ok(customerSocialFundService.saveOrUpdate(form));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CustomerSocialFundForm form) {
        return R.ok(customerSocialFundService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerSocialFundService.deleteById(id));
    }
}

