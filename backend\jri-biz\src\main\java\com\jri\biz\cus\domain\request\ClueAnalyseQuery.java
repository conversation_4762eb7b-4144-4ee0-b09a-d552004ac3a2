package com.jri.biz.cus.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.YearMonth;

/**
 * <AUTHOR>
 * @since 2024/1/2 15:13
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "线索分析查询")
public class ClueAnalyseQuery extends BaseQuery {

    @DateTimeFormat(pattern = "yyyy-MM")
    @ApiModelProperty(value = "分析月份")
    private YearMonth yearMonth;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "人员名称")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private String deptId;

    @JsonIgnore
    private String biz;

    @ApiModelProperty(value = "来源id")
    private Long sourceId;

}
