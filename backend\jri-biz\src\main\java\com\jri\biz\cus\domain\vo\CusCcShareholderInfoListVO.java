package com.jri.biz.cus.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 股东信息视图列表对象
 *
 * <AUTHOR>
 * @since 2023-11-08
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCcShareholderInfoListVO视图列表对象")
public class CusCcShareholderInfoListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}