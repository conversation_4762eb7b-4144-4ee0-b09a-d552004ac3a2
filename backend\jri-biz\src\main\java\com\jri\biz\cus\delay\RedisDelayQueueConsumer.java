package com.jri.biz.cus.delay;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jri.biz.cus.delay.handler.RedisDelayQueueHandler;
import com.jri.biz.cus.delay.util.RedisDelayQueueUtil;
import com.jri.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.boot.CommandLineRunner;

import javax.annotation.Resource;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Slf4j
//@Component
public class RedisDelayQueueConsumer implements CommandLineRunner {

    ThreadPoolExecutor executorService = new ThreadPoolExecutor(3, 5, 30, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000), new ThreadFactoryBuilder().setNameFormat("redis-delay-%d").build());
    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Override
    public void run(String... args) {
        new Thread(() -> {
            while (true) {
                try {
                    RedisDelayQueueEnum[] queueEnums = RedisDelayQueueEnum.values();
                    for (RedisDelayQueueEnum queueEnum : queueEnums) {

                        Object value = redisDelayQueueUtil.getDelayQueue(queueEnum.getCode());
                        if (value != null) {

                            RedisDelayQueueHandler<Object> redisDelayQueueHandler = SpringUtils.getBean(queueEnum.getBeanId());
                            executorService.execute(() -> redisDelayQueueHandler.execute(value));
                        }
                    }
                } catch (NoSuchBeanDefinitionException e) {
                    log.error("Redisson延迟队列 NoSuchBeanDefinitionException {}", e.getMessage());
                } catch (InterruptedException e) {
                    log.error("Redisson延迟队列监测异常中断 {}", e.getMessage());
                }
            }
        }).start();
        log.info("Redisson延迟队列监测启动成功");
    }
}
