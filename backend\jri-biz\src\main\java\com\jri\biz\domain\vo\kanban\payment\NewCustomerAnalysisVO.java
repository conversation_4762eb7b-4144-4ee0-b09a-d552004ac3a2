package com.jri.biz.domain.vo.kanban.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/9/4 13:49
 */
@Getter
@Setter
@ApiModel(value = "NewCustomerVO", description = "新增户数")
public class NewCustomerAnalysisVO {

    @ApiModelProperty("记账客户")
    private Long bookkeeping;

    @ApiModelProperty("办证客户")
    private Long license;

    @ApiModelProperty("不收费客户")
    private Long free;

    @ApiModelProperty("其他客户")
    private Long other;

}
