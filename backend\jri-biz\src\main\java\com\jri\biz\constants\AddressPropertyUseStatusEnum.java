package com.jri.biz.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2024/6/4 14:51
 *
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AddressPropertyUseStatusEnum {

    /**
     * 闲置
     */
    IDLE("idle"),

    /**
     * 已到期
     */
    EXPIRED("expired"),

    /**
     * 使用中
     */
    IN_USE("in_use");

    private String status;

}
