package com.jri.biz.cus.delay.util;

import com.jri.common.utils.StringUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;


@Slf4j
@Component
public class RedisDelayQueueUtil {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 添加延迟队列
     *
     * @param value     队列值
     * @param delay     延迟时间
     * @param timeUnit  时间单位
     * @param queueCode 队列键
     */
    public <T> void addDelayQueue(T value, long delay, TimeUnit timeUnit, String queueCode) {
        try {
            RBlockingDeque<T> blockingDeque = redissonClient.getBlockingDeque(queueCode);
            RDelayedQueue<T> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
            delayedQueue.offer(value, delay, timeUnit);
            log.info("(添加延时队列成功) 队列键：{}，队列值：{}，延迟时间：{}", queueCode, value, timeUnit.toSeconds(delay) + "秒");
        } catch (Exception e) {
            log.error("(添加延时队列失败) {}", e.getMessage());
            throw new RuntimeException("(添加延时队列失败)");
        }
    }

    /**
     * 获取延迟队列
     *
     * @param queueCode 队列键
     * @return 对象
     * @throws InterruptedException 中断
     */
    public <T> T getDelayQueue(@NonNull String queueCode) throws InterruptedException {
        try {
            if (!redissonClient.isShutdown()) {
                RBlockingDeque<T> blockingDeque = redissonClient.getBlockingDeque(queueCode);
                redissonClient.getDelayedQueue(blockingDeque);
                return blockingDeque.poll();
            }
        } catch (Exception e) {
            log.error("(获取延迟队列失败) {}", e.getMessage());
        }
        return null;
    }

    /**
     * 删除指定队列中的消息
     *
     * @param object  删除对象
     * @param queueCode 指定队列键
     * @return 布尔值
     */
    public <T> boolean removeDelayedQueue(T object, String queueCode) {
        if (StringUtils.isBlank(queueCode)) {
            return false;
        }
        RBlockingDeque<T> blockingDeque = redissonClient.getBlockingDeque(queueCode);
        RDelayedQueue<T> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);

        boolean flag = delayedQueue.remove(object);
        if (flag) {
            log.info("删除延时队列成功， 删除信息:{}，队列名称：{}", object, queueCode);
        }
        return flag;
    }

    /**
     * 订阅阻塞队列(可订阅所有实现类 例如: 延迟 优先 有界 等)
     */
    public <T> void subscribeBlockingQueue(String queueName, Consumer<T> consumer, boolean isDelayed) {
        RBlockingQueue<T> queue = redissonClient.getBlockingQueue(queueName);
        if (isDelayed) {
            // 订阅延迟队列
            redissonClient.getDelayedQueue(queue);
        }
        queue.subscribeOnElements(consumer);
    }
}
