<!--
 * @Description:  变更经理
 * @Author: thb
 * @Date: 2023-06-01 10:14:48
 * @LastEditTime: 2024-01-30 14:47:51
 * @LastEditors: thb
-->
<template>
  <el-dialog
    class="abandon-dialog"
    align-center
    :close-on-click-modal="false"
    title="变更财税顾问"
    v-model="visible"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-form-item label="财税顾问" prop="manger">
        <!-- <el-select v-model="formData.manger" placeholder="请选择" clearable>
          <el-option :label="item.nickName" :value="item.nickName" v-for="(item, index) in userList" :key="index" />
        </el-select> -->
        <SelectTree v-model="formData.manger" filterable placeholder="请选择" clearable @on-node-click="handleChangeManger" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit(formRef)">确认变更</el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { changeCustomerManager } from '@/api/customer/file'
// import { listUser } from '@/api/system/user'
import SelectTree from '@/components/SelectTree'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number
})

const formData = ref({})

const rules = {
  manger: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ]
}
const { proxy } = getCurrentInstance()
const formRef = ref()
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      const data = await changeCustomerManager({
        ...formData.value,
        customerId: props.id
      })
      if (data.code === 200) {
        proxy.$modal.msgSuccess(`变更成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`变更失败!`)
      }
    } else {
    }
  })
}

// const userList = ref([])
// const getUserList = async () => {
//   const result = await listUser()
//   userList.value = result.rows || []
// }
// getUserList()

const parentNameSearch = () => {
  let nameStr = []
  const searchNameByNode = node => {
    // nameStr = nameStr + '/' + node.data.label
    if (node.level === 1) {
      return nameStr
    } else {
      nameStr.push(node.data.label)
    }
    return searchNameByNode(node.parent)
  }
  return searchNameByNode
}
const handleChangeManger = (node, node1) => {
  const names = parentNameSearch()(node1)
  nextTick(() => {
    formData.value.mangerUserId = node.id
    formData.value.manger = names.reverse().join('/')
  })
}
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
