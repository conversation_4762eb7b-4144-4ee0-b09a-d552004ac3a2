package com.jri.biz.domain.request;

import com.jri.biz.domain.entity.BizFlowNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 流程 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-06-16
 */

@Data
@NoArgsConstructor
@ApiModel(value="流程表单请求对象")
public class BizFlowForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 流程名称
     */
    @NotBlank(message = "流程名称不能为空")
    private String name;

    /**
     * 合同类型0-记账合同1-一次性合同2-地址服务协议合同
     */
    private String contractType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 类型0-模板审批流程1-合同借阅2-合同审批
     */
    @NotBlank(message = "类型不能为空")
    private String type;

    /**
     * 使用状态0-关闭1-启用
     */
    private String enable;

    private String xmlStr;

    private String deptIds;

    @ApiModelProperty("节点列表")
    List<BizFlowNode> nodeList;
}
