package com.jri.biz.domain.common;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "CustomerAgent对象", description = "客户业务员和部门对象")
public class CustomerAgent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "2级部门/名字")
    private String deptAndName;


    @ApiModelProperty(value = "4种人对应2级部门id")
    private String deptIdArry;
}
