package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel(value="联系人批量保存表单请求对象")
public class CusCcContactBatchForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("联系人列表")
    private List<CusCcContactForm> list;

    @ApiModelProperty("线索/客户id")
    @NotNull(message = "客户id不能为空")
    private Long ccId;
}
