package com.jri.biz.domain.vo;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单记录视图列表对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="OrderRecordListVO视图列表对象")
public class OrderRecordListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 操作记录名称
     */
    @ApiModelProperty("操作记录名称")
    private String recordName;

    /**
     * 指派给
     */
    @ApiModelProperty("指派给")
    private Long executor;

    /**
     * 指派人 姓名
     */
    @ApiModelProperty("指派人 姓名")
    private String executorName;

    /**
     * 工单id
     */
    @ApiModelProperty("工单id")
    private Long orderId;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}