package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 商机业务关系查询类
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="商机业务关系查询对象")
public class CusBusinessBizQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
