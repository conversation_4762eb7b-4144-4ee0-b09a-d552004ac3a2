package com.jri.biz.domain.entity.job;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("contract_alert_job")
@ApiModel(value = "ContractAlterJob对象", description = "")
public class ContractAlterJob implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合同预警id")
    @TableId(value = "contract_alert_id",type= IdType.ASSIGN_ID)
    private Long contractAlertId;

    @ApiModelProperty("配置名称")
    private String name;

    @ApiModelProperty("预警时间")
    private Integer alertDate;

    @ApiModelProperty("状态0正常 1停用")
    private String alertStatus;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("通知方式 0-站内消息 1-短信")
    private String notificationMethod;

    @ApiModelProperty("通知发送时间")
    private String sendTime;

    @ApiModelProperty("消息模板")
    private String messageTemplate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;


}
