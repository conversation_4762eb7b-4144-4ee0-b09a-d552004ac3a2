package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.OrderType;
import com.jri.biz.domain.request.OrderTypeForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 工单类型对象转换
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Mapper
public interface OrderTypeConvert {
    OrderTypeConvert INSTANCE = Mappers.getMapper(OrderTypeConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    OrderType convert(OrderTypeForm form);

}