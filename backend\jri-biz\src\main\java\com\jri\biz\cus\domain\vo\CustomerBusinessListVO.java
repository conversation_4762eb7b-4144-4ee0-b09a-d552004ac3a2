package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerBusinessListVO视图列表对象")
public class CustomerBusinessListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商机名称")
    private String name;

    @ApiModelProperty("销售阶段")
    private String stage;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("预计成交时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("预计成交金额")
    private String expectAmount;

    @ApiModelProperty("实际成交金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("赢单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime winTime;
}