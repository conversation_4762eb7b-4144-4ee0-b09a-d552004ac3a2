package com.jri.biz.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 一般户信息视图列表对象
 *
 * <AUTHOR>
 * @since 2023-07-13
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerBankCommonListVO视图列表对象")
public class CustomerBankCommonListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}