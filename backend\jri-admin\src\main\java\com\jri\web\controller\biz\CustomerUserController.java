package com.jri.web.controller.biz;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerContractQuery;
import com.jri.biz.domain.request.CustomerInformationQuery;
import com.jri.biz.domain.request.license.LicenseBizTaskQuery;
import com.jri.biz.domain.vo.customerUser.UserCustomerContractListVO;
import com.jri.biz.domain.vo.customerUser.UserCustomerInformationListVO;
import com.jri.biz.domain.vo.license.UserCustomerLicenseBizTaskListVO;
import com.jri.biz.service.contract.ContractSignService;
import com.jri.biz.service.customerUser.CustomerUserService;
import com.jri.biz.service.license.LicenseBizTaskService;
import com.jri.common.core.domain.R;
import com.jri.common.core.domain.entity.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/1/25 14:30
 */
@Validated
@RestController
@RequestMapping("/customerUser")
@Api(tags = "企业用户")
public class CustomerUserController {

    @Resource
    private CustomerUserService customerUserService;

    @Resource
    private ContractSignService contractSignService;

    @Resource
    private LicenseBizTaskService licenseBizTaskService;

    @GetMapping("/customerList")
    @ApiOperation("用户绑定企业列表查询")
    public R<IPage<UserCustomerInformationListVO>> customerList(CustomerInformationQuery query){
        return R.ok(customerUserService.customerList(query));
    }

    @GetMapping("/contractList")
    @ApiOperation("查询需要签名的合同")
    public R<IPage<UserCustomerContractListVO>> contractList(CustomerContractQuery query){
        return R.ok(customerUserService.contractList(query));
    }

    @GetMapping("/counselorInfo")
    @ApiOperation("获取财税顾问信息")
    public R<SysUser> counselorInfo(){
        return R.ok(customerUserService.counselorInfo());
    }

    @PostMapping("/sign")
    @ApiOperation("合同签名")
    public R<Void> sign(@RequestParam("contractId") Long contractId,
                        @RequestBody MultipartFile file) {
        contractSignService.sign(contractId, file);
        return R.ok();
    }

    @GetMapping("/licenseBizTaskList")
    @ApiOperation("工商办证列表查询")
    public R<IPage<UserCustomerLicenseBizTaskListVO>> licenseBizTaskList(LicenseBizTaskQuery query) {
        IPage<UserCustomerLicenseBizTaskListVO> result = customerUserService.licenseBizTaskList(query);
        return R.ok(result);
    }

}
