package com.jri.web.controller.biz;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.entity.CustomerFollow;
import com.jri.biz.domain.request.CustomerFollowQuery;
import com.jri.biz.service.CustomerFollowService;
import com.jri.common.annotation.Log;
import com.jri.common.core.controller.BaseController;
import com.jri.common.core.domain.R;
import com.jri.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 跟进记录Controller
 *
 * <AUTHOR>
 * @date 2023-06-04
 */
@RestController
@Api(tags = "客户跟进记录")
@RequestMapping("/follow")
public class CustomerFollowController extends BaseController
{
    @Autowired
    private CustomerFollowService customerFollowService;

    /**
     * 查询跟进记录列表
     */
    ////@PreAuthorize("@ss.hasPermi('system:follow:list')")
    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerFollow>> listPage(CustomerFollowQuery query)
    {
        return R.ok(customerFollowService.listPage(query));
    }


    /**
     * 获取跟进记录详细信息
     */
    ////@PreAuthorize("@ss.hasPermi('system:follow:query')")
    @GetMapping( "/getById")
    @ApiOperation("通过Id获取详情")
    public R<CustomerFollow> getInfo(@RequestParam("id") Long id)
    {
        return R.ok(customerFollowService.selectCustomerFollowById(id));
    }

//    /**
//     * 获取跟进记录详细信息
//     */
//    ////@PreAuthorize("@ss.hasPermi('system:follow:query')")
//    @GetMapping(value = "/{getByCiId}")
//    public AjaxResult getInfoByCiId(@PathVariable("id") Long id)
//    {
//        return success(customerFollowService.selectCustomerFollowById(id));
//    }

    /**
     * 新增跟进记录
     */
    ////@PreAuthorize("@ss.hasPermi('system:follow:add')")
    @Log(title = "跟进记录", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @ApiOperation("保存或更新数据")
    public R<Boolean> save(@RequestBody CustomerFollow customerFollow)
    {
        return R.ok(customerFollowService.addOrUpdate(customerFollow));
    }

    /**
     * 修改跟进记录
     */
    ////@PreAuthorize("@ss.hasPermi('system:follow:edit')")
//    @Log(title = "跟进记录", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody CustomerFollow customerFollow)
//    {
//        return toAjax(customerFollowService.updateCustomerFollow(customerFollow));
//    }

    /**
     * 删除跟进记录
     */
    ////@PreAuthorize("@ss.hasPermi('system:follow:remove')")
    @Log(title = "跟进记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id)
    {
        return R.ok(customerFollowService.deleteCustomerFollowById(id));
    }
}
