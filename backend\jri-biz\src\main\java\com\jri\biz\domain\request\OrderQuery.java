package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 工单查询类
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="工单查询对象")
public class OrderQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @ApiModelProperty("工单标题")
    private String orderTitle;

    @ApiModelProperty("工单编号")
    private String orderNo;

    @ApiModelProperty("紧急状态0-一般 1-紧急")
    private String isUrgent;

    @ApiModelProperty("工单状态0-待完成 1-完成 2-回退")
    private String orderStatus;

    private Long userId;

    @ApiModelProperty("工单类型id")
    private Long orderTypeId;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("排序列 紧急状态-is_urgent 不限排序-no_limit")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("预计完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("待派工")
    private Boolean noExecutorFlag;

    @ApiModelProperty("指派给")
    private Long executor;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("是否超时")
    private Boolean timeoutFlag;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private Date completeTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private Date completeTimeEnd;

}
