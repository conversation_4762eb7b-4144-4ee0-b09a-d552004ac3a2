<template>
  <el-dialog align-center title="注销办理" width="800" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <el-steps direction="vertical" style="max-width: 600px" :active="active" finish-status="success">
      <el-step title="">
        <template #title>
          <div class="title-flex">
            <span class="title">新建合同</span>
            <span class="time">{{ detail.contractTime }}</span>
          </div>
        </template>
        <template #description>
          <p>
            {{ detail.contractNo }}
          </p>
          <p v-if="detail.contractUserName">操作人：{{ detail.contractUserName }}</p>
        </template>
      </el-step>
      <el-step>
        <template #title>
          <div class="title-flex">
            <span class="title">税务注销</span>
            <span class="time">{{ detail.orderTime }}</span>
          </div>
        </template>
        <template #description>
          <p>
            {{ detail.orderNo }}
          </p>
          <p v-if="detail.orderUserName">当前指派给：{{ detail.orderUserName }}</p>
        </template>
      </el-step>
      <el-step>
        <template #title>
          <div class="title-flex">
            <span class="title">工商注销</span>
            <span class="time">{{ detail.taskTime }}</span>
          </div>
        </template>
        <template #description>
          <p>
            {{ detail.taskNo }}
          </p>
          <p v-if="detail.taskUserName">当前办理人：{{ detail.taskUserName }}</p>
        </template>
      </el-step>
    </el-steps>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { getCancelProgressById } from '@/api/customer/file'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}
const props = defineProps({
  id: String
})

const list = ref([
  {
    name: '新建合同'
  },
  {
    name: '税务注销'
  },
  {
    name: '工商注销'
  }
])

const detail = ref({})
const active = ref(1)
const getProgress = async () => {
  const { data } = await getCancelProgressById(props.id)
  // list.value = data || []
  detail.value = data || {}
  if (data.contractTime) {
    active.value = 1
  }
  if (data.orderTime) {
    active.value = 2
  }

  if (data.taskTime) {
    active.value = 3
  }
  console.log('active', active.value)
}
getProgress()
</script>
<style lang="scss" scoped>
.title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title {
    font-size: 16px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #333333;
  }
  .time {
    font-size: 14px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #6a7697;
  }
}
p {
  font-size: 14px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400;
  color: #6a7697;
}
:deep(.el-step) {
  .el-step__head.is-success {
    color: #2383e7;
    border-color: #2383e7;
  }
}
</style>
