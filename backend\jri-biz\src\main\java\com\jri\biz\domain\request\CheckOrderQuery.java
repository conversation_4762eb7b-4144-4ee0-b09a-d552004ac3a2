package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "工单查询校验权限")
public class CheckOrderQuery extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @ApiModelProperty("工单标题")
    private String orderTitle;

    @ApiModelProperty("工单编号")
    private String orderNo;

    @ApiModelProperty("紧急状态0-一般 1-紧急")
    private String isUrgent;

    @ApiModelProperty("工单状态0-待完成 1-完成 2-回退 3-异常 4-超时")
    private String orderStatus;

    @ApiModelProperty("工单类型id")
    private Long orderTypeId;

    @ApiModelProperty("排序列 紧急状态-is_urgent 不限排序-no_limit")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("待派工")
    private Boolean noExecutorFlag;

    @ApiModelProperty("指派给")
    private Long executor;

    private List<String> orderStatusList;

    @ApiModelProperty("查询类型 all - 全部 myCreate - 我发起的 abnormal - 异常")
    private String queryType;

    private Long userId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("是否超时")
    private Boolean timeoutFlag;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private Date completeTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private Date completeTimeEnd;

}
