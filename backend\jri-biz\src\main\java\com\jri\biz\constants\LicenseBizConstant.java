package com.jri.biz.constants;

import static com.jri.biz.constants.LicenseBizStageEnum.*;

/**
 * 工商办证常量
 *
 * <AUTHOR>
 * @since 2023/8/21 10:52
 */
public class LicenseBizConstant {

    /**
     * 进度汇报类型-办证
     */
    public static final String LICENSE = "license";

    /**
     * 进度汇报类型-银行
     */
    public static final String BANK = "bank";

    /**
     * 进度汇报类型-许可证
     */
    public static final String PERMIT = "permit";

    /**
     * 进度汇报类型-工商注销
     */
    public static final String BUSINESS_CANCELLATION = "business_cancellation";

    /**
     * 进度汇报类型-工商变更
     */
    public static final String BUSINESS_CHANGE = "business_change";

    /**
     * 风险客户-流失清理
     */
    public static final String WORK_CLEAR = "work_clear";

    /**
     * 风险客户-正式停账
     */
    public static final String ACCOUNT_CLOSE = "account_close";


    public static class STAGE_NAME_LIST {

        /**
         * 工商办证
         */
        public static final LicenseBizStageEnum[] BUSINESS_REGISTRATION = {PENDING, DATA_COLLECTION, LICENSE_PROCESSING, INFO_UPLOADING, BANK_ACCOUNT_OPEN};

        /**
         * 工商注销
         */
        public static final LicenseBizStageEnum[] BUSINESS_CANCELLATION = {PENDING, LicenseBizStageEnum.BUSINESS_CANCELLATION, BANK_CANCELLATION};

        /**
         * 工商变更
         */
        public static final LicenseBizStageEnum[] BUSINESS_CHANGE = {PENDING, CHANGE_PROGRESS, DATA_UPDATES};

        /**
         * 许可证流程
         */
        public static final LicenseBizStageEnum[] PERMIT = {PENDING, DATA_COLLECTION, LICENSE_PROCESSING, INFO_UPLOADING};
    }

}
