package com.jri.biz.cus.domain.request;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 客资来源查询类
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="客资来源 直投 查询对象")
public class CusSourceDirectQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主表id")
    private Long mainId;

}
