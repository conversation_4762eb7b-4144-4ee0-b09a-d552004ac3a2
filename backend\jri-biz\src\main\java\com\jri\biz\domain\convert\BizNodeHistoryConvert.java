package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BizNodeHistory;
import com.jri.biz.domain.request.BizNodeHistoryForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 流程历史对象转换
 *
 * <AUTHOR>
 * @since 2023-07-04
 */

@Mapper
public interface BizNodeHistoryConvert {
    BizNodeHistoryConvert INSTANCE = Mappers.getMapper(BizNodeHistoryConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BizNodeHistory convert(BizNodeHistoryForm form);

}