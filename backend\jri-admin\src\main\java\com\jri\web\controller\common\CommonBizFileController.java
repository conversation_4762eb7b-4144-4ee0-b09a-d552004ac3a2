    package com.jri.web.controller.common;


import com.jri.biz.service.CommonBizFileService;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "文件业务信息", tags = "文件业务信息")
@RestController
@RequestMapping("/common-biz-file")
public class CommonBizFileController {

    @Resource
    CommonBizFileService commonBizFileService;

    @GetMapping("/selectByMainIdAndBizType")
    @ApiOperation(value = "查询附件信息")
    public R<List<CommonBizFile>> selectByMainIdAndBizType(Long bizId, String bizType) {
        return R.ok(commonBizFileService.selectByMainIdAndBizType(bizId,bizType));
    }

    @GetMapping("/selectList")
    @ApiOperation(value = "查询附件信息")
    public R<List<CommonBizFile>> selectList() {
        return R.ok(commonBizFileService.selectList());
    }

    @GetMapping("/selectOneByMainIdAndBizType")
    @ApiOperation(value = "查询附件信息")
    public R<CommonBizFile> selectOneByMainIdAndBizType(Long bizId, String bizType) {
        return R.ok(commonBizFileService.selectOneByMainIdAndBizType(bizId,bizType));
    }

    @DeleteMapping ("/deleteByMainIdAndBizType")
    @ApiOperation(value = "删除附件信息")
    public R<Void> deleteByMainIdAndBizType(Long bizId, String bizType) {
        commonBizFileService.deleteByMainIdAndBizType(bizId,bizType);
        return R.ok();
    }

    @PostMapping("/save")
    @ApiOperation(value = "存储附件信息")
    public R<Void> queryList(@RequestBody List<CommonBizFile> commonBizFiles) {
        commonBizFileService.saveBatch(commonBizFiles);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(commonBizFileService.removeById(id));
    }
}
