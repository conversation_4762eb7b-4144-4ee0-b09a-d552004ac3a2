package com.jri.biz.cus.domain.vo;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 保有量设置视图对象
 *
 * <AUTHOR>
 * @since 2023-08-22
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusSeaInventoryVO视图对象")
public class CusSeaInventoryVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("线索掉保时长")
    private Integer clueDuration;

    @ApiModelProperty("线索回收公海id")
    private Long clueSeaId;

    @ApiModelProperty("线索回收提醒提前天数")
    private Integer clueRecovery;

    @ApiModelProperty("线索员工私海上限")
    private Integer clueStaffNum;

    @ApiModelProperty("线索管理员私海上限")
    private Integer clueAdminNum;

    @ApiModelProperty("线索员工私海上限状态0-停用1-启用")
    private String clueStaffStatus;

    @ApiModelProperty("线索管理员私海上限状态0-停用1-启用")
    private String clueAdminStatus;

    @ApiModelProperty("客户掉保时长")
    private Integer cusDuration;

    @ApiModelProperty("客户回收公海id")
    private Long cusSeaId;

    @ApiModelProperty("客户回收提醒提前天数")
    private Integer cusRecovery;

    @ApiModelProperty("客户员工私海上限")
    private Integer cusStaffNum;

    @ApiModelProperty("客户管理员私海上限")
    private Integer cusAdminNum;

    @ApiModelProperty("客户员工私海上限状态0-停用1-启用")
    private String cusStaffStatus;

    @ApiModelProperty("客户管理员私海上限状态0-停用1-启用")
    private String cusAdminStatus;
}