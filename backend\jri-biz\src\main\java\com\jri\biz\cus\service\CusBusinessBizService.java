package com.jri.biz.cus.service;

import com.jri.biz.cus.domain.entity.CusBusinessBiz;
import com.jri.biz.cus.mapper.CusBusinessBizMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.vo.CusBusinessBizListVO;
import com.jri.biz.cus.domain.vo.CusBusinessBizVO;
import com.jri.biz.cus.domain.request.CusBusinessBizForm;
import com.jri.biz.cus.domain.request.CusBusinessBizQuery;
import com.jri.biz.cus.domain.convert.CusBusinessBizConvert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <p>
 * 商机业务关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Service
public class CusBusinessBizService extends ServiceImpl<CusBusinessBizMapper, CusBusinessBiz> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusBusinessBizListVO> listPage(CusBusinessBizQuery query) {
        var page = new Page<CusBusinessBizListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CusBusinessBizVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusBusinessBizForm form) {
        // todo 完善新增/更新逻辑
        CusBusinessBiz cusBusinessBiz = CusBusinessBizConvert.INSTANCE.convert(form);
        return save(cusBusinessBiz);
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CusBusinessBizForm form) {
        // todo 完善新增/更新逻辑
        CusBusinessBiz cusBusinessBiz = CusBusinessBizConvert.INSTANCE.convert(form);
        return updateById(cusBusinessBiz);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    /**
     * 查询客户所有业务
     *
     * @param ccId 客户id
     */
    public List<CusBusinessBizVO> getAllBiz(Long ccId) {
        return getBaseMapper().getAllBiz(ccId);
    }
}
