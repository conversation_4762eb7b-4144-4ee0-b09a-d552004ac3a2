package com.jri.biz.domain.request;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="跟进记录查询对象")
public class CustomerFollowQuery extends BaseQuery {


    @ApiModelProperty("客户信息主表id")
    @NotNull(message = "客户信息主表id不能为空")
    private Long ciId;

    @ApiModelProperty("查询关键词")
    private String text;
}
