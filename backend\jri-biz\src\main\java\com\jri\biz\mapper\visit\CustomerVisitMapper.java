package com.jri.biz.mapper.visit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.visit.CustomerVisit;
import com.jri.biz.domain.request.visit.CustomerVisitQuery;
import com.jri.biz.domain.vo.visit.CustomerVisitListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
public interface CustomerVisitMapper extends BaseMapper<CustomerVisit> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerVisitListVO> listPage(@Param("query") CustomerVisitQuery query, @Param("page") Page<CustomerVisitListVO> page);

    /**
     * 列表 导出用
     */
    List<CustomerVisitListVO> getList(@Param("query") CustomerVisitQuery query);

    CustomerVisitListVO getById(Long id);
}
