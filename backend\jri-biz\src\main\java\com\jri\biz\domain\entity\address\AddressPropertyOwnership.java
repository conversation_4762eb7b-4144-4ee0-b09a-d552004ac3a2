package com.jri.biz.domain.entity.address;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 房屋产权证明
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("address_property_ownership")
@ApiModel(value = "AddressPropertyOwnership对象", description = "房屋产权证明")
public class AddressPropertyOwnership implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;

    @ApiModelProperty("房本名称")
    private String name;

    @ApiModelProperty("房本分类")
    private String propertyCategory;

    @ApiModelProperty("领用状态")
    private String useStatus;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("租赁分类")
    private String leaseCategory;

    @ApiModelProperty("租赁开始时间")
    private LocalDate leaseStartDate;

    @ApiModelProperty("租赁结束时间")
    private LocalDate leaseEndDate;

    @ApiModelProperty("租赁价格")
    private BigDecimal leasePrice;

    @ApiModelProperty("面积")
    private String regin;

    @ApiModelProperty("产权人姓名")
    private String ownerName;

    @ApiModelProperty("产权人手机")
    private String ownerPhone;

    @ApiModelProperty("出租人id")
    private Long lessorId;

    @ApiModelProperty("特许行业")
    private String licensedIndustry;

    @ApiModelProperty("禁止行业")
    private String prohibitedIndustry;

    @ApiModelProperty("其他")
    private String other;

    @ApiModelProperty("托管公司分类")
    private String managementCompanyCategory;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;


}
