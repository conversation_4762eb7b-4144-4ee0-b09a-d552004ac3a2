package com.jri.biz.domain.request;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 客户信息修改记录 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-14
 */

@Data
@NoArgsConstructor
@ApiModel(value="客户信息修改记录表单请求对象")
public class CustomerChangeRecordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 客户信息id
     */
    private Long ciId;

    /**
     * 信息板块
     */
    private String infoSection;

    /**
     * 修改内容
     */
    private String content;
}
