package com.jri.web.controller.system;

import com.jri.common.constant.Constants;
import com.jri.common.core.domain.AjaxResult;
import com.jri.common.core.domain.entity.SysMenu;
import com.jri.common.core.domain.entity.SysUser;
import com.jri.common.core.domain.model.LoginBody;
import com.jri.common.utils.SecurityUtils;
import com.jri.framework.web.service.SysLoginService;
import com.jri.framework.web.service.SysPermissionService;
import com.jri.system.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 微信登录方法
     *
     * @param code code
     * @return 结果
     */
    @PostMapping("/wechat/login")
    public AjaxResult wechatLogin(@RequestParam("code") String code)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.wechatLogin(code);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 短信登录
     *
     * @param code code
     * @return 结果
     */
    @PostMapping("/sms/login")
    public AjaxResult wechatLogin(@RequestParam("phonenumber") String phonenumber, @RequestParam("code") String code)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.smsLogin(phonenumber,code);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 小程序登录方法(不需要验证码)
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/miniProgramLogin")
    public AjaxResult miniProgramLogin(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(), false);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
