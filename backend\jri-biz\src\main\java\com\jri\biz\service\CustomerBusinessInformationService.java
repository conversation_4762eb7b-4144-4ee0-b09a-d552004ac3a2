package com.jri.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.CustomerBusinessInformationConvert;
import com.jri.biz.domain.entity.CustomerBusinessInformation;
import com.jri.biz.domain.request.CustomerBusinessInformationForm;
import com.jri.biz.domain.request.CustomerBusinessInformationQuery;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.vo.CustomerBusinessInformationListVO;
import com.jri.biz.domain.vo.CustomerBusinessInformationVO;
import com.jri.biz.domain.vo.CustomerInformationVO;
import com.jri.biz.mapper.CustomerBusinessInformationMapper;
import com.jri.biz.mapper.CustomerInformationMapper;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerBusinessInformationService extends ServiceImpl<CustomerBusinessInformationMapper, CustomerBusinessInformation> {
    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    private CustomerBusinessInformationMapper customerBusinessInformationMapper;

    @Resource
    private CompletenessService completenessService;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerBusinessInformationListVO> listPage(CustomerBusinessInformationQuery query) {
        var page = new Page<CustomerBusinessInformationListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerBusinessInformationVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(CustomerBusinessInformationForm form) {
        CustomerBusinessInformation customerBusinessInformation = CustomerBusinessInformationConvert.INSTANCE.convert(form);
        //保存附件
        //附件 银行开户信息表 变更信息
        Long customerId = form.getCiId();
        Boolean coverFlag = form.getCoverFlag();
        // resolved: 移除已删除的BizType常量引用
        // commonBizFileService.fileSaveHandler(customerId, BizType.INDIVIDUAL_TAX_PASSWORD, form.getIndividualTaxPasswordFile(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.BUSINESS_LICENSE, form.getBusinessFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.REGISTRATION_INFORMATION, form.getRegistrationInformationFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.BUSINESS_CONSTITUTION, form.getBusinessConstitutionFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.SHAREHOLDER_COMMITTEE_RESSOLUTION, form.getShareholderCommitteeRessolutionFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.ADRESS, form.getAdressFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.IDENTITY_DOCUMENT, form.getIdentityDocumentFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.BUSINESS_CHANGE_INFO, form.getBusinessChangeInfoFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.BUSINESS_OTHER, form.getBusinessOtherFileList(), coverFlag);
        commonBizFileService.fileSaveHandler(customerId, BizType.BUSINESS_HANDOVER_DOCUMENT, form.getHandoverDocumentFileList(), coverFlag);

        String content = "编辑";
        if (form.getBusinessInformationId() == null) {
            content = "新增";
            customerBusinessInformation.setUpdateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }
        saveOrUpdate(customerBusinessInformation);
        customerChangeRecordService.sendChangeMessage(customerBusinessInformation.getCiId());
        CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
        changeRecordForm.setCiId(customerBusinessInformation.getCiId());
        changeRecordForm.setContent(content);
        changeRecordForm.setInfoSection("工商信息");
        customerChangeRecordService.add(changeRecordForm);
        completenessService.UpdateCompleteness(customerBusinessInformation.getCiId());
        return customerBusinessInformation.getBusinessInformationId();
    }


    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        String updateBy = SecurityUtils.getLoginUser().getUser().getNickName();
        //删除附件
        CustomerBusinessInformationVO customerBusinessInformationVO = getDetailById(id);
        // resolved: 移除已删除的BizType常量引用
        // commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.INDIVIDUAL_TAX_PASSWORD);
        // commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.BUSINESS_CHANGE_INFO);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.BUSINESS_LICENSE);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.REGISTRATION_INFORMATION);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.BUSINESS_CONSTITUTION);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.SHAREHOLDER_COMMITTEE_RESSOLUTION);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.ADRESS);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.IDENTITY_DOCUMENT);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.BUSINESS_OTHER);
        commonBizFileService.deleteByMainIdAndBizType(customerBusinessInformationVO.getCiId(), BizType.BUSINESS_HANDOVER_DOCUMENT);
        customerBusinessInformationMapper.deleteById(id, updateBy);
        return removeById(id);
    }

    public CustomerBusinessInformationVO getDetailByCiId(Long ciId) {

        CustomerBusinessInformationVO customerBusinessInformationVO = getBaseMapper().getDetailByCiId(ciId);
        CommonBizFile individualTaxPasswordFile = commonBizFileService.selectOneByMainIdAndBizType(ciId, BizType.INDIVIDUAL_TAX_PASSWORD);
        List<CommonBizFile> businessChangeInfoFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_CHANGE_INFO);
        List<CommonBizFile> businessLicenseFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_LICENSE);
        List<CommonBizFile> registrationInformationFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.REGISTRATION_INFORMATION);
        List<CommonBizFile> businessConstitutionFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_CONSTITUTION);
        List<CommonBizFile> shareholderCommitteeRessolutionFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.SHAREHOLDER_COMMITTEE_RESSOLUTION);
        List<CommonBizFile> adressFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.ADRESS);
        List<CommonBizFile> businessOtherFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_OTHER);
        List<CommonBizFile> identityDocumentFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.IDENTITY_DOCUMENT);
        List<CommonBizFile> handoverDocumentFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.BUSINESS_HANDOVER_DOCUMENT);

        if (null != customerBusinessInformationVO) {
            customerBusinessInformationVO.setIndividualTaxPasswordFile(individualTaxPasswordFile);
            customerBusinessInformationVO.setBusinessFileList(businessLicenseFileList);
            customerBusinessInformationVO.setRegistrationInformationFileList(registrationInformationFileList);
            customerBusinessInformationVO.setBusinessConstitutionFileList(businessConstitutionFileList);
            customerBusinessInformationVO.setShareholderCommitteeRessolutionFileList(shareholderCommitteeRessolutionFileList);
            customerBusinessInformationVO.setAdressFileList(adressFileList);
            customerBusinessInformationVO.setBusinessChangeInfoFileList(businessChangeInfoFileList);
            customerBusinessInformationVO.setBusinessOtherFileList(businessOtherFileList);
            customerBusinessInformationVO.setIdentityDocumentFileList(identityDocumentFileList);
            customerBusinessInformationVO.setHandoverDocumentFileList(handoverDocumentFileList);

            //查询客户名称
            CustomerInformationVO customerInformationVO = customerInformationMapper.getDetailById(ciId);
            customerBusinessInformationVO.setCustomerName(customerInformationVO.getCustomerName());
        }

        return customerBusinessInformationVO;
    }
}
