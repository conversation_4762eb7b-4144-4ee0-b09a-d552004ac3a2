package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressPropertyOwnership;
import com.jri.biz.domain.request.address.AddressPropertyOwnershipForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 房屋产权证明对象转换
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Mapper
public interface AddressPropertyOwnershipConvert {
    AddressPropertyOwnershipConvert INSTANCE = Mappers.getMapper(AddressPropertyOwnershipConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    AddressPropertyOwnership convert(AddressPropertyOwnershipForm form);

}