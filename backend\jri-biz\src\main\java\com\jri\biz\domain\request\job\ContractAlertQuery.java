package com.jri.biz.domain.request.job;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="查询对象")
public class ContractAlertQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
