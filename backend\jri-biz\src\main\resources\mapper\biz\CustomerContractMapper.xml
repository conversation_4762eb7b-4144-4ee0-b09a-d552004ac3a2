<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerContractMapper">
    <update id="updateDocumentNo">
        update customer_contract set document_no = #{form.documentNo}
        where contract_id = #{form.contractId}
    </update>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerContractListVO">
        select cc.*, ci.customer_no, ci.customer_name, bp.product_name, manger.nick_name manager, cc.start_time
        startTimeOrg, cc.end_time endTimeOrg, counselor.nick_name counselor, customer_success.nick_name customerSuccess, sponsor_accounting.nick_name sponsorAccounting
        from customer_contract cc
        left join customer_information ci on cc.ci_id = ci.customer_id
        left join business_product bp on cc.product_id = bp.id
        left join sys_user su on ci.manger_user_id = su.user_id
        left join sys_user su1 on ci.counselor_user_id = su1.user_id
        left join sys_user su2 on ci.customer_success_user_id = su2.user_id
        left join sys_user su3 on ci.sponsor_accounting_user_id = su3.user_id
        left join sys_user manger on cc.manger_user_id = manger.user_id
        left join sys_user counselor on cc.counselor_user_id = counselor.user_id
        left join sys_user customer_success on cc.customer_success_user_id = customer_success.user_id
        left join sys_user sponsor_accounting on cc.sponsor_accounting_user_id = sponsor_accounting.user_id
        <where>
            cc.is_deleted = 0
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                and cc.contract_name like concat('%', #{query.contractName}, '%')
            </if>
            <if test="query.contractNo != null and query.contractNo != ''">
                and cc.contract_no like concat('%', #{query.contractNo}, '%')
            </if>
            <if test="query.personnel != null and query.personnel != ''">
                and (su.nick_name like concat('%', #{query.personnel}, '%') or su1.nick_name like concat('%', #{query.personnel}, '%')
                 or su2.nick_name like concat('%', #{query.personnel}, '%') or su3.nick_name like concat('%', #{query.personnel}, '%'))
            </if>
            <if test="query.productId != null and query.productId != ''">
                and cc.product_id = #{query.productId}
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and cc.contract_type = #{query.contractType}
            </if>
            <if test="query.customerId != null">
                and ci.customer_id = #{query.customerId}
            </if>
            <if test="query.contractStatus == 3">
                and cc.contract_status = '3' and cc.is_intention = '0'
            </if>
            <if test="query.contractStatus == 4">
                and cc.contract_status = '1' and (cc.end_time >= current_date or contract_type = '1') and cc.is_intention = '0'
            </if>
            <if test="query.contractStatus == 5">
                and cc.contract_status = '1' and current_date > cc.end_time and cc.is_intention = '0'
            </if>
            <if test="query.contractStatus == 6">
                and cc.contract_status = '1' and cc.is_intention = '1'
            </if>
            <if test="query.contractStatus == null or query.contractStatus == ''">
                and cc.contract_status = '1' and cc.is_intention = '0'
            </if>
            <if test="query.totalCostMax != null">
                and cc.total_cost &lt;= #{query.totalCostMax}
            </if>
            <if test="query.totalCostMin != null">
                and cc.total_cost &gt;= #{query.totalCostMin}
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and cc.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.startTimeStart != null and query.startTimeEnd != null">
                and cc.start_time between #{query.startTimeStart} and #{query.startTimeEnd}
            </if>
            <if test="query.endTimeStart != null and query.endTimeEnd != null">
                and cc.end_time between #{query.endTimeStart} and #{query.endTimeEnd}
            </if>
            <if test="query.mangerUserId != null">
                and cc.manger_user_id = #{query.mangerUserId}
            </if>
            <if test="query.counselorUserId != null">
                and cc.counselor_user_id = #{query.counselorUserId}
            </if>
            <if test="query.customerSuccessUserId != null">
                and cc.customer_success_user_id = #{query.customerSuccessUserId}
            </if>
            <if test="query.sponsorAccountingUserId != null">
                and cc.sponsor_accounting_user_id = #{query.sponsorAccountingUserId}
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by cc.contract_no desc, cc.create_time desc
            </otherwise>
        </choose>
    </select>


    <select id="listPageEndTime" resultType="com.jri.biz.domain.vo.CustomerContractListVO">
        select cc.*, ci.customer_no, ci.customer_name, bp.product_name, su.nick_name manager, cc.start_time
        startTimeOrg, cc.end_time endTimeOrg
        from customer_contract cc
        left join customer_information ci on cc.ci_id = ci.customer_id
        left join business_product bp on cc.product_id = bp.id
        left join sys_user su on cc.create_by = su.user_id
        <where>
            cc.is_deleted = 0
            and cc.end_time >=CURDATE()
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerNo != null and query.customerNo != ''">
                and ci.customer_no like concat('%', #{query.customerNo}, '%')
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                and cc.contract_name like concat('%', #{query.contractName}, '%')
            </if>
            <if test="query.contractNo != null and query.contractNo != ''">
                and cc.contract_no like concat('%', #{query.contractNo}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and cc.contract_type = #{query.contractType}
            </if>
            <if test="query.customerId != null">
                and ci.customer_id = #{query.customerId}
            </if>
            <if test="query.contractStatus == 3">
                and cc.contract_status = '3'
            </if>
            <if test="query.contractStatus == 4">
                and cc.contract_status = '1' and (cc.end_time >= current_date or contract_type = '1')
            </if>
            <if test="query.contractStatus == 5">
                and cc.contract_status = '1' and current_date > cc.end_time
            </if>
            <if test="query.contractStatus == null or query.contractStatus == ''">
                and cc.contract_status = '1'
            </if>
        </where>
        order by cc.contract_no desc, cc.create_time desc
    </select>


    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerContractVO">
        select customer_contract.*,
               ci.customer_no,
               ci.customer_name,
               ci.address,
               bp.product_name,
               manager_user.user_id manager_user_id,
               su.nick_name                   createBy,
               customer_contract.start_time   startTimeOrg,
               customer_contract.end_time     endTimeOrg,
               bp.fee_type,
               bp.is_in_contract,
               customer_contract.declare_type `declare`,
               org.contract_no                originContractNo,
               ct.temp_name
        from customer_contract
        left join customer_information ci on customer_contract.ci_id = ci.customer_id
        left join business_product bp on customer_contract.product_id = bp.id
        left join sys_user su on customer_contract.create_by = su.user_id
        left join customer_contract org on customer_contract.origin_id = org.contract_id
        left join contract_temp ct on customer_contract.temp_id = ct.id
        left join sys_user as manager_user on customer_contract.manger_user_id = manager_user.user_id
        where customer_contract.contract_id = #{id}
          <if test="showFlag == null and !showFlag">
              and customer_contract.is_deleted = 0
          </if>

    </select>
    <select id="getDetailByIdCheck" resultType="com.jri.biz.domain.vo.CustomerContractVO">
        select customer_contract.*,
        ci.customer_no,
        ci.customer_name,
        ci.address,
        bp.product_name,
        manager_user.user_id manager_user_id,
        su.nick_name createBy,
        customer_contract.start_time startTimeOrg,
        customer_contract.end_time endTimeOrg,
        bp.fee_type,
        bp.is_in_contract,
        customer_contract.declare_type `declare`,
        org.contract_no originContractNo,
        ct.temp_name
        from customer_contract
        left join customer_information ci on customer_contract.ci_id = ci.customer_id
        left join business_product bp on customer_contract.product_id = bp.id
        left join sys_user su on customer_contract.create_by = su.user_id
        left join sys_dept d on su.dept_id = d.dept_id
        left join customer_contract org on customer_contract.origin_id = org.contract_id
        left join contract_temp ct on customer_contract.temp_id = ct.id
        left join sys_user as manager_user on customer_contract.manger_user_id = manager_user.user_id
        where customer_contract.contract_id = #{id}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>
    <select id="listByCiId" resultType="com.jri.biz.domain.vo.ContractListVO">
        select cc.*,
               bp.product_name,
               su.nick_name                       manager,
               bt.type_name                       typeName,
               case
                   when cc.is_intention = '0' then
                    case
                        when cc.contract_status = '1' then
                            case
                                when cc.contract_type = '1' then
                                    '4' else
                                    case
                                        when CURRENT_DATE > cc.end_time then
                                            '5'
                                        when cc.end_time >= CURRENT_DATE then
                                            '4' else ''
                                        end
                                end
                        else cc.contract_status end
                   else '6' end
                   as contractStatus
        from customer_contract cc
                 left join customer_information ci on cc.ci_id = ci.customer_id
                 left join business_product bp on cc.product_id = bp.id
                 left join business_type bt on bp.type_id = bt.id
                 left join sys_user su on ci.manger_user_id = su.user_id
        where cc.is_deleted = 0
          and cc.ci_id = #{query.ciId}
          and (cc.contract_status = '1' or cc.contract_status = '3')
        order by cc.create_time desc
    </select>
    <select id="listMyCreate" resultType="com.jri.biz.domain.vo.ReviewCustomerContractListVO">
        select cc.contract_id,
        cc.contract_name,
        cc.contract_type,
        ci.customer_no,
        ci.customer_name,
        bp.product_name,
        su.nick_name createBy,
        cc.create_time,
        cc.contract_status reviewStatus,
        cc.biz_type,
        cc.ci_id,
        cc.contract_no,
        cc.total_cost
        from customer_contract cc
        left join customer_information ci on cc.ci_id = ci.customer_id
        left join business_product bp on cc.product_id = bp.id
        left join sys_user su on cc.create_by = su.user_id
        <where>
            and cc.is_deleted = 0
            and cc.create_by = #{query.userId}
            <if test="query.contractName != null and query.contractName != ''">
                and cc.contract_name like concat('%', #{query.contractName}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and cc.contract_type = #{query.contractType}
            </if>
            <if test="query.contractNo != null and query.contractNo != ''">
                and cc.contract_no = #{query.contractNo}
            </if>
            <if test="query.bizType != null and query.bizType != ''">
                and cc.biz_type = #{query.bizType}
            </if>
            <if test="query.reviewStatus != null and query.reviewStatus != ''">
                <if test="query.reviewStatus == 0">
                    and cc.contract_status = #{query.reviewStatus}
                </if>
                <if test="query.reviewStatus == 1">
                    and (cc.contract_status = '1' or cc.contract_status = '3')
                </if>
                <if test="query.reviewStatus == 2">
                    and cc.contract_status = #{query.reviewStatus}
                </if>
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.productId != null">
                and cc.product_id = #{query.productId}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su.nick_name like concat('%', #{query.createBy}, '%')
            </if>
        </where>
        order by cc.create_time desc
    </select>

    <select id="auditList" resultType="com.jri.biz.domain.vo.ReviewCustomerContractListVO">
        select
        distinct cc.contract_id,
        cc.contract_name,
        cc.contract_type,
        ci.customer_no,
        ci.customer_name,
        bp.product_name,
        su.nick_name createBy,
        cc.create_time,
        cc.contract_status reviewStatus,
        cc.biz_type,
        cc.ci_id,
        cc.contract_no,
        cc.total_cost
        from customer_contract cc
        left join customer_information ci on cc.ci_id = ci.customer_id
        left join business_product bp on cc.product_id = bp.id
        left join sys_user su on cc.create_by = su.user_id
        left join biz_node_history on cc.contract_id = biz_node_history.main_id and biz_node_history.type = '2'and biz_node_history.review_status != '0'
        left join sys_user as manager_user on (ci.manger_user_id = manager_user.user_id)
        left join sys_user as sponsor_accounting_user on ci.sponsor_accounting_user_id = sponsor_accounting_user.user_id
        left join sys_user as counselor_user on ci.counselor_user_id = counselor_user.user_id
        left join sys_user as customer_success_user on ci.customer_success_user_id = customer_success_user.user_id
        <where>
            and cc.is_deleted = 0
            <if test="query.contractName != null and query.contractName != ''">
                and cc.contract_name like concat('%', #{query.contractName}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and cc.contract_type = #{query.contractType}
            </if>
            <if test="query.contractNo != null and query.contractNo != ''">
                and cc.contract_no = #{query.contractNo}
            </if>
            <if test="query.bizType != null and query.bizType != ''">
                and cc.biz_type = #{query.bizType}
            </if>
            <if test="query.reviewStatus != null and query.reviewStatus != ''">
                <choose>
                    <when test="query.reviewStatus == 1">
                        and (cc.contract_status = '1' or cc.contract_status = '3')
                    </when>
                    <otherwise>
                        and cc.contract_status = #{query.reviewStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.productId != null">
                and cc.product_id = #{query.productId}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.totalCostMax != null">
                and cc.total_cost &lt;= #{query.totalCostMax}
            </if>
            <if test="query.totalCostMin != null">
                and cc.total_cost &gt;= #{query.totalCostMin}
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and cc.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            ${query.dataScopeSql}
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by cc.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="listMyAudit" resultType="com.jri.biz.domain.vo.ReviewCustomerContractListVO">
        select cc.contract_id,
        cc.contract_name,
        cc.contract_type,
        cc.start_time,
        cc.end_time,
        ci.customer_no,
        ci.customer_name,
        bp.product_name,
        su.nick_name createBy,
        cc.create_time,
        bnh.review_status reviewStatus,
        cc.biz_type,
        cc.ci_id,
        cc.contract_no,
        cc.total_cost
        from customer_contract cc
        left join customer_information ci on cc.ci_id = ci.customer_id
        left join business_product bp on cc.product_id = bp.id
        left join sys_user su on cc.create_by = su.user_id
        left join (
            select max(bnh.node_key) node_key,contract_id from customer_contract cc left join biz_node_history bnh ON
            bnh.main_id = cc.contract_id
            and bnh.type = '2'
            where bnh.user_id = #{query.userId}
            group by contract_id
            )mm on cc.contract_id = mm.contract_id
        left join biz_node_history bnh on mm.contract_id = bnh.main_id and mm.node_key = bnh.node_key
        <where>
            and bnh.user_id = #{query.userId} and bnh.is_now = '1' and cc.contract_status = '0'
            <if test="query.contractName != null and query.contractName != ''">
                and cc.contract_name like concat('%', #{query.contractName}, '%')
            </if>
            <if test="query.contractType != null and query.contractType != ''">
                and cc.contract_type = #{query.contractType}
            </if>
            <if test="query.contractNo != null and query.contractNo != ''">
                and cc.contract_no = #{query.contractNo}
            </if>
            <if test="query.bizType != null and query.bizType != ''">
                and cc.biz_type = #{query.bizType}
            </if>
            <if test="query.reviewStatus != null and query.reviewStatus != ''">
                and bnh.review_status = #{query.reviewStatus}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (ci.customer_name like concat('%', #{query.keyword}, '%') or
                    bp.product_name like concat('%', #{query.keyword}, '%') or
                    su.nick_name like concat('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and ci.customer_name like concat('%', #{query.customerName}, '%')
            </if>
            <if test="query.productId != null">
                and cc.product_id = #{query.productId}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and su.nick_name like concat('%', #{query.createBy}, '%')
            </if>
            <if test="query.totalCostMax != null">
                and cc.total_cost &lt;= #{query.totalCostMax}
            </if>
            <if test="query.totalCostMin != null">
                and cc.total_cost &gt;= #{query.totalCostMin}
            </if>
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                and cc.create_time between #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
        </where>
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by ${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <otherwise>
                order by cc.create_time desc
            </otherwise>
        </choose>
    </select>
    <select id="getIsDelete" resultType="java.lang.Boolean">
        select is_deleted
        from customer_contract
        where contract_id = #{id}
    </select>

    <select id="getContractListByCustomerIdAndType" resultType="com.jri.biz.domain.entity.CustomerContract">
         select contract.*
         from customer_contract as contract
         where contract.ci_id = #{customerId}
           and contract.contract_status = '1'
           and contract.contract_type = '0'
    </select>
    <select id="getNumByProductIdAndYear" resultType="java.lang.Long">
        select ifnull(sum(total_cost), 0)
        from customer_contract
        where is_deleted = 0
        and product_id = #{productId}
        and year(create_time) = #{year}
        and month(create_time) = #{month}
        and contract_status = '1'
        and (is_intention = '0' or is_intention is null)
    </select>
    <select id="getTotalCostByYearAndMonthAndProductId" resultType="java.lang.Long">
        select ifnull(sum(cc.total_cost), 0)
        from customer_contract cc
        left join business_product bp on cc.product_id = bp.id
        where cc.is_deleted = 0
            and (cc.product_id = #{productId} or bp.type_id = #{productId})
            and year(cc.create_time) = #{year}
          and month(cc.create_time) = #{month}
          and cc.contract_status = '1'
          and (cc.is_intention = '0' or cc. is_intention is null)
    </select>

    <select id="countByCustomerStatus" resultType="java.lang.Long">
        select count(*)
        from customer_contract as contract
        left join business_product as product on contract.product_id = product.id
        left join customer_information as cust on contract.ci_id = cust.customer_id
        where contract.is_intention = '0'
        and contract.contract_status = '1'
        and contract.is_deleted = '0'
        and product.is_deleted = '0'
        and product.product_name like concat('%', #{productName}, '%')
        and cust.is_deleted = '0'
        and cust.customer_status = #{customerStatus}
        and cust.discard = '0'
    </select>

    <select id="getAccountNum" resultType="java.lang.Long">
        select count(contract_id)
        from customer_contract
        where is_deleted = '0'
        and (contract_status = '1' or contract_status = '0')
        and contract_type = '0'
        and ci_id = #{ciId}
    </select>

    <select id="getLatestExpireTime" resultType="java.time.LocalDate">
        select min(end_time) from customer_contract as contract
        where contract.ci_id = #{customerId}
          and contract.is_intention = '0'
          and contract.contract_status = '1'
          and contract.is_deleted = '0'
          and contract.end_time > now()
    </select>
    <select id="getDocRole" resultType="com.jri.common.core.domain.entity.SysRole">
        select sr.*
        from sys_role sr
        left join sys_user_role sur on sr. role_id = sur.role_id
        where sur.user_id = #{userId} and sr.del_flag = '0' and sr.role_key = 'document'
    </select>

    <select id="userCustomerContractListPage"
            resultType="com.jri.biz.domain.vo.customerUser.UserCustomerContractListVO">
        select
            contract.contract_id,
            contract.ci_id,
            contract.contract_name,
            contract.contract_type,
            contract.type,
            contract.start_time,
            contract.end_time,
            contract.total_cost,
            contract.product_id,
            info.customer_name,
            product.product_name
        from bofeng.user_customer_bind_record bind_record
        left join customer_contract contract on bind_record.customer_id = contract.ci_id
        left join customer_information info on contract.ci_id = info.customer_id
        left join business_product product on contract.product_id = product.id
        left join sys_user create_user on contract.create_by = create_user.user_id
        where contract.is_deleted = 0
          and contract.contract_status = '1'
          and contract.is_intention = '0'
          and contract.sign_flag = false
          and info.is_deleted = 0
          and bind_record.user_id = #{query.userId}
          and bind_record.bind_status = 'bind'
        <if test="query.keyword != null and query.keyword != ''">
            and (info.customer_name like concat('%', #{query.keyword}, '%') or
                product.product_name like concat('%', #{query.keyword}, '%') or
                create_user.nick_name like concat('%', #{query.keyword}, '%')
                )
        </if>
        order by contract.contract_no desc, contract.create_time desc
    </select>

    <select id="getContractByProductNameAndCustomerId" resultType="com.jri.biz.domain.entity.CustomerContract">
        select *
        from customer_contract contract
        left join business_product product on contract.product_id = product.id
        where contract.is_deleted = 0
          and contract.contract_status = '1'
          and contract.is_intention = '0'
          and product.is_deleted = 0
          and product.product_name like concat('%', #{productName}, '%')
          and contract.ci_id = #{customerId}
        order by contract.create_time desc
        limit 1
    </select>
    <select id="getCancelContractCustomerId" resultType="java.lang.Long">
        select
            distinct ci_id
        from customer_contract contract
        left join business_product product on contract.product_id = product.id
        left join customer_information info on contract.ci_id = info.customer_id
        where contract.is_deleted = '0'
        and contract.contract_status = '1'
        and contract.is_intention = '0'
        and product.product_name like '%注销%'
        <if test="role != null and role != ''">
            <if test="role == 'manager'">
                and info.manger_user_id = #{userId}
            </if>
            <if test="role == 'sponsor_accounting'">
                and info.sponsor_accounting_user_id = #{userId}
            </if>
            <if test="role == 'counselor'">
                and info.counselor_user_id = #{userId}
            </if>
            <if test="role == 'customer_success'">
                and info.customer_success_user_id = #{userId}
            </if>
        </if>
        <if test="startTime != null">
            and contract.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and contract.create_time &lt;= #{endTime}
        </if>
    </select>


    <delete id="delete">
        update customer_contract
        set is_deleted = '1',
            update_by= #{updateBy}
        where contract_id = #{id}
    </delete>
</mapper>
