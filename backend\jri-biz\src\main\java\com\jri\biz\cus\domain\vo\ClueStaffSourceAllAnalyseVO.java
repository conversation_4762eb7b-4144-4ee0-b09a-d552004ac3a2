package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/5 14:49
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "人员&全部来源统计VO")
public class ClueStaffSourceAllAnalyseVO extends BaseStaffSourceAnalyseVO {

    @ApiModelProperty(value = "总成交天数")
    private Long dealDays = 0L;

    @ApiModelProperty(value = "平均成交天数")
    private String avgDealDays = "0";

}
