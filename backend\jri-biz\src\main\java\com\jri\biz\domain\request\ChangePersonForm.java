package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@ApiModel(value = "主办会计/财税顾问/客户成功变更")
public class ChangePersonForm {

    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @ApiModelProperty(value = "变更人员")
    @NotBlank(message = "变更人员不能为空")
    private String nameStr;

    @ApiModelProperty(value = "变更人员id")
    @NotNull(message = "变更人员id不能为空")
    private Long userId;

    @ApiModelProperty(value = "类型0-会计 1-财税 2-客户成功")
    @NotNull(message = "类型不能为空")
    private Integer type;
}
