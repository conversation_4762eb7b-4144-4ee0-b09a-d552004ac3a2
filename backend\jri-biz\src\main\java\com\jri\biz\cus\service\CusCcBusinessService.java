package com.jri.biz.cus.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.delay.RedisDelayQueueEnum;
import com.jri.biz.cus.delay.util.RedisDelayQueueUtil;
import com.jri.biz.cus.domain.convert.CusCcBusinessConvert;
import com.jri.biz.cus.domain.entity.*;
import com.jri.biz.cus.domain.request.CusCcBusinessForm;
import com.jri.biz.cus.domain.request.CusCcBusinessMarkForm;
import com.jri.biz.cus.domain.request.CusCcBusinessQuery;
import com.jri.biz.cus.domain.vo.*;
import com.jri.biz.cus.mapper.CusCcBusinessMapper;
import com.jri.biz.cus.mapper.CusCcFollowMapper;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.biz.domain.entity.BusinessProduct;
import com.jri.biz.mapper.BusinessProductMapper;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 商机 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Slf4j
@Service
public class CusCcBusinessService extends ServiceImpl<CusCcBusinessMapper, CusCcBusiness> {

    @Resource
    private CusBusinessBizService cusBusinessBizService;

    @Resource
    private BusinessProductMapper businessProductMapper;

    @Resource
    private CusCustomerOrClueMapper cusCustomerOrClueMapper;

    @Resource
    private CusCcRecordService cusCcRecordService;

    @Resource
    private CusCcFollowMapper cusCcFollowMapper;

    @Resource
    private CusSeaInventoryService cusSeaInventoryService;

    @Resource
    private CusSeaService cusSeaService;

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCcBusinessListVO> listPage(CusCcBusinessQuery query) {
        var page = new Page<CusCcBusinessListVO>(query.getPageNum(), query.getPageSize());
        query.setUserId(SecurityUtils.getUserId());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CusCcBusinessVO getDetailById(Long id) {
        CusCcBusinessVO res = getBaseMapper().getDetailById(id);
        if (null != res) {
            res.setList(cusBusinessBizService.getBaseMapper().getBizListByBusinessId(id));
            CusCcFollow cusCcFollow = cusCcFollowMapper.lastBusiness(id);
            if (null != cusCcFollow) {
                res.setLastFollowTime(cusCcFollow.getCreateTime());
            }
            if (ObjectUtil.isNotEmpty(res.getPaymentIds())) {
                List<String> noList = new ArrayList<>();
                res.getPaymentIds().forEach(item -> {
                    noList.add(getBaseMapper().getPaymentNoById(item));
                });

                res.setPaymentNos(String.join(";", noList));
            }

        }
        return res;
    }


    /**
     * 保存
     *
     * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public R<Long> saveOrUpdate(CusCcBusinessForm form) {
        CusCustomerOrClue customerOrClue = cusCustomerOrClueMapper.selectById(form.getCcId());
        if ("1".equals(customerOrClue.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }
        CusCcBusiness cusCcBusiness = CusCcBusinessConvert.INSTANCE.convert(form);
        final LocalDateTime now = LocalDateTime.now();
        List<CusBusinessBiz> cusBusinessBizList = form.getList();
        if (null == cusCcBusiness.getId()) {
            customerOrClue.setProtectionStartTime(now);
            customerOrClue.setLastFollowTime(now);
            if ("赢单".equals(cusCcBusiness.getStage()) || "已收定金,待打尾款".equals(cusCcBusiness.getStage())) {
                // 注册业务检查
                String message = registerBizCheck(cusBusinessBizList, cusCcBusiness.getCcId());
                if (StrUtil.isNotBlank(message)) {
                    return R.ok(null, message);
                }
                // 如果客户未建档，需先建档
                Long customerId = customerOrClue.getCustomerId();
                if (ObjectUtil.isNull(customerId)) {
                    return R.ok(null, "客户未建档，请先建档！");
                } else {
                    cusCcBusiness.setCreateTime(now);
                    updateFollowStatus(cusCcBusiness, now, cusBusinessBizList, customerOrClue);

                    // 删除队列
                    if ("1".equals(customerOrClue.getFollowStatus())) {
                        delayQueueHandle(customerOrClue);
                    }

                    return R.ok(customerOrClue.getCustomerId(), "是否需要新增合同？");
                }
            } else {
                if ("输单".equals(cusCcBusiness.getStage())) {
                    cusCcBusiness.setLoseTime(now);
                    cusCcBusiness.setFollowStatus("3");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
                }
                cusCcBusiness.setCreateTime(now);
                cusCcBusiness.setUpdateTime(now);
                save(cusCcBusiness);

                // 更新最近跟进时间 跟进状态
                customerOrClue.setFollowStatus("1");
                customerOrClue.setLastFollowUser(SecurityUtils.getLoginUser().getUser().getNickName());
                cusCustomerOrClueMapper.updateById(customerOrClue);
                // 修改队列 未跟进或者跟进中需要更新队列
                if ("0".equals(customerOrClue.getFollowStatus()) || "1".equals(customerOrClue.getFollowStatus())) {
                    delayQueueHandle(customerOrClue);
                }
            }
        } else {
            // 原商机
            CusCcBusiness oldCus = getById(cusCcBusiness.getId());
            if ("赢单".equals(cusCcBusiness.getStage()) || "已收定金,待打尾款".equals(cusCcBusiness.getStage())) {
                // 注册业务检查
                String message = registerBizCheck(cusBusinessBizList, cusCcBusiness.getCcId());
                if (StrUtil.isNotBlank(message)) {
                    return R.ok(null, message);
                }
                // 如果客户未建档，需先建档
                Long customerId = customerOrClue.getCustomerId();
                if (ObjectUtil.isNull(customerId)) {
                    return R.ok(null, "客户未建档，请先建档！");
                } else {
                    updateFollowStatus(cusCcBusiness, now, cusBusinessBizList, customerOrClue);

                    if ("2".equals(customerOrClue.getFollowStatus())) {
                        redisDelayQueueUtil.removeDelayedQueue(customerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
                        redisDelayQueueUtil.removeDelayedQueue(customerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                        cusCustomerOrClueMapper.updateDuration(customerOrClue.getId(), 0);
                    }

                    // 记录操作记录
                    String remark = "商机阶段：从【" + oldCus.getStage() + "】变更为：【" + cusCcBusiness.getStage() + "】<br/>实际成交金额从【0.00】变更为【" + cusCcBusiness.getActualAmount() + "】";
                    cusCcRecordService.saveBusiness("编辑商机", remark, cusCcBusiness.getId());
                    return R.ok(customerOrClue.getCustomerId(), "是否需要新增合同？");
                }
            } else {
                if ("输单".equals(cusCcBusiness.getStage())) {
                    cusCcBusiness.setLoseTime(now);
                    cusCcBusiness.setFollowStatus("3");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
                }
                cusCcBusiness.setUpdateTime(now);
                updateById(cusCcBusiness);

                // 记录操作记录
                String remark = "商机阶段：从【" + oldCus.getStage() + "】变更为：【" + cusCcBusiness.getStage() + "】";
                cusCcRecordService.saveBusiness("编辑商机", remark, cusCcBusiness.getId());
            }
        }
        return R.ok();
    }

    /**
     * 延迟队列处理
     */
    private void delayQueueHandle(CusCustomerOrClue customerOrClue) {
        Integer duration = 0;
        Integer recovery = 0;
        if (ObjectUtil.isEmpty(customerOrClue.getSeaId())) {// 私海新建
            CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
            if (null != inventory) {
                if ("0".equals(customerOrClue.getType())) {
                    duration = inventory.getClueDuration();
                    recovery = inventory.getClueRecovery();
                } else {
                    duration = inventory.getCusDuration();
                    recovery = inventory.getCusRecovery();
                }
            }
        } else { // 领取或者分配
            CusSea byId = cusSeaService.getById(customerOrClue.getSeaId());
            if (null != byId) {// 公海存在
                duration = byId.getDuration();
                recovery = byId.getRecovery();
            } else { // 公海删除
                CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
                if (null != inventory) {
                    if ("0".equals(customerOrClue.getType())) {
                        duration = inventory.getClueDuration();
                        recovery = inventory.getClueRecovery();
                    } else {
                        duration = inventory.getCusDuration();
                        recovery = inventory.getCusRecovery();
                    }
                }
            }
        }
        redisDelayQueueUtil.removeDelayedQueue(customerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(customerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        cusCustomerOrClueMapper.updateDuration(customerOrClue.getId(), 0);
        if (ObjectUtil.isNotEmpty(duration) && duration > 0) {
            cusCustomerOrClueMapper.updateDuration(customerOrClue.getId(), duration);
            redisDelayQueueUtil.addDelayQueue(customerOrClue.getId(), duration, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
            if (ObjectUtil.isNotEmpty(recovery) && recovery > 0) {
                if (duration - recovery > 0) {
                    redisDelayQueueUtil.addDelayQueue(customerOrClue.getId(), duration - recovery, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                }
            }
        }
    }

    /**
     * 跟进状态更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFollowStatus(CusCcBusiness cusCcBusiness, LocalDateTime now,
                                   List<CusBusinessBiz> cusBusinessBizList,
                                   CusCustomerOrClue customerOrClue) {
        cusCcBusiness.setWinTime(now);
        cusCcBusiness.setUpdateTime(now);
        if ("赢单".equals(cusCcBusiness.getStage())) {
            cusCcBusiness.setFollowStatus("2");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
        } else {
            cusCcBusiness.setFollowStatus("1");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
        }
        saveOrUpdate(cusCcBusiness);
        if (StrUtil.equalsAny(cusCcBusiness.getStage(), "赢单", "已收定金,待打尾款")) {
            // 分类计算各个赢单产品实际成交金额
            actualAmountHandle(cusCcBusiness);
        }
        // 业务信息
        if (ObjectUtil.isNotEmpty(cusBusinessBizList)) {
            for (CusBusinessBiz item : cusBusinessBizList) {
                item.setBusinessId(cusCcBusiness.getId());
            }
            cusBusinessBizService.saveBatch(cusBusinessBizList);
        }
        // 更新跟进状态
        updateFollowStatus(cusCcBusiness, customerOrClue);
        customerOrClue.setLastFollowUser(SecurityUtils.getLoginUser().getUser().getNickName());
        cusCustomerOrClueMapper.updateById(customerOrClue);
    }

    /**
     * 跟进状态更新
     */
    private void updateFollowStatus(CusCcBusiness cusCcBusiness, CusCustomerOrClue customerOrClue) {
        LambdaQueryWrapper<CusCcBusiness> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCcBusiness::getCcId, cusCcBusiness.getCcId());
        wrapper.ne(CusCcBusiness::getStage, "赢单");
        if ("赢单".equals(cusCcBusiness.getStage())) {
            wrapper.ne(CusCcBusiness::getId, cusCcBusiness.getId());
        }
        if (StrUtil.equalsAny(cusCcBusiness.getStage(), "赢单", "已收定金,待打尾款")) {
            // 分类计算各个赢单产品实际成交金额
            actualAmountHandle(cusCcBusiness);
        }
        if (count(wrapper) > 0) {
            customerOrClue.setFollowStatus("1");
        } else {
            customerOrClue.setFollowStatus("2");
        }
    }

    /**
     * 注册业务检查
     * @return 返回信息
     */
    private String registerBizCheck(List<CusBusinessBiz> list, Long ccid) {
        int num = 0;
        for (CusBusinessBiz item : list) {
            BusinessProduct businessProduct = businessProductMapper.selectById(item.getProductId());
            if ("内资注册".equals(businessProduct.getProductName()) || "内资注册 - 单办证".equals(businessProduct.getProductName())
                    || "外资注册".equals(businessProduct.getProductName()) || "外资注册 - 单办证".equals(businessProduct.getProductName())) {
                num = num + 1;
            }
        }
        if (num > 1) {
            throw new ServiceException("填写多个新注册业务");
        }
        if (num == 1) {
            List<CusBusinessBizVO> businessBizList = cusBusinessBizService.getAllBiz(ccid);
            for (CusBusinessBizVO item : businessBizList) {
                if ("内资注册".equals(item.getProductName()) || "内资注册 - 单办证".equals(item.getProductName())
                        || "外资注册".equals(item.getProductName()) || "外资注册 - 单办证".equals(item.getProductName())) {
                    return "该客户已办理过工商新注册业务，请确认签约业务后再次尝试！";
                }
            }
        }
        return null;
    }

    /**
     * 保存(建档时调用)
     *
     * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void establishSaveOrUpdate(CusCcBusinessForm form) {
        CusCcBusiness cusCcBusiness = CusCcBusinessConvert.INSTANCE.convert(form);
        final LocalDateTime now = LocalDateTime.now();
        CusCustomerOrClue cusCustomerOrClue = cusCustomerOrClueMapper.selectById(cusCcBusiness.getCcId());
        if (null == cusCcBusiness.getId()) {
            cusCcBusiness.setWinTime(now);
            cusCcBusiness.setCreateTime(now);
            cusCcBusiness.setUpdateTime(now);
            if ("赢单".equals(cusCcBusiness.getStage())) {
                cusCcBusiness.setFollowStatus("2");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
            } else {
                cusCcBusiness.setFollowStatus("1");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
            }
            save(cusCcBusiness);
            // 业务信息
            List<CusBusinessBiz> list = form.getList();
            if (ObjectUtil.isNotEmpty(list)) {
                for (CusBusinessBiz item : list) {
                    item.setBusinessId(cusCcBusiness.getId());
                }
                cusBusinessBizService.saveBatch(list);
            }
            cusCustomerOrClue.setProtectionStartTime(now);
            cusCustomerOrClue.setLastFollowTime(now);

            // 更新客户跟进状态
            updateFollowStatus(cusCcBusiness, cusCustomerOrClue);
            cusCustomerOrClueMapper.updateById(cusCustomerOrClue);

            // 删除队列
            if ("1".equals(cusCustomerOrClue.getFollowStatus())) {
                delayQueueHandle(cusCustomerOrClue);
            }
        } else {
            // 原商机
            CusCcBusiness oldCus = getById(cusCcBusiness.getId());
            cusCcBusiness.setWinTime(now);
            cusCcBusiness.setUpdateTime(now);
            if ("赢单".equals(cusCcBusiness.getStage())) {
                cusCcBusiness.setFollowStatus("2");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
            } else {
                cusCcBusiness.setFollowStatus("1");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
            }
            updateById(cusCcBusiness);
            // 业务信息
            List<CusBusinessBiz> list = form.getList();
            if (ObjectUtil.isNotEmpty(list)) {
                for (CusBusinessBiz item : list) {
                    item.setBusinessId(cusCcBusiness.getId());
                }
                cusBusinessBizService.saveBatch(list);
            }
            // 记录操作记录
            String remark = "商机阶段：从【" + oldCus.getStage() + "】变更为：【" + cusCcBusiness.getStage() + "】<br/>实际成交金额从【0.00】变更为【" + cusCcBusiness.getActualAmount() + "】";
            cusCcRecordService.saveBusiness("编辑商机", remark, cusCcBusiness.getId());

            // 更新客户跟进状态
            updateFollowStatus(cusCcBusiness, cusCustomerOrClue);
            cusCustomerOrClueMapper.updateById(cusCustomerOrClue);

            if ("2".equals(cusCustomerOrClue.getFollowStatus())) {
                redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
                redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                cusCustomerOrClueMapper.updateDuration(cusCustomerOrClue.getId(), 0);
            }
        }
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        CusCcBusiness business = getById(id);
        CusCustomerOrClue cusCustomerOrClue = cusCustomerOrClueMapper.selectById(business.getCcId());
        if ("1".equals(cusCustomerOrClue.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }
        if ("赢单".equals(business.getStage()) || "输单".equals(business.getStage())) {
            throw new ServiceException("赢单、输单的商机不可删除");
        }
        // 如果客户状态是跟进中 删除该商机后如果没有别的商机和跟进记录 状态更新为未跟进
        if ("1".equals(cusCustomerOrClue.getFollowStatus())) {
            LambdaQueryWrapper<CusCcFollow> followWrapper = new LambdaQueryWrapper<>();
            followWrapper.eq(CusCcFollow::getCcId, cusCustomerOrClue.getId());
            followWrapper.eq(CusCcFollow::getBizType, "0");

            LambdaQueryWrapper<CusCcBusiness> businessWrapper = new LambdaQueryWrapper<>();
            businessWrapper.eq(CusCcBusiness::getCcId, cusCustomerOrClue.getId());
            businessWrapper.ne(CusCcBusiness::getId, id);

            if (cusCcFollowMapper.selectCount(followWrapper) <= 0 && count(businessWrapper) <= 0) {
                cusCustomerOrClue.setFollowStatus("0");
                cusCustomerOrClueMapper.updateById(cusCustomerOrClue);
            }

            LambdaQueryWrapper<CusCcBusiness> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CusCcBusiness::getCcId, business.getCcId());
            wrapper.ne(CusCcBusiness::getStage, "赢单");
            wrapper.ne(CusCcBusiness::getId, id);

            LambdaQueryWrapper<CusCcBusiness> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(CusCcBusiness::getCcId, business.getCcId());
            wrapper1.eq(CusCcBusiness::getStage, "赢单");
            wrapper1.ne(CusCcBusiness::getId, id);

            // 如果仅有赢单商机 更新为已转企业
            if (count(wrapper) <= 0 && count(wrapper1) > 0) {
                cusCustomerOrClue.setFollowStatus("2");
                cusCustomerOrClueMapper.updateById(cusCustomerOrClue);
                redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
                redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                cusCustomerOrClueMapper.updateDuration(cusCustomerOrClue.getId(), 0);
            }

        }
        return removeById(id);
    }

    /**
     * 查询商机列表
     *
     * @param ccId 客户id
     */
    public List<CustomerBusinessListVO> listByCcId(Long ccId) {
        return getBaseMapper().listByCcId(ccId);
    }

    /**
     * 查询客户赢单商机数量
     *
     * @param id 客户id
     */
    public Long getWinNum(Long id) {
        return getBaseMapper().getWinNum(id);
    }

    /**
     * 查询客户输单商机数量
     *
     * @param id 客户id
     */
    public Long getLoseNum(Long id) {
        return getBaseMapper().getLoseNum(id);
    }

    /**
     * 查询客户其它商机数量
     *
     * @param id 客户id
     */
    public Long getOtherNum(Long id) {
        return getBaseMapper().getOtherNum(id);
    }

    /**
     * 标记赢单
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void mark(CusCcBusinessMarkForm form) {
        CusCcBusiness byId = getById(form.getId());
        if (null == byId) {
            throw new ServiceException("商机不存在");
        }
        if (!"已收定金,待打尾款".equals(byId.getStage())) {
            throw new ServiceException("当前阶段不允许标记赢单");
        }
        byId.setStage("赢单");
        byId.setPaymentIds(form.getPaymentIds());
        byId.setStagePercentage("100%");
        byId.setWinTime(LocalDateTime.now());
        byId.setStatus("结束");
        byId.setFollowStatus("2");
        updateById(byId);

        LambdaQueryWrapper<CusCcBusiness> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCcBusiness::getCcId, byId.getCcId());
        wrapper.ne(CusCcBusiness::getStage, "赢单");

        LambdaQueryWrapper<CusCcBusiness> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(CusCcBusiness::getCcId, byId.getCcId());
        wrapper1.eq(CusCcBusiness::getStage, "赢单");
        // 如果仅有赢单商机 更新为已转企业
        if (count(wrapper) <= 0 && count(wrapper1) > 0) {
            CusCustomerOrClue cusCustomerOrClue = cusCustomerOrClueMapper.selectById(byId.getCcId());
            cusCustomerOrClue.setFollowStatus("2");
            cusCustomerOrClueMapper.updateById(cusCustomerOrClue);
            redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
            redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
            cusCustomerOrClueMapper.updateDuration(cusCustomerOrClue.getId(), 0);
        }
    }

    /**
     * 分类计算各个赢单产品实际成交金额
     */
    private void actualAmountHandle(CusCcBusiness cusCcBusiness) {
        BigDecimal actualAmount = BigDecimal.ZERO;
        BigDecimal bookkeepingActualAmount = BigDecimal.ZERO;
        BigDecimal addressActualAmount = BigDecimal.ZERO;
        BigDecimal otherActualAmount = BigDecimal.ZERO;

        List<CusBusinessBizVO> cusBusinessBizVOList = cusBusinessBizService.getBaseMapper().getBizListByBusinessId(cusCcBusiness.getId());
        for (CusBusinessBizVO cusBusinessBizVO : cusBusinessBizVOList) {
            if (ObjectUtil.isNull(cusBusinessBizVO.getActualAmount())) {
                log.info("实际成交金额为空,商机id：{}", cusBusinessBizVO.getBusinessId());
                continue;
            }
            actualAmount = actualAmount.add(cusBusinessBizVO.getActualAmount());
            if (StrUtil.contains(cusBusinessBizVO.getTypeName(), "代理记账")) {
                bookkeepingActualAmount = bookkeepingActualAmount.add(cusBusinessBizVO.getActualAmount());
            } else if (StrUtil.contains(cusBusinessBizVO.getTypeName(), "地址")) {
                addressActualAmount = addressActualAmount.add(cusBusinessBizVO.getActualAmount());
            } else {
                otherActualAmount = otherActualAmount.add(cusBusinessBizVO.getActualAmount());
            }
        }

        cusCcBusiness.setActualAmount(actualAmount);
        cusCcBusiness.setBookkeepingActualAmount(bookkeepingActualAmount);
        cusCcBusiness.setAddressActualAmount(addressActualAmount);
        cusCcBusiness.setOtherActualAmount(otherActualAmount);
        updateById(cusCcBusiness);
    }
}
