package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.cus.domain.entity.CusBusinessBiz;
import com.jri.biz.cus.domain.request.CusBusinessBizQuery;
import com.jri.biz.cus.domain.vo.CusBusinessBizListVO;
import com.jri.biz.cus.domain.vo.CusBusinessBizVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商机业务关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public interface CusBusinessBizMapper extends BaseMapper<CusBusinessBiz> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<CusBusinessBizListVO> listPage(@Param("query") CusBusinessBizQuery query, Page<CusBusinessBizListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusBusinessBizVO getDetailById(@Param("id") Long id);

    /**
     * 查询客户所有业务
     *
     * @param ccId 客户id
     */
    List<CusBusinessBizVO> getAllBiz(@Param("ccId") Long ccId);

    /**
     * 查询客户所有业务
     *
     * @param customerId 客户id
     */
    List<CusBusinessBizVO> getAllBizByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据商机id查询签约业务
     *
     * @param businessId 商机id
     * @return 签约业务列表
     */
    List<CusBusinessBizVO> getBizListByBusinessId(@Param("businessId") Long businessId);
}
