package com.jri.biz.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.CustomerLicenseConvert;
import com.jri.biz.domain.entity.CustomerLicense;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.request.CustomerLicenseForm;
import com.jri.biz.domain.request.CustomerLicenseQuery;
import com.jri.biz.domain.request.LicenseSaveBatchForm;
import com.jri.biz.domain.vo.CustomerLicenseListVO;
import com.jri.biz.domain.vo.CustomerLicenseVO;
import com.jri.biz.mapper.CustomerLicenseMapper;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerLicenseService extends ServiceImpl<CustomerLicenseMapper, CustomerLicense> {

    @Resource
    private CustomerLicenseMapper customerLicenseMapper;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    @Resource
    private CommonBizFileService commonBizFileService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerLicenseListVO> listPage(CustomerLicenseQuery query) {
        var page = new Page<CustomerLicenseListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerLicenseVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }

    public List<CustomerLicenseVO> getDetailByCiId(Long ciId) {
        List<CustomerLicenseVO> customerLicenseVOList = getBaseMapper().getDetailByCiId(ciId);
        // 查询每一个许可证的附件
        for (var vo : customerLicenseVOList) {
            vo.setFileList(commonBizFileService.selectByMainIdAndBizType(vo.getLicenseId(), BizType.CUSTOMER_LICENSE));
        }
        return customerLicenseVOList;
    }

    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdate(CustomerLicenseForm form) {
        CustomerLicense customerLicense = CustomerLicenseConvert.INSTANCE.convert(form);
        if (form.getLicenseId() != null) {
            customerLicense.setUpdateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        } else {
            customerLicense.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }
        saveOrUpdate(customerLicense);
        return customerLicense.getLicenseId();
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerLicenseForm form) {
        CustomerLicense customerLicense = CustomerLicenseConvert.INSTANCE.convert(form);
        return updateById(customerLicense);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        String updateBy = SecurityUtils.getLoginUser().getUser().getNickName();
        customerLicenseMapper.deleteById(id, updateBy);
        return true;
    }

    /**
     * 批量保存数据
     *
     * @param form 许可证列表
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrUpdateBatch(LicenseSaveBatchForm form) {
        List<CustomerLicenseVO> customerLicenseVOList = getBaseMapper().getDetailByCiId(form.getCiId());
        changeRecordHandle(form, customerLicenseVOList);

        // 提取id
        List<Long> idList = new ArrayList<>(customerLicenseVOList.stream().map(CustomerLicenseVO::getLicenseId).toList());

        List<CustomerLicenseForm> list = form.getList();
        for (CustomerLicenseForm customerLicenseForm : list) {
            CustomerLicense customerLicense = CustomerLicenseConvert.INSTANCE.convert(customerLicenseForm);
            // 判断有没有id
            if (ObjectUtil.isNull(customerLicense.getLicenseId())) {
                // 新增
                save(customerLicense);
                // 附件处理
                List<CommonBizFile> fileList = customerLicenseForm.getFileList();
                if (ObjectUtil.isNotEmpty(fileList)) {
                    fileList.forEach(item -> {
                        item.setBizType(BizType.CUSTOMER_LICENSE);
                        item.setMainId(customerLicense.getLicenseId());
                    });
                    commonBizFileService.saveBatch(fileList);
                }
            } else {
                updateById(customerLicense);
                // 待删除id列表中删除
                idList.remove(customerLicense.getLicenseId());
                // 附件处理
                commonBizFileService.fileSaveHandler(customerLicenseForm.getFileList(), customerLicense.getLicenseId(), BizType.CUSTOMER_LICENSE);
            }
        }
        if (ObjectUtil.isNotEmpty(idList)) {
            for (Long id : idList) {
                deleteById(id);
            }
        }
        customerChangeRecordService.sendChangeMessage(form.getCiId());
        return true;
    }

    /**
     * 变动记录
     */
    private void changeRecordHandle(LicenseSaveBatchForm form, List<CustomerLicenseVO> customerLicenseVOList) {
        String content = "编辑";
        if (ObjectUtil.isEmpty(customerLicenseVOList)) {
            content = "新增";
        }
        CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
        changeRecordForm.setCiId(form.getCiId());
        changeRecordForm.setContent(content);
        changeRecordForm.setInfoSection("许可证件");
        customerChangeRecordService.add(changeRecordForm);
    }
}
