package com.jri.biz.cus.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 线索/客户标签视图列表对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCcTagsListVO视图列表对象")
public class CusCcTagsListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}