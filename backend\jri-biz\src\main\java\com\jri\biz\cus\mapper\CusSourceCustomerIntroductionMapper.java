package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.entity.CusSourceCustomerIntroduction;

/**
 * <p>
 * 客资来源 客户介绍 关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface CusSourceCustomerIntroductionMapper extends BaseMapper<CusSourceCustomerIntroduction> {


    default CusSourceCustomerIntroduction getByMainId(Long mainId) {
        return selectOne(new LambdaQueryWrapper<CusSourceCustomerIntroduction>().eq(CusSourceCustomerIntroduction::getMainId, mainId));
    }
}
