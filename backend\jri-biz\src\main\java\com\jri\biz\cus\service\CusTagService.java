package com.jri.biz.cus.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.convert.CusTagConvert;
import com.jri.biz.cus.domain.entity.CusCcTags;
import com.jri.biz.cus.domain.entity.CusTag;
import com.jri.biz.cus.domain.request.CusTagForm;
import com.jri.biz.cus.domain.request.CusTagQuery;
import com.jri.biz.cus.domain.vo.CusTagListVO;
import com.jri.biz.cus.domain.vo.CusTagVO;
import com.jri.biz.cus.mapper.CusCcTagsMapper;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.biz.cus.mapper.CusTagMapper;
import com.jri.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * 标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
@Service
public class CusTagService extends ServiceImpl<CusTagMapper, CusTag> {

    @Resource
    private CusCcTagsMapper cusCcTagsMapper;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusTagListVO> listPage(CusTagQuery query) {
        var page = new Page<CusTagListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CusTagVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(CusTagForm form) {
        CusTag cusTag = CusTagConvert.INSTANCE.convert(form);
        saveOrUpdate(cusTag);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        LambdaQueryWrapper<CusCcTags> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusCcTags::getTagId, id);
        if (cusCcTagsMapper.selectCount(wrapper) > 0) {
            throw new ServiceException("该标签有关联线索、客户");
        }
        return removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setStatus(Long id) {
        CusTag byId = getById(id);
        if (null == byId) {
            throw new ServiceException("工单类型不存在");
        }
        // 状态取反
        String status = StrUtil.equals(byId.getStatus(), "0") ? "1" : "0";
        byId.setStatus(status);
        updateById(byId);
    }
}
