package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/4 16:47
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "人员&直投来源统计VO")
public class ClueStaffSourceDirectAnalyseVO extends BaseStaffSourceAnalyseVO {

    @ApiModelProperty(value = "线索单价")
    private List<BigDecimal> unitPriceList;

    @ApiModelProperty(value = "线索成本")
    private BigDecimal cost = BigDecimal.ZERO;

}
