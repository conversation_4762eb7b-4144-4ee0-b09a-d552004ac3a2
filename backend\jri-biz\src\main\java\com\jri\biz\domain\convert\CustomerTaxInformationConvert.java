package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerTaxInformation;
import com.jri.biz.domain.request.CustomerTaxInformationForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Mapper
public interface CustomerTaxInformationConvert {
    CustomerTaxInformationConvert INSTANCE = Mappers.getMapper(CustomerTaxInformationConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerTaxInformation convert(CustomerTaxInformationForm form);

}