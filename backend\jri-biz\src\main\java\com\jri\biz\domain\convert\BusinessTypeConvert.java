package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BusinessType;
import com.jri.biz.domain.request.BusinessTypeForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Mapper
public interface BusinessTypeConvert {
    BusinessTypeConvert INSTANCE = Mappers.getMapper(BusinessTypeConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BusinessType convert(BusinessTypeForm form);

}