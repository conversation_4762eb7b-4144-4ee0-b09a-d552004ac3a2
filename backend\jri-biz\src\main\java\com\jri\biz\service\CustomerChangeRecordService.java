package com.jri.biz.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.CustomerChangeRecordConvert;
import com.jri.biz.domain.entity.CustomerChangeRecord;
import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.request.CustomerChangeRecordQuery;
import com.jri.biz.domain.vo.CustomerChangeRecordListVO;
import com.jri.biz.domain.vo.CustomerChangeRecordVO;
import com.jri.biz.mapper.CustomerChangeRecordMapper;
import com.jri.biz.mapper.CustomerInformationMapper;
import com.jri.message.constants.MessageConstant;
import com.jri.message.domain.request.MessageDetailForm;
import com.jri.message.domain.vo.ExtraDataVO;
import com.jri.message.service.MessageDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户信息修改记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Service
public class CustomerChangeRecordService extends ServiceImpl<CustomerChangeRecordMapper, CustomerChangeRecord> {

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    private MessageDetailService messageDetailService;


    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public List<CustomerChangeRecordListVO> listPage(CustomerChangeRecordQuery query) {
        List<CustomerChangeRecordListVO> records = getBaseMapper().listPage(query);
        return records;
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerChangeRecordVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CustomerChangeRecordForm form) {
        CustomerChangeRecord customerChangeRecord = CustomerChangeRecordConvert.INSTANCE.convert(form);
        return save(customerChangeRecord);
    }

    /**
     * 编辑保存发送消息
     *
     * @param id
     */
    public void sendChangeMessage(Long id) {
        var customerInfo = customerInformationMapper.selectById(id);
        sendChangeMessage(customerInfo);
    }

    public void sendChangeMessage(CustomerInformation customerInfo) {
        var set = new HashSet<Long>();
        set.add(customerInfo.getMangerUserId());
        set.add(customerInfo.getCustomerSuccessUserId());
        set.add(customerInfo.getSponsorAccountingUserId());
        set.add(customerInfo.getCounselorUserId());
        set.remove(null);
        List<Long> sendUserIdListOfChange = set.stream().toList();
        MessageDetailForm messageDetailForm2 = new MessageDetailForm();
        messageDetailForm2.setCode(BizType.CUSTOMER_INFORMATION_CHANGE);
        messageDetailForm2.setTitleParam(null);
        Map<String, String> contentParams2 = new HashMap<>();
        contentParams2.put("customerNo", customerInfo.getCustomerNo());
        contentParams2.put("customerName", customerInfo.getCustomerName());
        messageDetailForm2.setContentParam(contentParams2);
        messageDetailForm2.setBizType("customer");
        messageDetailForm2.setMessageType(MessageConstant.ALERT);
        messageDetailForm2.setNickName(MessageConstant.SYSTEM);
        ExtraDataVO extraDataVO = new ExtraDataVO(customerInfo.getCustomerNo(), "" + customerInfo.getCustomerId());
        messageDetailForm2.setExtraDataVO(extraDataVO);
        messageDetailForm2.setRecipients(sendUserIdListOfChange);

        try {
            messageDetailService.send(messageDetailForm2);
        } catch (Exception e) {
            log.error("消息发送失败:{}");
        }
    }


    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerChangeRecordForm form) {
        // todo 完善新增/更新逻辑
        CustomerChangeRecord customerChangeRecord = CustomerChangeRecordConvert.INSTANCE.convert(form);
        return updateById(customerChangeRecord);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    /**
     * 详情
     *
     * @param ciId ciId
     */
    public List<CustomerChangeRecordListVO> getDetailByCiId(Long ciId) {
        return getBaseMapper().getDetailByCiId(ciId);
    }
}
