package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 客户信息修改记录视图对象
 *
 * <AUTHOR>
 * @since 2023-07-14
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerChangeRecordVO视图对象")
public class CustomerChangeRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}