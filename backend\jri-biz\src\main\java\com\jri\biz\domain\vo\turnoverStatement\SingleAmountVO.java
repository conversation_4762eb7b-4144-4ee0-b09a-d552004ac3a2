package com.jri.biz.domain.vo.turnoverStatement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "一次性账单金额和收款VO")
public class SingleAmountVO  implements Serializable {

    @ApiModelProperty("一次性账单金额")
    private BigDecimal singlePaymentAmount;

    @ApiModelProperty("一次性账单收款")
    private BigDecimal singleReceiptAmount;


}
