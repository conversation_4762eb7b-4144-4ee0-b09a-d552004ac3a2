package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jri.biz.domain.entity.CustomerChangeRecord;
import com.jri.biz.domain.vo.CustomerContactVO;
import com.jri.common.core.controller.BaseController;
import com.jri.common.core.domain.BaseEntity;
import com.jri.common.core.page.TableDataInfo;
import com.jri.common.core.page.TableDataInfoRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.service.CustomerChangeRecordService;
import com.jri.biz.domain.vo.CustomerChangeRecordListVO;
import com.jri.biz.domain.vo.CustomerChangeRecordVO;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.request.CustomerChangeRecordQuery;


import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 客户信息修改记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Validated
@RestController
@RequestMapping("/customerChangeRecord")
@Api(tags = "客户信息修改记录")
public class CustomerChangeRecordController extends BaseController{
    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<TableDataInfoRes> listPage(CustomerChangeRecordQuery query) {
        startPage();
        List<CustomerChangeRecordListVO> customerChangeRecordListVOS = customerChangeRecordService.listPage(query);
        BaseEntity baseEntity = new BaseEntity();
        baseEntity.setPageNum(query.getPageNum());
        baseEntity.setPageSize(query.getPageSize());
        TableDataInfoRes dataTableRes = getDataTableRes(customerChangeRecordListVOS, baseEntity);
        LambdaQueryWrapper<CustomerChangeRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerChangeRecord::getCiId, query.getCiId());
        dataTableRes.setTotal(customerChangeRecordService.count(wrapper));
        return R.ok(dataTableRes);
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerChangeRecordVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerChangeRecordService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CustomerChangeRecordForm form) {
        return R.ok(customerChangeRecordService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CustomerChangeRecordForm form) {
        return R.ok(customerChangeRecordService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerChangeRecordService.deleteById(id));
    }

    @GetMapping("/getByCiId")
    @ApiOperation("详情")
    public R<List<CustomerChangeRecordListVO>> getDetailByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerChangeRecordService.getDetailByCiId(ciId));
    }
}

