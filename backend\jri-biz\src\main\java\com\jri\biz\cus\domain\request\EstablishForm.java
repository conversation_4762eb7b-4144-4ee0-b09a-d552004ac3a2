package com.jri.biz.cus.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel(value="建档表单请求对象")
public class EstablishForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客户id")
    @NotNull(message = "客户id不能为空")
    private Long id;

    @ApiModelProperty("客户名称")
    @NotEmpty(message = "客户名称不能为空")
    private String customerName;

    @ApiModelProperty("客户经理")
    private String manger;

    @ApiModelProperty(value = "客户经理id")
    private Long mangerUserId;

    @ApiModelProperty("所属分公司")
    private String branchOffice;

    @ApiModelProperty("实际经营地址")
    private String address;

    @ApiModelProperty("客户状态")
    private String customerStatus;

    @ApiModelProperty("客户性质")
    private String customerProperty;

    @ApiModelProperty("从事行业")
    private String industry;
    //========以下附表内容=============

    @ApiModelProperty("联系人姓名")
    private String contactPerson;

    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("营业执照")
    private List<CommonBizFile> businessFileList;

    @ApiModelProperty("财税顾问(开票员)")
    private String counselor;

    @ApiModelProperty("财税顾问id")
    private Long counselorUserId;

    @ApiModelProperty("客户成功")
    private String customerSuccess;

    @ApiModelProperty("客户成功id")
    private Long customerSuccessUserId;

    @ApiModelProperty("主办会计")
    private String sponsorAccounting;

    @ApiModelProperty("主办会计id")
    private Long sponsorAccountingUserId;

    @ApiModelProperty("商机表单对象")
    private CusCcBusinessForm form;

    @ApiModelProperty("法人身份证正面")
    private String legalIdCardFront;

    @ApiModelProperty("法人身份证反面")
    private String legalIdCardBack;

    @ApiModelProperty("法人联系方式")
    private String legalPhone;

    @ApiModelProperty("监事身份证正面")
    private String supervisorIdCardFront;

    @ApiModelProperty("监事身份证反面")
    private String supervisorIdCardBack;

    @ApiModelProperty("监事联系方式")
    private String supervisorPhone;

    @ApiModelProperty("股份比例")
    private String share;

    @ApiModelProperty("其它附件")
    private List<CommonBizFile> otherDocumentFileList;

    @ApiModelProperty("法人身份证附件")
    private List<CommonBizFile> legalIdentityFileList;

    @ApiModelProperty("监事身份证附件")
    private List<CommonBizFile> supervisorIdentityFileList;

    @ApiModelProperty("股东信息列表")
    private List<CusCcShareholderInfoForm> shareholderInfoList;
}
