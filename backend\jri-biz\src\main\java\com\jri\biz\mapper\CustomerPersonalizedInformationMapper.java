package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerPersonalizedInformation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerPersonalizedInformationListVO;
import com.jri.biz.domain.vo.CustomerPersonalizedInformationVO;
import com.jri.biz.domain.request.CustomerPersonalizedInformationQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerPersonalizedInformationMapper extends BaseMapper<CustomerPersonalizedInformation> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerPersonalizedInformationListVO> listPage(@Param("query") CustomerPersonalizedInformationQuery query, Page<CustomerPersonalizedInformationListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerPersonalizedInformationVO getDetailById(@Param("id") Long id);

    List<CustomerPersonalizedInformationVO> getByCiId(@Param("ciId") Long ciId);

    Boolean deleteById(@Param("id")Long id,@Param("updateBy")String updateBy);
}
