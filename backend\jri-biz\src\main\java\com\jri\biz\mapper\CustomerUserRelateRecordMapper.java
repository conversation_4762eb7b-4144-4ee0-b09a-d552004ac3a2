package com.jri.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.CustomerUserRelateRecord;
import com.jri.biz.domain.request.CustomerUserFlowAnalyseQuery;
import com.jri.biz.domain.request.CustomerUserRelateRecordQuery;
import com.jri.biz.domain.vo.CustomerUserFlowAnalyseVO;
import com.jri.biz.domain.vo.CustomerUserRelateRecordVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface CustomerUserRelateRecordMapper extends BaseMapper<CustomerUserRelateRecord> {

    IPage<CustomerUserRelateRecordVO> listPage(@Param("query") CustomerUserRelateRecordQuery query,
                                               @Param("page") Page<CustomerUserRelateRecordVO> page);

    IPage<CustomerUserFlowAnalyseVO> customerUserFlowAnalyse(@Param("query") CustomerUserFlowAnalyseQuery query,
                                                             @Param("page") Page<CustomerUserFlowAnalyseVO> page);

    List<CustomerUserFlowAnalyseVO> customerUserFlowAnalyseList(@Param("query") CustomerUserFlowAnalyseQuery query);

    /**
     * 查询期初数量
     */
    List<Long> monthStartCount(@Param("userId") Long userId,
                               @Param("endTime") LocalDateTime endTime,
                               @Param("deptId") Long deptId,
                               @Param("role") String role);

    /**
     * 迁入数量
     */
    List<Long> migrateInCount(@Param("userId") Long userId,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime,
                              @Param("role") String role);

    /**
     * 迁出数量
     */
    List<Long> migrateOutCount(@Param("userId") Long userId,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime,
                               @Param("role") String role);

    /**
     * 新接数量
     */
    List<Long> newRelateCount(@Param("userId") Long userId,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime,
                              @Param("role") String role);

}
