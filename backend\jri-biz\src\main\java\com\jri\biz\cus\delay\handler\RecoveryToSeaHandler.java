package com.jri.biz.cus.delay.handler;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jri.biz.cus.domain.entity.CusCcContact;
import com.jri.biz.cus.domain.entity.CusCcShare;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.entity.CusSea;
import com.jri.biz.cus.domain.vo.CusSeaInventoryVO;
import com.jri.biz.cus.mapper.CusCcContactMapper;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.biz.cus.service.CusCcRecordService;
import com.jri.biz.cus.service.CusCcShareService;
import com.jri.biz.cus.service.CusSeaInventoryService;
import com.jri.biz.cus.service.CusSeaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 回收公海延迟队列处理
 *
 * <AUTHOR>
 * @since 2022/11/23
 */
@Component
@Slf4j
public class RecoveryToSeaHandler implements RedisDelayQueueHandler<Long> {

    @Resource
    private CusCcRecordService cusCcRecordService;

    @Resource
    private CusCustomerOrClueMapper cusCustomerOrClueMapper;

    @Resource
    private CusSeaInventoryService cusSeaInventoryService;

    @Resource
    private CusCcContactMapper cusCcContactMapper;

    @Resource
    private CusSeaService cusSeaService;

    @Resource
    private CusCcShareService cusCcShareService;

    /**
     * 回收公海
     *
     * @param id 线索客户id
     */
    @Override
    public void execute(Long id) {
        log.info(id + "************回收公海延迟处理开始****************");
        CusCustomerOrClue res = cusCustomerOrClueMapper.selectById(id);
        if (ObjectUtil.isEmpty(res.getSeaId())) {// 私海新建
            CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
            if (null != inventory) {
                Long seaId;
                if ("0".equals(res.getType())) {
                    seaId = inventory.getClueSeaId();
                } else {
                    seaId = inventory.getCusSeaId();
                }
                res.setSeaId(seaId);
                res.setIsSea("1");
                cusCustomerOrClueMapper.updateById(res);
            }
        } else { // 领取或者分配
            CusSea byId = cusSeaService.getById(res.getSeaId());
            if (null != byId) {// 公海存在
                res.setIsSea("1");
                cusCustomerOrClueMapper.updateById(res);
            } else { // 公海删除
                CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
                if (null != inventory) {
                    Long seaId;
                    if ("0".equals(res.getType())) {
                        seaId = inventory.getClueSeaId();
                    } else {
                        seaId = inventory.getCusSeaId();
                    }
                    res.setSeaId(seaId);
                    res.setIsSea("1");
                    cusCustomerOrClueMapper.updateById(res);
                }
            }
        }

        if ("0".equals(res.getType())) {
            CusCcContact cusCcContact = cusCcContactMapper.selectById(res.getMainContactId());
            String remark = "线索【" + cusCcContact.getContactName() + "】已超期无跟进操作，自动回收至线索公海";
            cusCcRecordService.save("回收公海(自动)", remark, id);
            // 共享记录删除
            LambdaQueryWrapper<CusCcShare> shareWrapper = new LambdaQueryWrapper<>();
            shareWrapper.eq(CusCcShare::getCcId, res.getId());
            cusCcShareService.remove(shareWrapper);
        } else {
            String remark = "客户【" + res.getCompanyName() + "】已超期无跟进操作，自动回收至客户公海";
            cusCcRecordService.save("回收公海(自动)", remark, id);
        }
        log.info(id + "************回收公海延迟处理结束****************");
    }
}
