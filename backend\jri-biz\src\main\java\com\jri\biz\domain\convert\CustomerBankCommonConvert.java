package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerBankCommon;
import com.jri.biz.domain.request.CustomerBankCommonForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 一般户信息对象转换
 *
 * <AUTHOR>
 * @since 2023-07-13
 */

@Mapper
public interface CustomerBankCommonConvert {
    CustomerBankCommonConvert INSTANCE = Mappers.getMapper(CustomerBankCommonConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerBankCommon convert(CustomerBankCommonForm form);

}