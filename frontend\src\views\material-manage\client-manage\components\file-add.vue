<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-08-21 10:50:16
 * @LastEditTime: 2023-11-24 09:39:05
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center title="新增档案" width="1200" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="企业名称" prop="customerName">
            <el-input v-model="formData.customerName" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="formData.contactPerson" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="手机号" prop="contactPhone">
            <el-input v-model="formData.contactPhone" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="财税顾问" prop="mangerUserId">
            <SelectTree v-model="formData.mangerUserId" placeholder="请选择" clearable @on-node-click="handleChangeManger" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属分公司" prop="branchOffice">
            <el-select v-model="formData.branchOffice" placeholder="请选择" clearable>
              <el-option v-for="(option, index) in branch_office" :key="index" :label="option.name" :value="option.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="实际经营地址" prop="address">
            <el-input
              v-model="formData.address"
              maxlength="1000"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="企业状态" prop="customerStatus">
            <el-select v-model="formData.customerStatus" placeholder="请选择" clearable>
              <el-option v-for="(option, index) in customer_status" :key="index" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="企业性质" prop="customerProperty">
            <el-select v-model="formData.customerProperty" clearable placeholder="请选择">
              <el-option v-for="(item, index) in customer_property" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="从事行业" prop="industry">
            <el-select
              v-model="formData.industry"
              placeholder="请选择"
              filterable
              clearable
              allow-create
              default-first-option
              @change="handleChange"
            >
              <el-option v-for="(option, index) in industry" :key="index" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="营业执照" prop="businessFileList">
            <FileUpload :isShowTip="false" v-model="formData.businessFileList" :limit="100" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="开票员" prop="counselor">
            <SelectTree
              v-model="formData.counselorUserId"
              placeholder="请选择"
              clearable
              @on-node-click="handleChangeCounselor"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户成功" prop="customerSuccess">
            <SelectTree
              v-model="formData.customerSuccessUserId"
              placeholder="请选择"
              clearable
              @on-node-click="handleChangeCustomerSuccess"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="主办会计" prop="sponsorAccounting">
            <SelectTree
              v-model="formData.sponsorAccountingUserId"
              placeholder="请选择"
              clearable
              @on-node-click="handleChangeSponsorAccounting"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 如果是工商注册 则需要其他附加资料 -->
    <Collapse title="办证资料" v-if="isCertify">
      <extraForm ref="extraFormRef" />
    </Collapse>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleAddNewCustomer">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import SelectTree from '@/components/SelectTree'
import FileUpload from '@/components/FileUpload'
import extraForm from './extra-form'
import Collapse from '@/components/Collapse'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import { associateEstablishCustomer, establishCustomerFromClient, getClientDetail } from '@/api/material-manage/client'
import useUserStore from '@/store/modules/user'
import { ElMessageBox } from 'element-plus'
import { FormValidators } from '@/utils/validate'
import { changeFileList } from '@/utils/common'
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}
const { proxy } = getCurrentInstance()
const { industry } = proxy.useDict('industry')
const { customer_property } = proxy.useDict('customer_property')
const { customer_status } = proxy.useDict('customer_status')

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    }
  },
  businessData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const isCertify = ref(false)
// 判断是否存在工商新注册的业务

if (props.businessData && props.businessData.list) {
  const nameList = props.businessData.list.map(item => item.name)

  const strList = ['内资注册', '内资注册 - 单办证', '外资注册', '外资注册 - 单办证']
  let isExisted = false
  nameList.forEach(name => {
    if (strList.includes(name)) {
      isExisted = true
    }
  })

  isCertify.value = isExisted
}

const formData = ref({
  customerName: props.data.companyName,
  contactPerson: props.data.contactName,
  contactPhone: props.data.contactPhone,
  address: props.data.address,
  customerProperty: props.data.taxNature
})
const rules = ref({
  customerName: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  contactPhone: [
    {
      required: false,
      validator: FormValidators.mobilePhone,
      trigger: 'blur'
    }
  ],
  // mangerUserId: [
  //   {
  //     required: true,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ],
  customerStatus: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  customerProperty: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  branchOffice: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ]
  // address: [
  //   {
  //     required: true,
  //     message: '请输入',
  //     trigger: 'blur'
  //   }
  // ]
})
const handleChangeManger = node => {
  formData.value.manger = node.label
}

const handleChangeCounselor = node => {
  formData.value.counselor = node.label
}
const handleChangeCustomerSuccess = node => {
  formData.value.customerSuccess = node.label
}
const handleChangeSponsorAccounting = node => {
  formData.value.sponsorAccounting = node.label
}

const branch_office = ref([])
const onGetBasicData = async () => {
  const { data } = await getCompanyTreeList({ enable: 1 })
  branch_office.value = data.records || []
}
onGetBasicData()

// 新增档案
const extraFormRef = ref()
const formRef = ref()
// 股东信息数据做处理，去除存在股东名称、手机号以及文件都不存在的行
const translateShareholderInfoList = list => {
  return list.filter(
    item => item.shareholderName || item.shareholderPhone || (item.shareholderFileList && item.shareholderFileList.length)
  )
}
const handleAddNewCustomer = async () => {
  const result1 = await formRef.value.validate()
  // 分别校验两个表单
  let result2
  if (isCertify.value) {
    result2 = await extraFormRef.value.getFormRef().validate()
  }
  // 存在附加信息
  if (result1 && result2) {
    // 该接口传参数据分为三个(一个是商机信息，还有一个是档案信息 以及办证信息)
    const requestData = {
      ...formData.value,
      form: {
        ...props.businessData,
        ccId: props.data.ccId || props.data.id
      },
      ...extraFormRef.value.formData,
      shareholderInfoList: translateShareholderInfoList(extraFormRef.value.formData.shareholderInfoList),
      id: props.data.ccId || props.data.id // 客户id
    }
    // 对文件传参格式进行处理

    if (Array.isArray(requestData.businessFileList) && requestData.businessFileList.length) {
      requestData.businessFileList = changeFileList(requestData.businessFileList)
    } else {
      requestData.businessFileList = []
    }

    // 对新建商机的文件格式处理
    // 处理各个文件都要将其变成string
    const formatStr = data => {
      console.log('data', data)
      if (data.legalIdentityFileList && data.legalIdentityFileList.length) {
        data.legalIdentityFileList = changeFileList(data.legalIdentityFileList)
      }

      if (data.supervisorIdentityFileList && data.supervisorIdentityFileList.length) {
        data.supervisorIdentityFileList = changeFileList(data.supervisorIdentityFileList)
      }
      // 修改为list
      if (Array.isArray(data.otherDocumentFileList) && data.otherDocumentFileList.length) {
        data.otherDocumentFileList = changeFileList(data.otherDocumentFileList)
      }
    }
    formatStr(requestData)
    const result = await establishCustomerFromClient(requestData)
    if (result.code === 200) {
      if (result.msg === '该企业档案已存在，是否直接关联该企业？') {
        ElMessageBox.confirm(result.msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            const result3 = await associateEstablishCustomer({
              customerName: formData.value.customerName,
              form: {
                ...props.businessData,
                ccId: props.data.ccId || props.data.id
              },
              id: props.data.ccId || props.data.id // 客户id
            })
            if (result3.code === 200) {
              proxy.$modal.msgSuccess('关联成功！')
              handleClose()
              emits('on-success', result3.data) // // 企业档案id
            } else {
              proxy.$modal.msgError('关联失败！')
            }
          })
          .catch(() => {})
      } else {
        // 新增档案成功
        proxy.$modal.msgSuccess('保存成功！')
        handleClose()
        emits('on-success', result.data) //企业档案id
      }
    }
  }
  // 不存在附加信息
  if (result1 && !isCertify.value) {
    // 该接口传参数据分为三个(一个是商机信息，还有一个是档案信息 以及办证信息)
    const requestData = {
      ...formData.value,
      form: {
        ...props.businessData,
        ccId: props.data.ccId || props.data.id
      },
      id: props.data.ccId || props.data.id // 客户id
    }
    // 对文件传参格式进行处理

    if (Array.isArray(requestData.businessFileList) && requestData.businessFileList.length) {
      requestData.businessFileList = changeFileList(requestData.businessFileList)
    } else {
      requestData.businessFileList = []
    }

    const result = await establishCustomerFromClient(requestData)
    if (result.code === 200) {
      if (result.msg === '该企业档案已存在，是否直接关联该企业？') {
        ElMessageBox.confirm(result.msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            const result3 = await associateEstablishCustomer({
              customerName: formData.value.customerName,
              form: {
                ...props.businessData,
                ccId: props.data.ccId || props.data.id
              },
              id: props.data.ccId || props.data.id // 客户id
            })
            if (result3.code === 200) {
              proxy.$modal.msgSuccess('关联成功！')
              handleClose()
              emits('on-success', result3.data) // 企业档案id
            } else {
              proxy.$modal.msgError('关联失败！')
            }
          })
          .catch(() => {})
      } else {
        // 新增档案成功
        proxy.$modal.msgSuccess('保存成功！')
        handleClose()
        emits('on-success', result.data) // 企业档案id
      }
    }
  }
}
const setData = async () => {
  const { data } = await getClientDetail(props.data.ccId || props.data.id)
  const { companyName, contactName, contactPhone, address, taxNature, industry } = data
  formData.value = Object.assign(formData.value, {
    customerName: companyName,
    contactPerson: contactName,
    contactPhone,
    address,
    industry,
    mangerUserId: useUserStore().user.userId,
    manger: useUserStore().user.nickName,
    customerProperty: taxNature
  })

  // customerName: props.data.companyName,
  // contactPerson: props.data.contactName,
  // contactPhone: props.data.contactPhone,
  // address: props.data.address,
  // customerProperty: props.data.taxNature
}
onMounted(() => {
  // 获取客户详情,将客户详情的部分数据存入
  if (props.data.ccId || props.data.id) {
    setData()
  }
})
</script>
<style lang="scss" scoped></style>
