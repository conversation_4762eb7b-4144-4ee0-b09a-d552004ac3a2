package com.jri.biz.cus.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.convert.CusSourceConvert;
import com.jri.biz.cus.domain.entity.*;
import com.jri.biz.cus.domain.request.*;
import com.jri.biz.cus.domain.vo.*;
import com.jri.biz.cus.mapper.CusSourceMapper;
import com.jri.biz.mapper.CustomerInformationMapper;
import com.jri.common.core.text.Convert;
import com.jri.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.jri.biz.cus.constants.SourceConstant.SOURCE_BIZ_TYPE;

/**
 * <p>
 * 客资来源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Service
public class CusSourceService extends ServiceImpl<CusSourceMapper, CusSource> {

    @Resource
    private CusSourceChannelService cusSourceChannelService;

    @Resource
    private CusSourceCustomerIntroductionService cusSourceCustomerIntroductionService;

    @Resource
    private CusSourceDirectService cusSourceDirectService;

    @Resource
    private CusSourcePlatformService cusSourcePlatformService;

    @Resource
    private CustomerInformationMapper customerInformationMapper;


    /**
     * 保存
     *
     * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(CusSourceBaseForm form) {
        CusSource cusSource = CusSourceConvert.INSTANCE.convert(form);
        // 检查同一层是否重复
        isExist(cusSource);

        var parentSource = getById(cusSource.getParentId());
        if (ObjectUtil.isNull(parentSource)) {
            cusSource.setAncestors("0");
            // 四大类型固定其他不需要设置业务类型
        } else {
            cusSource.setAncestors(parentSource.getAncestors() + "," + parentSource.getId());
            cusSource.setBiz(parentSource.getBiz());
        }
        saveOrUpdate(cusSource);
        // 处理其他额外信息
        if (ObjectUtil.equal(cusSource.getParentId(), 0L)) {
            return;
        }
        extraInfoSaveOrUpdate(form, cusSource.getId());
    }

    /**
     * 树形构建
     *
     * @param query 查询条件
     * @return 树
     */

    public List<CusSourceListVO> tree(CusSourceQuery query) {
        List<CusSourceListVO> resList = getBaseMapper().getList(query);

        for (var vo : resList) {
            // 处理客户介绍来源 填充客户名称
            if (vo.getParentId() != 0L && Objects.equals(vo.getBiz(), "客户介绍")) {
                var customerIntroduction = cusSourceCustomerIntroductionService.getBaseMapper().getByMainId(vo.getId());
                var customerInfo = customerInformationMapper.selectById(customerIntroduction.getCustomerId());
                if (ObjectUtil.isNotNull(customerInfo)) {
                    vo.setName(customerInfo.getCustomerName());
                }
            }
            vo.setClueCount(getBaseMapper().countByClue(false, vo.getId()));
            vo.setCustomerCount(getBaseMapper().countByClue(true, vo.getId()));
        }
        return buildTree(resList);
    }

    /**
     * 额外信息保存
     *
     * @param form   表单
     * @param mainId 主表id
     */
    private void extraInfoSaveOrUpdate(CusSourceBaseForm form, Long mainId) {
        // 平台
        if (form instanceof CusSourcePlatformForm platformForm) {
            var sourcePlatform = cusSourcePlatformService.getBaseMapper().getByMainId(mainId);
            if (Objects.isNull(sourcePlatform)) {
                sourcePlatform = new CusSourcePlatform();
                sourcePlatform.setMainId(mainId);
            }
            sourcePlatform.setPrice(platformForm.getPrice());
            cusSourcePlatformService.saveOrUpdate(sourcePlatform);
        }
        // TODO 直投
        if (form instanceof CusSourceDirectForm directForm) {
        }
        // 客户介绍
        if (form instanceof CusSourceCustomerIntroductionForm customerIntroductionForm) {
            Long customerId = customerIntroductionForm.getCustomerId();
            // 查询是否设置重复客户
            if (customerIntroductionForm.getParentId() == 0L) {
                if (getBaseMapper().countByCustomerId(customerId, mainId) > 0) {
                    throw new ServiceException("客资来源已存在");
                }
            }
            var sourceCustomerIntroduction = cusSourceCustomerIntroductionService.getBaseMapper().getByMainId(mainId);
            if (Objects.isNull(sourceCustomerIntroduction)) {
                sourceCustomerIntroduction = new CusSourceCustomerIntroduction();
                sourceCustomerIntroduction.setMainId(mainId);
            }
            sourceCustomerIntroduction.setCustomerId(customerId);
            var customerInformation = customerInformationMapper.selectById(customerId);
            sourceCustomerIntroduction.setCustomerNo(customerInformation.getCustomerNo());
            cusSourceCustomerIntroductionService.saveOrUpdate(sourceCustomerIntroduction);
        }
        // 渠道
        if (form instanceof CusSourceChannelForm channelForm) {
            var cusSourceChannel = cusSourceChannelService.getBaseMapper().getByMainId(mainId);
            if (Objects.isNull(cusSourceChannel)) {
                cusSourceChannel = new CusSourceChannel();
                cusSourceChannel.setMainId(mainId);
            }
            cusSourceChannel.setPhone(channelForm.getPhone());
            cusSourceChannel.setBankAccount(channelForm.getBankAccount());
            cusSourceChannelService.saveOrUpdate(cusSourceChannel);
        }
    }

    /**
     * 获取来源名
     *
     * @param id id
     * @return 来源名
     */
    public String getName(Long id) {
        CusSource cusSource = getById(id);
        // 特殊处理客户介绍
        if (ObjectUtil.equal(cusSource.getBiz(), "客户介绍") && cusSource.getParentId() == 0L) {
            var cusSourceCustomerIntroduction = cusSourceCustomerIntroductionService.getBaseMapper().getByMainId(id);
            var customerInfo = customerInformationMapper.selectById(cusSourceCustomerIntroduction.getCustomerId());
            return customerInfo.getCustomerName();
        }
        return cusSource.getName();
    }


    /**
     * 获取详情
     *
     * @param id id
     * @return 详情
     */
    public CusSourceVO getDetailById(Long id) {
        CusSource cusSource = getById(id);
        CusSourceVO cusSourceVO = BeanUtil.copyProperties(cusSource, CusSourceVO.class);
        if (cusSourceVO.getParentId() == 0L) {
            return cusSourceVO;
        }
        // 判断biz类型
        String biz = cusSource.getBiz();
        if (ObjectUtil.equal(biz, "平台")) {
            CusSourcePlatform cusSourcePlatform = cusSourcePlatformService.getBaseMapper().getByMainId(id);
            var resVO = BeanUtil.copyProperties(cusSource, CusSourcePlatformVO.class);
            resVO.setPrice(cusSourcePlatform.getPrice());
            return resVO;
        }
        if (ObjectUtil.equal(biz, "直投")) {
            var resVO = BeanUtil.copyProperties(cusSource, CusSourceDirectVO.class);
            List<CusSourceDirect> cusSourceDirectList = cusSourceDirectService.getBaseMapper().getListByMainId(id);
            resVO.setChargeList(cusSourceDirectList);
            return resVO;
        }
        if (ObjectUtil.equal(biz, "客户介绍")) {
            // 检查客户有没有被重复引用的
            var cusSourceCustomerIntroduction = cusSourceCustomerIntroductionService.getBaseMapper().getByMainId(id);
            var customerInfo = customerInformationMapper.selectById(cusSourceCustomerIntroduction.getCustomerId());
            var resVO = BeanUtil.copyProperties(cusSource, CusSourceCustomerIntroductionVO.class);
            resVO.setCustomerId(customerInfo.getCustomerId());
            resVO.setCustomerNo(customerInfo.getCustomerNo());
            resVO.setName(customerInfo.getCustomerName());
            return resVO;
        }
        if (ObjectUtil.equal(biz, "渠道")) {
            CusSourceChannel cusSourceChannel = cusSourceChannelService.getBaseMapper().getByMainId(id);
            var resVO = BeanUtil.copyProperties(cusSource, CusSourceChannelVO.class);
            resVO.setPhone(cusSourceChannel.getPhone());
            resVO.setBankAccount(cusSourceChannel.getBankAccount());
            return resVO;
        }
        return cusSourceVO;
    }


    /**
     * 检查是否存在
     *
     * @param cusSource 客资来源
     */
    private void isExist(CusSource cusSource) {
        if (ObjectUtil.isNull(cusSource.getName())) {
            return;
        }
        LambdaQueryWrapper<CusSource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CusSource::getName, cusSource.getName());
        wrapper.eq(CusSource::getParentId, cusSource.getParentId());
        if (ObjectUtil.isNotNull(cusSource.getId())) {
            wrapper.ne(CusSource::getId, cusSource.getId());
        }
        if (count(wrapper) > 0) {
            throw new ServiceException("客资来源已存在");
        }
    }

    /**
     * 树形构建
     *
     * @param list 列表
     * @return 树
     */
    public List<CusSourceListVO> buildTree(List<CusSourceListVO> list) {
        List<CusSourceListVO> roots = list.stream()
                .filter(item -> isRoot(item.getParentId(), list))
                .toList();
        for (CusSourceListVO root : roots) {
            setChild(root, list);
        }
        return roots;
    }

    /**
     * 设置子节点内容
     *
     * @param parent 上级节点
     * @param list   列表
     */
    private void setChild(CusSourceListVO parent, List<CusSourceListVO> list) {
        List<CusSourceListVO> childList = list.stream()
                .filter(item -> Objects.equals(parent.getId(), item.getParentId()))
                .toList();
        if (childList.isEmpty()) {
            return;
        }
        parent.setChild(childList);
        for (CusSourceListVO child : childList) {
            setChild(child, list);
        }
    }

    /**
     * 判断是否是根节点
     *
     * @param parentId 上级id
     * @param list     列表
     * @return 是否是根节点
     */
    private boolean isRoot(Long parentId, List<CusSourceListVO> list) {
        for (CusSourceListVO item : list) {
            if (parentId.equals(item.getId())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        CusSource cusSource = getById(id);

        // 基础四个不能删除
        if (Arrays.stream(SOURCE_BIZ_TYPE).anyMatch(item -> (item.equals(cusSource.getBiz()) && cusSource.getParentId() == 0L))) {
            throw new ServiceException("基础平台无法删除");
        }
        // 被其他客资引用不能删除
        if (getBaseMapper().countByClue(null, id) > 0) {
            throw new ServiceException("来源已被引用无法删除");
        }

        return removeById(id);
    }

    /**
     * 启用
     *
     * @param id id
     */
    public void enable(Long id) {
        CusSource source = getById(id);
        if (ObjectUtil.isNull(source)) {
            throw new ServiceException("客资来源不存在");
        }
        // 状态取反
        String status = StrUtil.equals(source.getEnable(), "0") ? "1" : "0";

        // 如果启用 启用所有上级
        if ("1".equals(status)) {
            String ancestors = source.getAncestors();
            Long[] ids = Convert.toLongArray(ancestors);
            getBaseMapper().updateStatusEnable(ids);
        } else { // 停用 存在启用子级 不允许停用
            if (getBaseMapper().selectEnableChildrenById(id) > 0) {
                throw new ServiceException("存在启用的子级,不允许停用");
            }
        }

        source.setEnable(status);
        updateById(source);
    }
}
