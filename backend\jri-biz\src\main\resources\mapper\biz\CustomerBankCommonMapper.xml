<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerBankCommonMapper">
    <delete id="removeByBankId">
        delete from customer_bank_common where bank_id = #{bankId}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerBankCommonListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerBankCommonVO">

    </select>
</mapper>
