package com.jri.biz.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.domain.entity.CustomerUserRelateRecord;
import com.jri.biz.domain.request.CustomerUserFlowAnalyseQuery;
import com.jri.biz.domain.request.CustomerUserRelateRecordQuery;
import com.jri.biz.domain.vo.CustomerUserFlowAnalyseVO;
import com.jri.biz.domain.vo.CustomerUserRelateRecordVO;
import com.jri.biz.mapper.CustomerContractMapper;
import com.jri.biz.mapper.CustomerUserRelateRecordMapper;
import com.jri.biz.mapper.risk.RiskCustomerMapper;
import com.jri.biz.service.risk.RiskCustomerHandleNodeService;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @since 2024/4/11 15:02
 *
 */
@Service
public class CustomerUserRelateRecordService extends ServiceImpl<CustomerUserRelateRecordMapper, CustomerUserRelateRecord> {

    @Resource
    private RiskCustomerMapper riskCustomerMapper;

    @Resource
    private RiskCustomerHandleNodeService riskCustomerHandleNodeService;

    @Resource
    private CustomerContractMapper customerContractMapper;

    /**
     * 创建记录
     *
     * @param customerId 客户id
     * @param userId 用户id
     * @param role 角色
     */
    @Transactional(rollbackFor = Exception.class)
    public void createRecord(Long customerId, Long userId, String role) {
        final LocalDateTime now = LocalDateTime.now();
        Long operationUser = SecurityUtils.getUserId();
        // 查询最新的记录
        CustomerUserRelateRecord latestRecord = getOne(Wrappers.lambdaQuery(CustomerUserRelateRecord.class)
                .eq(CustomerUserRelateRecord::getCustomerId, customerId)
                .eq(CustomerUserRelateRecord::getRole, role)
                .orderByDesc(CustomerUserRelateRecord::getOperationTime)
                .last("limit 1"));
        if (ObjectUtil.isNotNull(latestRecord)) {
            latestRecord.setEndTime(now);
            latestRecord.setEndUser(operationUser);
            updateById(latestRecord);
        }

        CustomerUserRelateRecord record = CustomerUserRelateRecord.builder()
                .customerId(customerId)
                .userId(userId)
                .preUserId(Optional.ofNullable(latestRecord).map(CustomerUserRelateRecord::getUserId).orElse(null))
                .role(role)
                .operationTime(now)
                .operationUser(operationUser)
                .build();
        save(record);
    }


    public IPage<CustomerUserRelateRecordVO> listPage(CustomerUserRelateRecordQuery query) {
        var page = new Page<CustomerUserRelateRecordVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 客户流动统计
     */
    public IPage<CustomerUserFlowAnalyseVO> customerUserFlowAnalyse(CustomerUserFlowAnalyseQuery query) {
        var page = new Page<CustomerUserFlowAnalyseVO>(query.getPageNum(), query.getPageSize());
        IPage<CustomerUserFlowAnalyseVO> result = getBaseMapper().customerUserFlowAnalyse(query, page);

        String role = query.getRole();
        YearMonth month = query.getMonth();
        LocalDateTime startTime = month.atDay(1).atStartOfDay();
        LocalDateTime endTime = month.atEndOfMonth().atTime(23, 59, 59);
        customerUserFlowAnalyseHandle(result.getRecords(), month, startTime, endTime, role);
        return result;
    }

    private void customerUserFlowAnalyseHandle(List<CustomerUserFlowAnalyseVO> result, YearMonth month,
                                               LocalDateTime startTime, LocalDateTime endTime, String role) {
        for (CustomerUserFlowAnalyseVO record : result) {
            record.setMonth(month.getMonth().getValue());
            Long userId = record.getUserId();
            // 期初        这个月原来有几家企业关联 就是上个月关联的 去掉之前正常流失和注销合同的
            LocalDateTime lastMonthEndTime = month.minusMonths(1).atEndOfMonth().atTime(23, 59, 59);
            int monthStart = monthStartCount(userId, null, lastMonthEndTime, role);
            record.setMonthStart(monthStart);
            // 新接        这个月没有到有分配的
            record.setNewRelate(getBaseMapper().newRelateCount(userId, startTime, endTime, role).size());
            // 从开始流失到挽回，小于三个月的 属流失数量-1；大于三个月的 新接数量+1
            // 正常流失     这个月提报风险的
            List<Long> normalLoseRiskIdList = riskCustomerMapper.countByRelateUserId(userId, startTime, endTime, false, role);
            List<Long> cancelLoseRiskIdList = riskCustomerMapper.countByRelateUserId(userId, startTime, endTime, true, role);
            List<Long> cancelContractCustomerIdList = customerContractMapper.getCancelContractCustomerId(userId, startTime, endTime, role);
            // 去重
            cancelContractCustomerIdList.removeAll(cancelLoseRiskIdList);
            cancelLoseRiskIdList.addAll(cancelContractCustomerIdList);
            Integer normalLoseRiskSize = normalLoseRiskIdList.size();
            Integer cancelLoseRiskSize = cancelLoseRiskIdList.size();
            Integer newRelate = record.getNewRelate();
//            for (Long id : normalLoseRiskIdList) {
//                Boolean loseToRestoreMoreThreeMonth = riskCustomerHandleNodeService.isLoseToRestoreMoreThreeMonth(id, 3);
//                if (ObjectUtil.isNull(loseToRestoreMoreThreeMonth)) {
//                    continue;
//                }
//                if (loseToRestoreMoreThreeMonth) {
//                    newRelate++;
//                } else {
//                    normalLoseRiskSize--;
//                }
//            }
            record.setNewRelate(newRelate);
            record.setNormalLose(normalLoseRiskSize);
            // 注销        这个月有注销合同+风险中有注销
            record.setCancelLose(cancelLoseRiskSize);
            // 迁入        这个月分配进来的
            record.setMigrateIn(getBaseMapper().migrateInCount(userId, startTime, endTime, role).size());
            // 迁出        这个月分配出去的 注销的数据不能选择迁出和正常流失中
            List<Long> migrateOutUserIdList = getBaseMapper().migrateOutCount(userId, startTime, endTime, role);
            Integer migrateOut = migrateOutUserIdList.size();
            for (Long customerId : migrateOutUserIdList) {
                if (cancelLoseRiskIdList.contains(customerId)) {
                    migrateOut--;
                }
            }
            record.setMigrateOut(migrateOut);
            // 期末        到这个月为止关联的客户数量
            int monthEnd = monthStartCount(userId, null, endTime, role);
            record.setMonthEnd(monthEnd);
            // 注销的数据不能选择迁出和正常流失中
            record.setNormalLose(record.getNormalLose());
        }
    }

    /**
     * 期初
     */
    private Integer monthStartCount(Long userId, LocalDateTime startTime, LocalDateTime endTime, String role) {
        List<Long> monthStartCustomerId = getBaseMapper().monthStartCount(userId, endTime, null, role);
        int monthStartCount = monthStartCustomerId.size();
        List<Long> normalLoseRiskIdList = riskCustomerMapper.countByRelateUserId(userId, startTime, endTime, null, role);
        List<Long> cancelContractCustomerIdList = customerContractMapper.getCancelContractCustomerId(userId, startTime, endTime, role);
        // 去重
        cancelContractCustomerIdList.removeAll(normalLoseRiskIdList);
        normalLoseRiskIdList.addAll(cancelContractCustomerIdList);
        for (Long customerId : monthStartCustomerId) {
            if (normalLoseRiskIdList.contains(customerId)) {
                monthStartCount--;
            }
        }
        return monthStartCount;
    }

    /**
     * 客户流动统计列表
     */
    public List<CustomerUserFlowAnalyseVO> customerUserFlowAnalyseList(CustomerUserFlowAnalyseQuery query) {
        List<CustomerUserFlowAnalyseVO> result = getBaseMapper().customerUserFlowAnalyseList(query);

        String role = query.getRole();
        YearMonth month = query.getMonth();
        LocalDateTime startTime = month.atDay(1).atStartOfDay();
        LocalDateTime endTime = month.atEndOfMonth().atTime(23, 59, 59);
        customerUserFlowAnalyseHandle(result, month, startTime, endTime, role);
        return result;
    }

}
