package com.jri.biz.domain.vo.kanban.payment;

import com.jri.biz.domain.common.ECharsPair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/4 14:23
 */
@Getter
@Setter
@ApiModel(value = "回款分析", description = "回款分析")
public class PaymentRecoveryAnalysisVO {

    @ApiModelProperty("账款金额总计")
    private BigDecimal total = BigDecimal.ZERO;

    @ApiModelProperty("实收金额")
    private BigDecimal received = BigDecimal.ZERO;

    @ApiModelProperty("欠费金额分布")
    private List<ECharsPair<String, BigDecimal>> outstanding;

}
