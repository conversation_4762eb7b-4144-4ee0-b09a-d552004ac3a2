package com.jri.biz.cus.service;

import com.jri.biz.cus.domain.entity.CusCcTags;
import com.jri.biz.cus.mapper.CusCcTagsMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.vo.CusCcTagsListVO;
import com.jri.biz.cus.domain.vo.CusCcTagsVO;
import com.jri.biz.cus.domain.request.CusCcTagsForm;
import com.jri.biz.cus.domain.request.CusCcTagsQuery;
import com.jri.biz.cus.domain.convert.CusCcTagsConvert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <p>
 * 线索/客户标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class CusCcTagsService extends ServiceImpl<CusCcTagsMapper, CusCcTags> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCcTagsListVO> listPage(CusCcTagsQuery query) {
        var page = new Page<CusCcTagsListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CusCcTagsVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusCcTagsForm form) {
        // todo 完善新增/更新逻辑
        CusCcTags cusCcTags = CusCcTagsConvert.INSTANCE.convert(form);
        return save(cusCcTags);
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CusCcTagsForm form) {
        // todo 完善新增/更新逻辑
        CusCcTags cusCcTags = CusCcTagsConvert.INSTANCE.convert(form);
        return updateById(cusCcTags);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    /**
     * 查询标签列表
     *
     * @param ccId 线索客户id
     */
    public List<String> getListByCcId(Long ccId) {
        return getBaseMapper().getListByCcId(ccId);
    }

    /**
     * 查询标签列表
     *
     * @param ccId 线索客户id
     */
    public List<CusCcTags> getTagsListByCcId(Long ccId) {
        return getBaseMapper().getTagsListByCcId(ccId);
    }
}
