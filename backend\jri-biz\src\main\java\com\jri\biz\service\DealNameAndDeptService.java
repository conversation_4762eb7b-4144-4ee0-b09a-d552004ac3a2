package com.jri.biz.service;

import com.jri.biz.domain.common.CustomerAgent;
import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.common.core.domain.entity.SysDept;
import com.jri.common.core.domain.entity.SysUser;
import com.jri.common.utils.StringUtils;
import com.jri.system.service.ISysDeptService;
import com.jri.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/10/12 17:45
 */
@Service
public class DealNameAndDeptService {

    @Resource
    private ISysUserService userService;
    @Resource
    private ISysDeptService deptService;

    /**
     * 查询人+2级部门
     *
     * @param id
     * @param name
     * @return
     */
    public CustomerAgent dealPersonNameDept(Long id, String name) {
        CustomerAgent customerAgent = new CustomerAgent();

        // 根据人员先去查部门id
        SysUser sysUser = userService.selectUserById(id);
        if (sysUser != null) {
            SysDept sysDept = deptService.selectDeptById(sysUser.getDeptId());
            if (sysDept != null) {
                String deptList = sysDept.getAncestors() + "," + sysDept.getDeptId();
                customerAgent.setDeptIdArry(deptList);

                String[] deptArry = deptList.split(",");
                ArrayList<String> deptNameList = new ArrayList<>();
                for (String deptId : deptArry) {
                    if (Long.valueOf(deptId) == 0) continue;
                    deptNameList.add(deptService.selectDeptById(Long.valueOf(deptId)).getDeptName());
                }
                String[] deptNameArry = deptNameList.toArray(new String[deptNameList.size()]);
                int length = deptArry.length;
                if (length > 2) {
                    String nameAndTwoDept = deptNameArry[length - 3] + "/" + deptNameArry[length - 2] + "/" + name;
                    customerAgent.setDeptAndName(nameAndTwoDept);
                } else {
                    //就一级部门
                    String nameAndTwoDept = deptNameArry[length - 2] + "/" + name;
                    customerAgent.setDeptAndName(nameAndTwoDept);

                }
            }
        }
        return customerAgent;
    }

    public CustomerInformation dealNameandDept(CustomerInformation customerInformation, Long mangerUserId, Long counselorUserId, Long customerSuccessUserId, Long sponsorAccountingUserId) {
        //应收台账那边4种业务员 要求展示人员的最后两级部门+人员姓名,建议在新增的时候按照格式存进数据库
        CustomerAgent mangerCustomerAgent = dealPersonNameDept(mangerUserId, customerInformation.getManger());
        customerInformation.setMangerAndDept(mangerCustomerAgent.getDeptAndName());
        CustomerAgent counselorCustomerAgent = dealPersonNameDept(counselorUserId, customerInformation.getCounselor());
        customerInformation.setCounselorAndDept(counselorCustomerAgent.getDeptAndName());
        CustomerAgent customerSuccessCustomerAgent = dealPersonNameDept(customerSuccessUserId, customerInformation.getCustomerSuccess());
        customerInformation.setCustomerSuccessAndDept(customerSuccessCustomerAgent.getDeptAndName());
        CustomerAgent sponsorAccountingCustomerAgent = dealPersonNameDept(sponsorAccountingUserId, customerInformation.getSponsorAccounting());
        customerInformation.setSponsorAccountingAndDept(sponsorAccountingCustomerAgent.getDeptAndName());

        //对部门去重
        List<String> deptIdArray = new ArrayList<>();
        if (StringUtils.isNotBlank(mangerCustomerAgent.getDeptIdArry())) {
            deptIdArray.add(mangerCustomerAgent.getDeptIdArry());
        }
        if (StringUtils.isNotBlank(counselorCustomerAgent.getDeptIdArry())) {
            deptIdArray.add(counselorCustomerAgent.getDeptIdArry());
        }
        if (StringUtils.isNotBlank(customerSuccessCustomerAgent.getDeptIdArry())) {
            deptIdArray.add(customerSuccessCustomerAgent.getDeptIdArry());
        }
        if (StringUtils.isNotBlank(sponsorAccountingCustomerAgent.getDeptIdArry())) {
            deptIdArray.add(sponsorAccountingCustomerAgent.getDeptIdArry());
        }
        String seceondDeptArry = String.join(",", deptIdArray);
        String dealRepeted = dealRepet(seceondDeptArry);

        customerInformation.setSeceondDeptArry(dealRepeted);
        return customerInformation;

    }


    /**
     * 对一个字符串内容去重
     *
     * @param str
     * @return
     */
    public static String dealRepet(String str) {
        String[] parts = str.split(",");

        Set<String> uniqueSet = new HashSet<>();

        for (String part : parts) {
            uniqueSet.add(part);
        }

        StringBuilder uniqueString = new StringBuilder();
        for (String uniquePart : uniqueSet) {
            uniqueString.append(uniquePart).append(",");
        }

        // 删除最后一个逗号
        if (uniqueString.length() > 0) {
            uniqueString.deleteCharAt(uniqueString.length() - 1);
        }
        return uniqueString.toString();
    }

}
