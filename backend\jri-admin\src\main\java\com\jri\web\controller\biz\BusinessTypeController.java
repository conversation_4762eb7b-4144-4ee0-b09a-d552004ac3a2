package com.jri.web.controller.biz;


import com.jri.biz.domain.entity.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.common.core.domain.R;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.jri.biz.service.BusinessTypeService;
import com.jri.biz.domain.vo.BusinessTypeListVO;
import com.jri.biz.domain.vo.BusinessTypeVO;
import com.jri.biz.domain.request.BusinessTypeForm;
import com.jri.biz.domain.request.BusinessTypeQuery;


import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Validated
@RestController
@RequestMapping("/businessType")
@Api(tags = "业务类型")
public class BusinessTypeController {
    @Resource
    private BusinessTypeService businessTypeService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<List<BusinessType>> listPage(BusinessTypeQuery query) {
        return R.ok(businessTypeService.getList(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BusinessType> getDetailById(@RequestParam("id") Long id) {
        return R.ok(businessTypeService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存或更新数据")
    public R<Boolean> save(@RequestBody @Valid BusinessTypeForm form) {
        return R.ok(businessTypeService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid BusinessTypeForm form) {
        return R.ok(businessTypeService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(businessTypeService.deleteById(id));
    }

    @GetMapping("/tree")
    @ApiOperation("树结构查询")
    public R<List<BusinessType>> tree(BusinessTypeQuery query) {
        return R.ok(businessTypeService.tree(query));
    }
}

