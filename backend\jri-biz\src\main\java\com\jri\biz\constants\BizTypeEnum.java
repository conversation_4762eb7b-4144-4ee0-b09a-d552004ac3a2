package com.jri.biz.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum BizTypeEnum {
    BANK_CHANGE_INFO("bank_change_info", "银行信息变更信息"),
    NOFEE_REASON("nofee_reason", "不收费原因"),
    INVOICE_INFO("invoice_info", "开票资料"),
    INTERVIEW("interview", "下户表(企业走访表)"),
    CONTRACT("contract", "合同附件"),
    BANK_ACCOUNT_OPEN("bank_account_open", "开户信息"),
    INDIVIDUAL_TAX_PASSWORD("individual_tax_password", "个税密码"),
    BUSINESS_CHANGE_INFO("business_change_info", "工商信息变更信息"),
    BUSINESS_LICENSE("business_license", "营业执照"),
    REGISTRATION_INFORMATION("registration_information", "注册资料"),
    BUSINESS_CONSTITUTION("business_constitution", "公司章程"),
    SHAREHOLDER_COMMITTEE_RESSOLUTION("shareholder_committee_ressolution", "股东会决议"),
    ADRESS("adress", "地址"),
    IDENTITY_DOCUMENT("identity_document", "身份证件"),
    BUSINESS_OTHER("business_other", "工商信息其他附件"),
    SOCIAL_ACCOUNT_OPEN("social_account_open", "社保开户信息"),
    FUND_ACCOUNT_OPEN("fund_account_open", "公积金开户信息"),
    CUSTOMER_BILLING_INFORMATION("customer_billing_information", "客户开票资料"),
    TAX_INFORMATION("tax_information", "税务资料上传"),
    TAXPAYER_IDENTIFICATION_FORM("taxpayer_identification_form", "一般纳税人认定表"),
    SHAREHOLDER_FAMILY_RELATIONSHIP("shareholder_family_relationship", "客户股东家庭关系"),
    INVOICING_SPECIAL_REQUIREMENT("invoicing_special_requirement", "开具发票特殊要求"),
    PERSONALIZED_INFORMATION_ATTACHMENT("personalized_information_attachment", "个性化信息附件"),
    CUSTOMER_FIRST_CONFIRM("customer_first_confirm", "客户首次确认"),
    CUSTOMER_LICENSE("customer_license", "许可证件"),
    BUSINESS_HANDOVER_DOCUMENT("business_handover_document", "交接单");

    private String value;
    private String description;


}




