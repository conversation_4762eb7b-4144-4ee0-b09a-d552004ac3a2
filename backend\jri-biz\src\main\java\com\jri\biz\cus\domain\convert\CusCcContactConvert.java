package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcContact;
import com.jri.biz.cus.domain.request.CusCcContactForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 联系人对象转换
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Mapper
public interface CusCcContactConvert {
    CusCcContactConvert INSTANCE = Mappers.getMapper(CusCcContactConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCcContact convert(CusCcContactForm form);

}