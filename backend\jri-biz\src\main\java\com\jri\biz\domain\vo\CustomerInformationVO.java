package com.jri.biz.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;


/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CustomerInformationVO视图对象")
public class CustomerInformationVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 主联系人id
     */
    @ApiModelProperty(value = "主联系人id")
    private Long contractMainId;

    /**
     * 客户经理
     */
    @ApiModelProperty(value = "客户经理")
    private String manger;

    /**
     * 所属分公司
     */
    @ApiModelProperty(value = "所属分公司")
    private String branchOffice;

    /**
     * 实际经营地址
     */
    @ApiModelProperty(value = "实际经营地址")
    private String address;

    /**
     * 企业联系信息备注
     */
    @ApiModelProperty(value = "企业联系信息备注")
    private String informationMark;

    /**
     * 客户状态
     */
    @ApiModelProperty(value = "客户状态")
    private String customerStatus;

    /**
     * 客户性质
     */
    @ApiModelProperty(value = "客户性质")
    private String customerProperty;

    /**
     * 不收费原因备注
     */
    @ApiModelProperty(value = "不收费原因备注")
    private String nofeeReasonMark;



    @ApiModelProperty(value = "是否废弃")
    private Integer discard;

    @ApiModelProperty(value = "废弃原因")
    private String discardReason;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "废弃时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime discardTime;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("营业执照")
    private List<CommonBizFile> businessFileList;

//    @ApiModelProperty("客户开票资料")
//    private List<CommonBizFile> invoiceInfoFileList;

    @ApiModelProperty("不收费原因")
    private String nofeeReason;

    @ApiModelProperty("下户表")
    private List<CommonBizFile> interviewFileList;

    //=== 其他关联注入信息 ===
    @ApiModelProperty(value = "月费服务")
    private String monthlyFee;
    @ApiModelProperty(value = "总欠费合计")
    private String arrears;
    @ApiModelProperty(value = "最近合同到期日")
    private String dueDate;
    @ApiModelProperty(value = "最后跟进时间")
    private String latestFollowUpDate;

    @ApiModelProperty(value = "客户类型")
    private List<String> types;

    @ApiModelProperty(value = "客户标签")
    private List<String> tags;

    /**
     * 从事行业
     */
    private String industry;

    /**
     * 联系人姓名
     */
    private String contactPerson;

    /**
     * 联系人电话
     */
    private String contactPhone;

    @ApiModelProperty("财税顾问")
    private String counselor;

    @ApiModelProperty("客户成功")
    private String customerSuccess;

    @ApiModelProperty("主办会计")
    private String sponsorAccounting;

    @ApiModelProperty(value = "客户经理id")
    private Long mangerUserId;

    @ApiModelProperty(value = "财税顾问id")
    private Long counselorUserId;

    @ApiModelProperty(value = "客户成功id")
    private Long customerSuccessUserId;

    @ApiModelProperty(value = "主办会计id")
    private Long sponsorAccountingUserId;

    @ApiModelProperty("转化来自客户名称")
    private String clientName;

    /**
     * 钱包金额
     */
    @ApiModelProperty(value = "钱包金额")
    private BigDecimal walletAmount;

    @ApiModelProperty("所属公司联系人")
    private String companyPerson;

    @ApiModelProperty("所属公司联系电话")
    private String companyPhone;

    @ApiModelProperty("所属公司地址")
    private String companyAddress;

    @ApiModelProperty(value = "企业认定")
    private String companyIdentification;

    @ApiModelProperty(value = "企业认定")
    private List<String> companyIdentificationList;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;

    public List<String> getCompanyIdentificationList() {
        String companyIdentification = getCompanyIdentification();
        if (ObjectUtil.isNotEmpty(companyIdentification)) {
            return Arrays.stream(companyIdentification.split(",")).toList();
        }
        return companyIdentificationList;
    }
}
