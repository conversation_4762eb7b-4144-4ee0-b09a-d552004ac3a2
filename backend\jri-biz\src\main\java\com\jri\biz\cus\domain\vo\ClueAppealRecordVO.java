package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线索申诉记录表视图对象
 *
 * <AUTHOR>
 * @since 2024-01-10
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="ClueAppealRecordVO视图对象")
public class ClueAppealRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("线索id")
    private Long clueId;

    @ApiModelProperty("线索名称")
    private String clueName;

    @ApiModelProperty("来源名称")
    private String sourceName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("申述原因")
    private String remark;

    @ApiModelProperty("创建人用户id")
    private Long createUserId;

    @ApiModelProperty("创建人用户姓名")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}