package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/1/10 15:22
 */
@Data
@NoArgsConstructor
@ApiModel(value="线索申诉更新请求对象")
public class ClueAppealEndForm {

    @NotNull(message = "线索id不能为空")
    @ApiModelProperty("线索id")
    private Long clueId;

    @NotNull(message = "是否成功不能为空")
    @ApiModelProperty("是否成功")
    private Boolean success;

}
