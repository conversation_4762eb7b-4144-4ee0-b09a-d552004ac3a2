package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "模板字段列表对象")
public class TempFieldListVO {

    @ApiModelProperty("字段中文名")
    private String fieldName;

    @ApiModelProperty("字段名")
    private String fieldCode;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("是否必填")
    private Boolean required;

    @ApiModelProperty("子对象")
    private TempFieldListVO extra;
}
