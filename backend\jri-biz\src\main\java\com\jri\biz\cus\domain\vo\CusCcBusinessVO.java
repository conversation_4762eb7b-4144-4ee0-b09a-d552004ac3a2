package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商机视图对象
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCcBusinessVO视图对象")
public class CusCcBusinessVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("商机名称")
    private String name;

    @ApiModelProperty("客户id")
    private Long ccId;

    @ApiModelProperty("预计成交金额")
    private String expectAmount;

    @ApiModelProperty("预计成交时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("销售阶段")
    private String stage;

    @ApiModelProperty("阶段百分比")
    private String stagePercentage;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("实际成交金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("赢单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime winTime;

    @ApiModelProperty("输单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loseTime;

    @ApiModelProperty("输单原因")
    private String reason;

    @ApiModelProperty("输单描述")
    private String remark;

    @ApiModelProperty("公司名称(客户名称)")
    private String companyName;

    @ApiModelProperty("最近跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFollowTime;

//    @ApiModelProperty("当前处理人")
//    private Long currentUserId;

    @ApiModelProperty("当前处理人姓名(跟进人)")
    private String currentUserName;

    @ApiModelProperty("业务列表")
    private List<CusBusinessBizVO> list;

    @ApiModelProperty("联系人")
    private String contactName;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("账单id列表")
    private List<String> paymentIds;

    @ApiModelProperty("账单编号")
    private String paymentNos;

    @ApiModelProperty("企业档案id")
    private Long customerId;

    @ApiModelProperty("企业档案编号")
    private String customerNo;
}