package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商机业务关系视图对象
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusBusinessBizVO视图对象")
public class CusBusinessBizVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 商机id
     */
    @ApiModelProperty("商机id")
    private Long businessId;

    @ApiModelProperty("实际成交金额")
    private BigDecimal actualAmount;

    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品类型名称")
    private String typeName;
}