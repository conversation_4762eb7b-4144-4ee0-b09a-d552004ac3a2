package com.jri.web.controller.biz;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.request.*;
import com.jri.biz.domain.vo.*;
import com.jri.biz.service.CustomerInformationService;
import com.jri.biz.service.CustomerUserRelateRecordService;
import com.jri.biz.service.ExportService;
import com.jri.biz.service.ProgressService;
import com.jri.biz.service.finance.ImportService;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.poi.ExcelUtil;
import com.jri.common.utils.poi.NewExcelUtil;
import com.jri.common.utils.uuid.Seq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
//@Validated
@RestController
@RequestMapping("/customerInformation")
@Api(tags = "客户基础信息")
public class CustomerInformationController {
    @Resource
    private CustomerInformationService customerInformationService;
    @Resource
    private ProgressService progressService;
    @Resource
    private ImportService importService;
    @Resource
    private CustomerUserRelateRecordService customerUserRelateRecordService;

    @Resource
    private ExportService exportService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerInformationListVO>> listPage(CustomerInformationQuery query) {
        return R.ok(customerInformationService.listPage(query));
    }

    @PostMapping("/export")
    @ApiOperation("导出")
    public R<Long> exportAnnualReport(@Validated @RequestBody CustomerInformationQuery query) {
        query.setPageSize(-1);
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "企业档案.xlsx");
        var id = progressService.create(ProgressType.CUSTOMER_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        customerInformationService.export(fileName, query, id);
        return R.ok(id);
    }

    @GetMapping("/getAssociatedCustomerInformationList")
    @ApiOperation("关联客户查询列表")
    public R<IPage<AssociatedCustomerInformationListVO>> getAssociatedCustomerInformationList(CustomerInformationQuery query) {
        return R.ok(customerInformationService.getAssociatedCustomerInformationList(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerInformationVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerInformationService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Long> save(@RequestBody @Valid CustomerInformationForm form) {
        return R.ok(customerInformationService.addOrUpdate(form));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CustomerInformationForm form) {
        return R.ok(customerInformationService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerInformationService.deleteById(id));
    }

    @DeleteMapping("/deleteByIds")
    @ApiOperation("根据id删除")
    public R<Void> deleteByIds(@RequestParam("ids") List<Long> ids) {
        customerInformationService.deleteByIds(ids);
        return R.ok();
    }

    @GetMapping("/getByCiId")
    @ApiOperation("通过ciId获取详情")
    public R<CustomerInformationVO> getDetailByCiId(@RequestParam("id") Long ciId) {
        return R.ok(customerInformationService.getDetailByCiId(ciId));
    }

    @PostMapping("/upload")
    @ApiOperation("导入")
    public R<Long> upload(@RequestPart("file") MultipartFile file,
                          @RequestParam(name = "coverFlag", defaultValue = "false") Boolean coverFlag) throws IOException {
        if (file == null || file.isEmpty()) {
            return R.fail("文件为空");
        }
        var id = progressService.create(ProgressType.CUSTOMER_UPLOAD, file.getOriginalFilename(), ProgressType.UPLOAD);
        customerInformationService.upload(file.getInputStream(), id, coverFlag);
//        SecurityUtils
        return R.ok(id);
    }

    @PostMapping("/changeManger")
    @ApiOperation("变更经理")
    public R<Void> changeManger(@RequestBody @Valid ChangeMangerForm form) {
        customerInformationService.changeManger(form);
        return R.ok();
    }

    @GetMapping("/statistics")
    @ApiOperation("客户统计")
    public R<CustomerInformationStatisticsVO> statistics() {
        return R.ok(customerInformationService.statistics());
    }

    @PostMapping("/changePerson")
    @ApiOperation("主办会计/财税顾问/客户成功变更")
    public R<Void> changePerson(@RequestBody @Valid ChangePersonForm form) {
        customerInformationService.changePerson(form);
        return R.ok();
    }

    @PostMapping("/getContactImportTemplate")
    @ApiOperation("获取联系人导入模板")
    public void getContactImportTemplate(HttpServletResponse response) {
        var template = importService.getContactImportTemplate();
        if (ObjectUtil.isEmpty(template)) {
            throw new ServiceException("没有数据可导出");
        }
        ExcelUtil<CustomerContactImportTemplate> util = new ExcelUtil<>(CustomerContactImportTemplate.class);
        util.exportExcel(response, template, "联系人导入模板");
    }

    @PostMapping("/importContact")
    @ApiOperation("联系人导入")
    public R<Void> importContact(@RequestPart("file") MultipartFile file) throws Exception {
        ImportService.fileCheck(file);
        String originalFilename = file.getOriginalFilename();
        if (null != originalFilename) {
            if (!(originalFilename.contains(".xlsx") || originalFilename.contains(".xls"))) {
                throw new ServiceException("导入失败！文件类型错误");
            }
        }
        var excel = new NewExcelUtil<>(CustomerContactImportTemplate.class);
        var list = excel.importExcel(file.getInputStream(), excel.getExcelFieldNames().toArray(String[]::new));
        var id = progressService.create(ProgressType.CUSTOMER_CONTACT_UPLOAD, file.getOriginalFilename(), ProgressType.UPLOAD);
        importService.importContact(list, id);
        return R.ok();
    }

    @PostMapping("/getCancellationRecord")
    @ApiOperation("企业注销记录查询")
    public R<CancellationRecordVO> getCancellationRecord(Long customerId) {
        return R.ok(customerInformationService.getCancellationRecord(customerId));
    }

    @GetMapping("/getUserRelateRecord")
    @ApiOperation("获取人员分配记录")
    public R<IPage<CustomerUserRelateRecordVO>> listPage(CustomerUserRelateRecordQuery query) {
        return R.ok(customerUserRelateRecordService.listPage(query));
    }

    @GetMapping("/customerUserFlowAnalyse")
    @ApiOperation("客户流动统计")
    public R<IPage<CustomerUserFlowAnalyseVO>> customerUserFlowAnalyse(CustomerUserFlowAnalyseQuery query) {
        return R.ok(customerUserRelateRecordService.customerUserFlowAnalyse(query));
    }

    @PostMapping("/customerUserFlowAnalyseExport")
    @ApiOperation("客户流动统计导出")
    public R<Void> customerUserFlowAnalyseExport(CustomerUserFlowAnalyseQuery query) {
        String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), "客户流动统计.xlsx");
        var id = progressService.create(ProgressType.CUSTOMER_USER_FLOW_ANALYSE_DOWNLOAD, fileName, ProgressType.DOWNLOAD);
        exportService.customerUserFlowAnalyseExport(fileName, query, id);
        return R.ok();
    }

    @GetMapping("/getRelateFiles")
    @ApiOperation(value = "查询相关附件信息")
    public R<List<CommonBizFile>> getRelateFiles(Long customerId) {
        return R.ok(customerInformationService.getRelateFiles(customerId));
    }

}

