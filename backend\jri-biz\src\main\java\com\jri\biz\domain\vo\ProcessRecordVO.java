package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2024/4/9 10:00
 *
 */
@Data
public class ProcessRecordVO {

    @ApiModelProperty("步骤1")
    private String stage1;

    @ApiModelProperty("步骤1用时")
    private Long stage1Time;

    @ApiModelProperty("步骤2")
    private String stage2;

    @ApiModelProperty("步骤2用时")
    private Long stage2Time;

    @ApiModelProperty("步骤3")
    private String stage3;

    @ApiModelProperty("步骤3用时")
    private Long stage3Time;

    @ApiModelProperty("步骤4")
    private String stage4;

    @ApiModelProperty("步骤4用时")
    private Long stage4Time;

    @ApiModelProperty("步骤5")
    private String stage5;

    @ApiModelProperty("步骤5用时")
    private Long stage5Time;

    @ApiModelProperty("步骤6")
    private String stage6;

    @ApiModelProperty("步骤6用时")
    private Long stage6Time;

    @ApiModelProperty("步骤7")
    private String stage7;

    @ApiModelProperty("步骤7用时")
    private Long stage7Time;

    @ApiModelProperty("步骤8")
    private String stage8;

    @ApiModelProperty("步骤8用时")
    private Long stage8Time;

    @ApiModelProperty("步骤9")
    private String stage9;

    @ApiModelProperty("步骤9用时")
    private Long stage9Time;

    @ApiModelProperty("步骤10")
    private String stage10;

    @ApiModelProperty("步骤10用时")
    private Long stage10Time;

    @ApiModelProperty("步骤11")
    private String stage11;

    @ApiModelProperty("步骤11用时")
    private Long stage11Time;

    @ApiModelProperty("步骤12")
    private String stage12;

    @ApiModelProperty("步骤12用时")
    private Long stage12Time;

}