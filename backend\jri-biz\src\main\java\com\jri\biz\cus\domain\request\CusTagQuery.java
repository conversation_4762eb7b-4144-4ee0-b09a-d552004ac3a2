package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 标签查询类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="标签查询对象")
public class CusTagQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("标签名称")
    private String name;

    @ApiModelProperty("状态0-停用1-正常")
    private String status;
}
