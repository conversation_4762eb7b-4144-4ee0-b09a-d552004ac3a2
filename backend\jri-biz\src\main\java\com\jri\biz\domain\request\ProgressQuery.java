package com.jri.biz.domain.request;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/6/6 15:19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("进度查询")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgressQuery extends BaseQuery {

    private String type;

    private Integer status;

    private String fileName;

    private String createTime;
}
