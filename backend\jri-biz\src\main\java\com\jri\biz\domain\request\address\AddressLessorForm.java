package com.jri.biz.domain.request.address;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 出租人 表单请求对象
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Data
@NoArgsConstructor
@ApiModel(value = "出租人表单请求对象")
public class AddressLessorForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("出租人姓名")
    private String lessorName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账户")
    private String bankAccount;

}
