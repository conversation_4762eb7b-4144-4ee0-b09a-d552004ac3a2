package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.UserCustomerBindRecordForm;
import com.jri.biz.domain.request.UserCustomerBindRecordQuery;
import com.jri.biz.domain.vo.UserCustomerBindRecordListVO;
import com.jri.biz.service.UserCustomerBindRecordService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 用户客户绑定记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Validated
@RestController
@RequestMapping("/userCustomerBindRecord")
@Api(tags = "用户客户绑定记录")
public class UserCustomerBindRecordController {
    @Resource
    private UserCustomerBindRecordService userCustomerBindRecordService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<UserCustomerBindRecordListVO>> listPage(UserCustomerBindRecordQuery query) {
        return R.ok(userCustomerBindRecordService.listPage(query));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid UserCustomerBindRecordForm form) {
        userCustomerBindRecordService.add(form);
        return R.ok();
    }

    @PostMapping("/unbind")
    @ApiOperation("解绑")
    public R<Void> unbind(@RequestBody @NotEmpty List<Long> idList) {
        userCustomerBindRecordService.unbind(idList);
        return R.ok();
    }

}

