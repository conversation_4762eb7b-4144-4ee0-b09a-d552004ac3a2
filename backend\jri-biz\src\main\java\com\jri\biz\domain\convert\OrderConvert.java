package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.Order;
import com.jri.biz.domain.request.OrderForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 工单对象转换
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Mapper
public interface OrderConvert {
    OrderConvert INSTANCE = Mappers.getMapper(OrderConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    Order convert(OrderForm form);

}