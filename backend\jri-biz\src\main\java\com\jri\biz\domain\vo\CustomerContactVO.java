package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 客户联系人视图对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerContactVO视图对象")
public class CustomerContactVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 客户主表id
     */
    private Long ciId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 部门
     */
    private String dept;

    /**
     * 职务
     */
    private String post;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 是否决策人
     */
    private Integer isLeader;

    /**
     * 联系人详情
     */
    private String details;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 是否常用联系人0-否1-是
     */
    private Integer isOften;

    /**
     * 性别0-未知 1-男 2-女
     */
    private String sex;

    /**
     * QQ
     */
    private String qq;

    /**
     * 生日
     */
    private String birthday;
}