package com.jri.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.CustomerBankCommonConvert;
import com.jri.biz.domain.convert.CustomerBankConvert;
import com.jri.biz.domain.entity.CustomerBank;
import com.jri.biz.domain.entity.CustomerBankCommon;
import com.jri.biz.domain.request.CustomerBankCommonForm;
import com.jri.biz.domain.request.CustomerBankForm;
import com.jri.biz.domain.request.CustomerBankQuery;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.vo.CustomerBankListVO;
import com.jri.biz.domain.vo.CustomerBankVO;
import com.jri.biz.mapper.CustomerBankMapper;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerBankService extends ServiceImpl<CustomerBankMapper, CustomerBank> {

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CustomerBankMapper customerBankMapper;

    @Resource
    private CompletenessService completenessService;

    @Resource
    private CustomerBankCommonService customerBankCommonService;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerBankListVO> listPage(CustomerBankQuery query) {
        var page = new Page<CustomerBankListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerBankVO getDetailById(Long id) {
        CustomerBankVO customerBankVO = new CustomerBankVO();
        customerBankVO = getBaseMapper().getDetailById(id);
        //查询附件
        List<CommonBizFile> accountOpenFileList = commonBizFileService.selectByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_ACCOUNT_OPEN);
        List<CommonBizFile> changeInfoFileList = commonBizFileService.selectByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_CHANGE_INFO);
        customerBankVO.setAccountOpenFileList(accountOpenFileList);
        customerBankVO.setChangeInfoFileList(changeInfoFileList);
        return customerBankVO;
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdate(CustomerBankForm form) {
        //记录老的bizFile
        //附件 银行开户信息表 变更信息
        commonBizFileService.fileSaveHandler(form.getCiId(), BizType.BANK_ACCOUNT_OPEN, form.getAccountOpenFileList(), form.getCoverFlag());
        commonBizFileService.fileSaveHandler(form.getCiId(), BizType.BANK_CHANGE_INFO, form.getChangeInfoFileList(), form.getCoverFlag());
        CustomerBank customerBank = CustomerBankConvert.INSTANCE.convert(form);
        String content = "编辑";
        if (null == customerBank.getBankId()) {
            content = "新增";
        }
        saveOrUpdate(customerBank);
        customerChangeRecordService.sendChangeMessage(customerBank.getCiId());
        CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
        changeRecordForm.setCiId(customerBank.getCiId());
        changeRecordForm.setContent(content);
        changeRecordForm.setInfoSection("银行信息");
        customerChangeRecordService.add(changeRecordForm);
        // 一般户信息列表
        customerBankCommonService.removeByBankId(customerBank.getBankId());
        List<CustomerBankCommonForm> commonList = form.getCommonList();
        if (null != commonList) {
            List<CustomerBankCommon> customerBankCommonList = new ArrayList<>();
            commonList.forEach(item -> {
                CustomerBankCommon customerBankCommon = CustomerBankCommonConvert.INSTANCE.convert(item);
                customerBankCommon.setBankId(customerBank.getBankId());
                customerBankCommonList.add(customerBankCommon);
            });
            customerBankCommonService.saveBatch(customerBankCommonList);
        }
        completenessService.UpdateCompleteness(customerBank.getCiId());
        return customerBank.getBankId();
    }


    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerBankForm form) {
        // todo 完善新增/更新逻辑
        CustomerBank customerBank = CustomerBankConvert.INSTANCE.convert(form);
        return updateById(customerBank);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        //删除附件
        String updateBy = SecurityUtils.getLoginUser().getUser().getNickName();
        CustomerBankVO customerBankVO = getDetailById(id);
        commonBizFileService.deleteByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_ACCOUNT_OPEN);
        commonBizFileService.deleteByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_CHANGE_INFO);
        return customerBankMapper.deleteByIdAndUpdateBy(id, updateBy);
    }

    public CustomerBankVO getDetailByCiId(Long ciId) {
        var customerBankVO = getBaseMapper().getDetailByCiId(ciId);
        if (null != customerBankVO) {
            //查询附件
            List<CommonBizFile> accountOpenFileList = commonBizFileService.selectByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_ACCOUNT_OPEN);
            List<CommonBizFile> changeInfoFileList = commonBizFileService.selectByMainIdAndBizType(customerBankVO.getCiId(), BizType.BANK_CHANGE_INFO);
            customerBankVO.setAccountOpenFileList(accountOpenFileList);
            customerBankVO.setChangeInfoFileList(changeInfoFileList);
            // 一般户列表
            LambdaQueryWrapper<CustomerBankCommon> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerBankCommon::getBankId, customerBankVO.getBankId());
            customerBankVO.setCommonList(customerBankCommonService.list(wrapper));
        }
        return customerBankVO;
    }
}
