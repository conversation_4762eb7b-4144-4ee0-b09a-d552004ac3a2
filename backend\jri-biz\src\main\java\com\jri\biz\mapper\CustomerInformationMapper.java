package com.jri.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.domain.request.ChangeMangerForm;
import com.jri.biz.domain.request.ChangePersonForm;
import com.jri.biz.domain.request.CustomerInformationQuery;
import com.jri.biz.domain.vo.AssociatedCustomerInformationListVO;
import com.jri.biz.domain.vo.CustomerContactImportTemplate;
import com.jri.biz.domain.vo.CustomerInformationListVO;
import com.jri.biz.domain.vo.CustomerInformationVO;
import com.jri.biz.domain.vo.customerUser.UserCustomerInformationListVO;
import com.jri.common.core.domain.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerInformationMapper extends BaseMapper<CustomerInformation> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<CustomerInformationListVO> listPage(@Param("page") Page<CustomerInformationListVO> page, @Param("query") CustomerInformationQuery query);

    /**
     * 企业用户所有企业列表查询
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 查询结果
     */
    IPage<UserCustomerInformationListVO> userCustomerListPage(@Param("page") Page<UserCustomerInformationListVO> page, @Param("query") CustomerInformationQuery query);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerInformationVO getDetailById(@Param("id") Long id);

    void delete(Long id);

    String selectMaxCode(String code);

    /**
     * 变更经理
     *
     * @param form 表单
     */
    void changeManger(@Param("form") ChangeMangerForm form);

    Long getCountByCustomerStatus(@Param("customerStatus") String customerStatus);

    Long getCountByCustomerProperty(@Param("customerProperty") String customerProperty);

    /**
     * 主办会计变更
     *
     * @param form 表单
     */
    void changeSponsorAccounting(@Param("form") ChangePersonForm form);

    /**
     * 财税顾问变更
     *
     * @param form 表单
     */
    void changeCounselor(@Param("form") ChangePersonForm form);

    /**
     * 客户成功变更
     *
     * @param form 表单
     */
    void changeCustomerSuccess(@Param("form") ChangePersonForm form);

    IPage<AssociatedCustomerInformationListVO> getAssociatedCustomerInformationList(@Param("page") Page<CustomerInformationListVO> page, @Param("query") CustomerInformationQuery query);

    /**
     * 当月跟多少家企业关联
     *
     * @param customerSuccessUserId 客户成功用户id
     * @return 数量
     */
    Long getMonthLinkCustomer(@Param("customerSuccessUserId") Long customerSuccessUserId,
                              @Param("customerSuccess") String customerSuccess,
                              @Param("deptId") Long deptId,
                              @Param("role") String role);


    /**
     * 按企业认定查询
     *
     * @param type 类型
     */
    Long getCountByIdentification(@Param("type") String type);

    /**
     * 获取导出模板
     *
     * @return 列表
     */
    List<CustomerContactImportTemplate> getContactImportTemplate();

    /**
     * 根据客户编号查询客户信息
     *
     * @param customerNo 客户编号
     * @return 客户信息
     */
    default CustomerInformation getByCustomerNo(String customerNo) {
        var wrapper = new LambdaQueryWrapper<CustomerInformation>();
        wrapper.eq(CustomerInformation::getCustomerNo, customerNo);
        return selectOne(wrapper);
    }

    /**
     * 根据客户名称获取客户信息
     *
     * @param customerName 客户名称
     * @return 客户id
     */
    CustomerInformation getCustomerByCustomerName(String customerName);

    /**
     * 根据企业用户id获取财税顾问用户
     * @param userId 用户id
     * @return 财税顾问id
     */
    SysUser getCounselorUserByCustomerUserId(Long userId);
}
