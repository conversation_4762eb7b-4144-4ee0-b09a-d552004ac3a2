package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusCcShareholderInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusCcShareholderInfoListVO;
import com.jri.biz.cus.domain.vo.CusCcShareholderInfoVO;
import com.jri.biz.cus.domain.request.CusCcShareholderInfoQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 股东信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
public interface CusCcShareholderInfoMapper extends BaseMapper<CusCcShareholderInfo> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusCcShareholderInfoListVO> listPage(@Param("query") CusCcShareholderInfoQuery query, Page<CusCcShareholderInfoListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCcShareholderInfoVO getDetailById(@Param("id") Long id);
}
