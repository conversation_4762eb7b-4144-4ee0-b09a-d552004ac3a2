package com.jri.biz.domain.request.address;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 房屋产权证明查询类
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="房屋产权证明查询对象")
public class AddressPropertyOwnershipQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("房本名称")
    private String name;

    @ApiModelProperty("出租人姓名")
    private String lessorName;

    @ApiModelProperty("领用状态 闲置-idle 已到期-expired 使用中-in_use")
    private String useStatus;

}
