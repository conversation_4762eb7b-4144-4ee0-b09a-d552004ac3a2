package com.jri.biz.domain.request.address;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 地址供应商查询类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="地址供应商查询对象")
public class AddressSupplierQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("供应商名称")
    private String supplier;

    @ApiModelProperty("收费类型")
    private String feeType;
}
