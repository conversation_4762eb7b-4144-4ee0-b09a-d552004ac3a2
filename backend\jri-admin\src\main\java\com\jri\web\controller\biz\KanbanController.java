package com.jri.web.controller.biz;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.biz.domain.common.ECharsPair;
import com.jri.biz.domain.request.MonthlySalesBreakdownQuery;
import com.jri.biz.domain.vo.ClueChangeVO;
import com.jri.biz.domain.vo.CollectionAmountVO;
import com.jri.biz.domain.vo.DataBriefingVO;
import com.jri.biz.domain.vo.SalesFunnelVO;
import com.jri.biz.domain.vo.kanban.license.LicenseAnalysisVO;
import com.jri.biz.domain.vo.kanban.payment.*;
import com.jri.biz.service.kanban.LicenseAnalysisService;
import com.jri.biz.service.kanban.PaymentAnalysisService;
import com.jri.biz.service.kanban.SalesAnalysisService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Month;
import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/5 14:40
 */
@RestController
@RequestMapping(value = "/kanban")
@Api(value = "看板服务", tags = "看板服务")
public class KanbanController {

    @Resource
    private LicenseAnalysisService licenseAnalysisService;

    @Resource
    private PaymentAnalysisService paymentAnalysisService;

    @Resource
    private SalesAnalysisService salesAnalysisService;


    @GetMapping("/license/licenseAnalysis")
    @ApiOperation("办证面板/办证数量 许可证统计")
    public R<LicenseAnalysisVO> licenseAnalysis() {
        return R.ok(licenseAnalysisService.licenseAnalysis());
    }

    @GetMapping("/license/licenseEfficiency")
    @ApiOperation("办证面板/办证效率计算")
    public R<List<ECharsPair<String, CountVO>>> licenseEfficiency(
            @JsonFormat(pattern = "yyyy-MM") YearMonth yearMonth) {
        return R.ok(licenseAnalysisService.licenseEfficiency(yearMonth));
    }

    @GetMapping("/payment/newCustomer")
    @ApiOperation("收款面板/新增户数")
    public R<NewCustomerAnalysisVO> newCustomer(String period) {
        return R.ok(paymentAnalysisService.newCustomer(period));
    }

    @GetMapping("/payment/customerChangeStatistics")
    @ApiOperation("收款面板/客户数量变化")
    public R<CustomerChangeAnalysisVO> customerChangeStatistics() {
        return R.ok(paymentAnalysisService.customerChangeStatistics());
    }

    @GetMapping("/payment/agencyBookkeepingUnitPrice")
    @ApiOperation("收款面板/服务客户记账单价")
    public R<List<ECharsPair<Integer, CountVO>>> agencyBookkeepingUnitPrice(Integer year) {
        return R.ok(paymentAnalysisService.agencyBookkeepingUnitPrice(year));
    }

    @GetMapping("/payment/paymentRecoveryAnalysis")
    @ApiOperation("收款面板/当月回款分析")
    public R<PaymentRecoveryAnalysisVO> paymentRecoveryAnalysis(
            @JsonFormat(pattern = "yyyy-MM") YearMonth yearMonth) {
        return R.ok(paymentAnalysisService.paymentRecoveryAnalysis(yearMonth));
    }

    @GetMapping("/payment/monthlySalesBreakdown")
    @ApiOperation("收款面板/月营业额明细")
    public R<MonthlySalesBreakdownVO> monthlySalesBreakdown(Integer year) {
        MonthlySalesBreakdownQuery query = new MonthlySalesBreakdownQuery();
        query.setStartDate(YearMonth.of(year, Month.JANUARY));
        query.setEndDate(YearMonth.of(year, Month.DECEMBER));
        return R.ok(paymentAnalysisService.monthlySalesBreakdown(query));
    }

    @GetMapping("/payment/monthlySalesBreakdown2")
    @ApiOperation("收款面板/月营业额明细2")
    public R<MonthlySalesBreakdownVO> monthlySalesBreakdown2(MonthlySalesBreakdownQuery query) {
        return R.ok(paymentAnalysisService.monthlySalesBreakdown(query));
    }


    @GetMapping("/sales/dataBriefing")
    @ApiOperation("销售面板 数据简报")
    public R<DataBriefingVO> dataBriefing() {
        return R.ok(salesAnalysisService.dataBriefing());
    }

    @GetMapping("/sales/salesFunnelVO")
    @ApiOperation("销售面板 销售漏斗")
    public R<SalesFunnelVO> salesFunnelVO() {
        return R.ok(salesAnalysisService.salesFunnelVO());
    }

    @GetMapping("/sales/businessFunnelVO")
    @ApiOperation("销售面板 商机漏斗")
    public R<List<ECharsPair<String, Long>>> businessFunnelVO() {
        return R.ok(salesAnalysisService.businessFunnelVO());
    }

    @GetMapping("/sales/clueChange")
    @ApiOperation("销售面板 线索转化")
    public R<ClueChangeVO> clueChange() {
        return R.ok(salesAnalysisService.clueChange());
    }

    @GetMapping("/sales/collectionAmount")
    @ApiOperation("销售面板 收单金额平均客单价")
    public R<List<ECharsPair<Integer, CollectionAmountVO>>> collectionAmount(
            @ApiParam("年份") @RequestParam Integer year) {
        return R.ok(salesAnalysisService.collectionAmount(year));
    }

    @GetMapping("/sales/performanceAmountPie")
    @ApiOperation("销售面板 业绩金额饼图")
    public R<List<ECharsPair<String, Long>>> performanceAmountPie(
            @ApiParam("年月yyyy-MM") @RequestParam String yearMonth) {
        return R.ok(salesAnalysisService.performanceAmountPie(yearMonth));
    }

    @GetMapping("/sales/performanceAmountLine")
    @ApiOperation("销售面板 业绩金额折线图")
    public R<List<ECharsPair<Integer, CollectionAmountVO>>> performanceAmountLine(
            @ApiParam("年月yyyy-MM") @RequestParam String yearMonth,
            @ApiParam("产品id") @RequestParam Long productId) {
        return R.ok(salesAnalysisService.performanceAmountLine(yearMonth, productId));
    }
}
