package com.jri.biz.domain.request.address;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.YearMonth;

/**
 * 地址申请查询类
 *
 * <AUTHOR>
 * @since 2023-12-01
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="地址申请查询对象")
public class AddressApplyQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("状态 待选地址 暂无地址 已完成 已结束")
    private String status;

    @ApiModelProperty(value = "账期结束时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth paymentEndTime;

    private String dataScopeSql;

}
