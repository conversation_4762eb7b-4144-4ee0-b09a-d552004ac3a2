package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerTaxRebateIdentifiedForm;
import com.jri.biz.domain.request.CustomerTaxRebateIdentifiedQuery;
import com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedListVO;
import com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedVO;
import com.jri.biz.service.CustomerTaxRebateIdentifiedService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerTaxRebateIdentified")
@Api(tags = "退税信息")
public class CustomerTaxRebateIdentifiedController {
    @Resource
    private CustomerTaxRebateIdentifiedService customerTaxRebateIdentifiedService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerTaxRebateIdentifiedListVO>> listPage(CustomerTaxRebateIdentifiedQuery query) {
        return R.ok(customerTaxRebateIdentifiedService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerTaxRebateIdentifiedVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerTaxRebateIdentifiedService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid CustomerTaxRebateIdentifiedForm form) {
        return R.ok(customerTaxRebateIdentifiedService.add(form));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CustomerTaxRebateIdentifiedForm form) {
        return R.ok(customerTaxRebateIdentifiedService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerTaxRebateIdentifiedService.deleteById(id));
    }
}

