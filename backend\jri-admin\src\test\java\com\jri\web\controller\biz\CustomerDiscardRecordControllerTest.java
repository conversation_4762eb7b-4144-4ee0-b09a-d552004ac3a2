package com.jri.web.controller.biz;

import com.alibaba.fastjson2.JSON;
import com.jri.biz.domain.request.CustomerDiscardRecordForm;
import com.jri.biz.domain.request.CustomerInformationForm;
import com.jri.framework.security.context.AuthenticationContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * <AUTHOR>
 * @since 2023/6/2 13:14
 */
@SpringBootTest
class CustomerDiscardRecordControllerTest {
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private CustomerDiscardRecordController controller;

    @BeforeEach
    void before() {
        var authenticationToken = new UsernamePasswordAuthenticationToken("admin", "admin123");
        AuthenticationContextHolder.setContext(authenticationToken);
        var authentication = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }

    @Test
    void save() throws Exception {
        var body = new CustomerDiscardRecordForm();
        body.setDiscard(1);
        body.setCiId(27L);
        body.setDiscardReason("王五2");
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .post("/customerDiscardRecord/save")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(body));
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void getDetailByCiId() throws Exception {
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/customerDiscardRecord/getByCiId")
                .param("ciId","27");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void getListByCiId() throws Exception{
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/customerDiscardRecord/getListByCiId")
                .param("ciId","27");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }
}