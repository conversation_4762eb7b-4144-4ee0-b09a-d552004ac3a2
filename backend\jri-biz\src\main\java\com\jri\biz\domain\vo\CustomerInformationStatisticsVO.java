package com.jri.biz.domain.vo;

import com.jri.biz.domain.common.ECharsPair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "客户统计")
public class CustomerInformationStatisticsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客户状态列表")
    private List<ECharsPair<String, Long>> customerStatusList;

    @ApiModelProperty("企业性质列表")
    private List<ECharsPair<String, Long>> customerPropertyList;

    @ApiModelProperty("企业认定列表")
    private List<ECharsPair<String, Long>> companyIdentificationList;

}
