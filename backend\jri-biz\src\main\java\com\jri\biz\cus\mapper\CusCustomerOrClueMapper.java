package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.request.*;
import com.jri.biz.cus.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 线索/客户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface CusCustomerOrClueMapper extends BaseMapper<CusCustomerOrClue> {

    /**
     * 我的线索
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<CusCustomerOrClueListVO> myClueList(@Param("query") CusCustomerOrClueQuery query, Page<CusCustomerOrClueListVO> page);

    /**
     * 共享线索
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<CusCustomerOrClueListVO> shareClueList(@Param("query") CusCustomerOrClueQuery query, Page<CusCustomerOrClueListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCustomerOrClueVO getDetailById(@Param("id") Long id);

    /**
     * 查询线索共享人id列表
     *
     * @param ccId 线索客户id
     */
    List<Long> getUserIds(@Param("ccId") Long ccId);

    /**
     * 线索公海列表
     *
     * @param query 查询参数
     * @param page  分页参数
     */
    IPage<CusClueInSeaListVO> clueInSeaList(@Param("query") CusClueInSeaListQuery query, Page<CusCustomerOrClueListVO> page);

    /**
     * 客户列表
     *
     * @param query 查询参数
     * @param page  分页参数
     */
    IPage<CusCustomerListVO> customerList(@Param("query") CusCustomerListQuery query, Page<CusCustomerListVO> page);

    /**
     * 查询附加信息
     *
     * @param id id
     */
    CusCustomerOtherInfoForm getOtherInfo(@Param("id") Long id);

    /**
     * 查询附加信息
     *
     * @param customerId 客户id
     */
    CusCustomerOtherInfoForm getOtherInfoByCustomerId(@Param("customerId") Long customerId);

    /**
     * 客户公海列表
     *
     * @param query 查询参数
     * @param page  分页参数
     */
    IPage<CusCustomerInSeaListVO> customerInSeaList(@Param("query") CusCustomerListQuery query, Page<CusCustomerListVO> page);

    /**
     * 更新回收时长
     *
     * @param id 线索客户id
     * @param duration 回收时长
     */
    void updateDuration(@Param("id") Long id, @Param("duration") Integer duration);

    /**
     * 线索数量
     *
     */
    Long getClueNum();

    /**
     * 客户数量
     *
     */
    Long getCusNum();

    /**
     * 所有私海线索
     * 
     */
    List<CusCustomerOrClue> getAllClue();

    /**
     * 所有私海客户
     * 
     */
    List<CusCustomerOrClue> getAllCus();

    /**
     * 转企业数量
     * 
     */
    List<Long> getChangeNum();

    /**
     * 转企业数量 直接创建的客户不统计
     *
     */
    List<Long> getChangeNumNoCus();

    /**
     * 转客户数
     *
     */
    Long getChangeToCusNum();

    /**
     * 添加商机客户数量
     *
     */
    List<Long> getAddBusinessNum();

    /**
     * 线索数量 按来源
     *
     */
    Long getClueNumBySource(@Param("source") String source);

    /**
     * 线索数量 按来源
     *
     */
    Long getChangeToCusNumBySource(@Param("source") String source);

    List<String> getCompanyNameByCiId(@Param("customerId") Long customerId);

    /**
     * 所有私海线索
     *
     * @param query 查询参数
     * @param page 分页参数
     */
    IPage<CusCustomerOrClueListVO> allClueList(@Param("query") AllCusCustomerOrClueQuery query, @Param("page") Page<CusCustomerOrClueListVO> page);

    /**
     * 所有私海客户列表
     *
     * @param query 查询参数
     * @param page 分页参数
     */
    IPage<CusCustomerListVO> allCustomerList(@Param("query") AllCusCustomerListQuery query, @Param("page") Page<CusCustomerListVO> page);
}
