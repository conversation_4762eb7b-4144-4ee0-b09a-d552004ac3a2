package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerBusinessInformation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerBusinessInformationListVO;
import com.jri.biz.domain.vo.CustomerBusinessInformationVO;
import com.jri.biz.domain.request.CustomerBusinessInformationQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerBusinessInformationMapper extends BaseMapper<CustomerBusinessInformation> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerBusinessInformationListVO> listPage(@Param("query") CustomerBusinessInformationQuery query, Page<CustomerBusinessInformationListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerBusinessInformationVO getDetailById(@Param("id") Long id);

    Boolean deleteById(@Param("id") Long id,@Param("updateBy")String username);

    CustomerBusinessInformationVO getDetailByCiId(@Param("ciId") Long ciId);
}
