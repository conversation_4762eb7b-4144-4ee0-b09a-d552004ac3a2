package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "SalesFunnelVO视图对象")
public class SalesFunnelVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索数量")
    private Long clueNum;

    @ApiModelProperty("转客户线索数")
    private Long changeToCusNum;

    @ApiModelProperty("添加商机客户数")
    private Long addBusinessNum;

    @ApiModelProperty("转企业客户数")
    private Long changeToCompanyNum;
}
