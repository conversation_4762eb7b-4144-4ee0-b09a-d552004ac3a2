package com.jri.biz.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/8/18 9:04
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LicenseBizStageEnum {

    PENDING("pending", "派工", 1),

    DATA_COLLECTION("data_collection", "资料收集", 2),

    LICENSE_PROCESSING("license_processing", "证照办理", 3),

    INFO_UPLOADING("info_uploading", "资料上传", 4),

    BANK_ACCOUNT_OPEN("bank_account_open", "银行开户", 5),

    COMPLETED("completed", "已完成", 6),

    BUSINESS_CANCELLATION("business_cancellation", "工商注销", 7),

    BANK_CANCELLATION("bank_cancellation", "银行注销", 8),

    CHANGE_PROGRESS("change_progress", "变更进度", 9),

    DATA_UPDATES("data_updates", "资料更新", 10),

    DEPRECATED("deprecated", "已作废", 99);

    private String code;

    private String value;

    private int sort;

    public static LicenseBizStageEnum getEnumByCode(String code) {
        for (LicenseBizStageEnum eventNodeEnum : LicenseBizStageEnum.values()) {
            if (eventNodeEnum.getCode().equals(code)) {
                return eventNodeEnum;
            }
        }
        return null;
    }

    public static String searchValueByCode(String code) {
        for (LicenseBizStageEnum bizStageEnum : LicenseBizStageEnum.values()) {
            if (bizStageEnum.getCode().equals(code)) {
                return bizStageEnum.getValue();
            }
        }
        return null;
    }

}
