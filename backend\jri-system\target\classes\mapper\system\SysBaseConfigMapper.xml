<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.system.mapper.SysBaseConfigMapper">

    <resultMap type="SysBaseConfig" id="SysBaseConfigResult">
        <result property="baseconfigId"    column="baseConfig_id"    />
        <result property="baseconfigName"    column="baseConfig_name"    />
        <result property="type"    column="type"    />
        <result property="baseConfigStatus"    column="baseConfig_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="baseConfigContext"    column="baseConfig_context"    />
    </resultMap>

    <sql id="selectSysBaseConfigVo">
        select baseConfig_id, baseConfig_name, type,baseConfig_status, create_by, create_time, update_by, update_time,baseConfig_context from sys_base_config
    </sql>

    <select id="selectSysBaseConfigList" parameterType="SysBaseConfig" resultMap="SysBaseConfigResult">
        <include refid="selectSysBaseConfigVo"/>
        <where>
             is_deleted=0
            <if test="baseconfigName != null  and baseconfigName != ''"> and baseConfig_name like concat('%', #{baseconfigName}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="baseConfigStatus != null "> and baseConfig_status = #{baseConfigStatus}</if>
        </where>
    </select>

    <select id="selectSysBaseConfigByBaseconfigId" parameterType="Long" resultMap="SysBaseConfigResult">
        <include refid="selectSysBaseConfigVo"/>
        where baseConfig_id = #{baseconfigId}
    </select>

    <insert id="insertSysBaseConfig" parameterType="SysBaseConfig" useGeneratedKeys="true" keyProperty="baseconfigId">
        insert into sys_base_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="baseconfigName != null and baseconfigName != ''">baseConfig_name,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="baseConfigContext != null">baseConfig_context,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="baseconfigName != null and baseconfigName != ''">#{baseconfigName},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="baseConfigContext != null">#{baseConfigContext},</if>
         </trim>
    </insert>

    <update id="updateSysBaseConfigStatus" parameterType="SysBaseConfig">
        update sys_base_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="baseConfigStatus != null">baseConfig_status = #{baseConfigStatus},</if>
        </trim>
        where baseConfig_id = #{baseconfigId}
    </update>

    <delete id="deleteSysBaseConfigByBaseconfigId" parameterType="Long">
        update sys_base_config set is_deleted='1' where baseConfig_id = #{baseconfigId}
    </delete>

    <delete id="deleteSysBaseConfigByBaseconfigIds" parameterType="Long">
        update sys_base_config set is_deleted='1' where baseConfig_id in
        <foreach item="baseconfigId" collection="array" open="(" separator="," close=")">
            #{baseconfigId}
        </foreach>
    </delete>
</mapper>
