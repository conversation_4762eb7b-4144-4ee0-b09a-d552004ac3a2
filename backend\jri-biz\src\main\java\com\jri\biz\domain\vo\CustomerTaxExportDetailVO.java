package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;


/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CustomerTaxExportDetailVO视图对象")
public class CustomerTaxExportDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}