package com.jri.web.controller.biz;


import com.jri.biz.domain.request.BasicBankForm;
import com.jri.biz.domain.request.BasicBankQuery;
import com.jri.biz.domain.vo.BasicBankListVO;
import com.jri.biz.domain.vo.BasicBankVO;
import com.jri.biz.service.BasicBankService;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.file.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.util.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 银行信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Validated
@RestController
@RequestMapping("/basicBank")
@Api(tags = "银行信息")
public class BasicBankController {
    @Resource
    private BasicBankService basicBankService;

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BasicBankVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(basicBankService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid BasicBankForm form) {
        basicBankService.add(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(basicBankService.deleteById(id));
    }

    @GetMapping("/tree")
    @ApiOperation("树结构查询")
    public R<List<BasicBankListVO>> tree(BasicBankQuery query) {
        return R.ok(basicBankService.tree(query));
    }

    @PostMapping("/enable")
    @ApiOperation("状态设置")
    public R<Void> enable(@RequestParam("id") Long id) {
        basicBankService.enable(id);
        return R.ok();
    }

    @PostMapping("/importTemp")
    @ApiOperation(value = "获取导入模板")
    public void download2Oss(HttpServletResponse response, HttpServletRequest request) {
        try {
            ClassPathResource classPathResource = new ClassPathResource("importTemp/银行信息.xls");
            InputStream inputStream = classPathResource.getInputStream();
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, "银行信息.xls");
            ServletOutputStream outputStream = response.getOutputStream();
            IOUtils.copy(inputStream, outputStream);
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    public R<Void> upload(@RequestPart("file") MultipartFile file) throws IOException {
        if(file == null || file.isEmpty()){
            return R.fail("文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        if (null != originalFilename) {
            if (!(originalFilename.contains(".xlsx") || originalFilename.contains(".xls"))) {
                throw new ServiceException("导入失败！文件类型错误");
            }
        }
        basicBankService.upload(file.getInputStream());
        return R.ok();
    }
}

