<!--
 * @Description: 新增或者编辑联系人
 * @Author: thb
 * @Date: 2023-08-17 13:20:18
 * @LastEditTime: 2023-11-07 10:57:51
 * @LastEditors: thb
-->
<template>
  <el-button type="primary" @click="handleAddContact">新增</el-button>
  <el-form ref="formRef" :model="data" label-position="top">
    <Collapse :title="`联系人${index + 1}`" v-for="(item, index) in data.tableData" :key="item">
      <template #button>
        <el-button v-if="index !== 0" type="danger" @click.stop="handleDelete(index)">删除</el-button>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item
            label="姓名"
            :prop="`tableData.` + index + '.name'"
            :rules="{
              required: true,
              message: '请输入',
              trigger: 'blur'
            }"
          >
            <el-input v-model="item.name" length="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="手机号"
            :prop="`tableData.` + index + '.phone'"
            :rules="{
              required: true,
              trigger: 'blur',
              validator: phoneValidate
            }"
          >
            <el-input v-model="item.phone" length="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="关键决策人">
            <el-radio-group v-model="item.isLeader">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item></el-col
        >
        <el-col :span="6">
          <el-form-item label="常用联系人">
            <el-radio-group v-model="item.isOften">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="职位">
            <el-input v-model="item.post" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="微信">
            <el-input v-model="item.wechat" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="QQ"
            :prop="`tableData.` + index + '.qq'"
            :rules="{
              required: false,
              trigger: 'blur',
              validator: FormValidators.qq
            }"
          >
            <el-input v-model="item.qq" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="邮箱"
            :prop="`tableData.` + index + '.email'"
            :rules="{
              required: false,
              trigger: 'blur',
              validator: FormValidators.email
            }"
          >
            <el-input v-model="item.email" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="性别">
            <el-radio-group v-model="item.sex">
              <el-radio label="0">未知</el-radio>
              <el-radio label="1">男</el-radio>
              <el-radio label="2">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="生日">
            <el-date-picker
              v-model="item.birthday"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              type="date"
              placeholder="请选择"
          /></el-form-item>
        </el-col>
      </el-row>
    </Collapse>
  </el-form>
</template>
<script setup>
import { getClientContactList, saveBatchClientContactList } from '@/api/material-manage/client'
import Collapse from '@/components/Collapse'
import { FormValidators } from '@/utils/validate'
import { saveCustomerContact, getCustomerContactByCiId } from '@/api/customer/file'

const emits = defineEmits(['on-edit'])
// 手机号检验
const phoneValidate = (rules, value, callback) => {
  if (value) {
    FormValidators.mobilePhone(rules, value, callback)
  } else {
    callback(new Error('请输入'))
  }
}

const props = defineProps({
  id: String,
  data: {
    type: Object,
    default: () => {
      return {
        tableData: [],
        rules: {}
      }
    }
  }
})

// 新增联系人
const handleAddContact = () => {
  props.data.tableData.push({
    // contactName: '',
    // contactPhone: '',
    name: '',
    phone: '',
    isLeader: 0,
    isOften: 0,
    post: '',
    wechat: '',
    qq: '',
    email: '',
    sex: '0',
    birthday: ''
  })
}

// 删除联系人
const handleDelete = index => {
  props.data.tableData.splice(index, 1)
}
// 监听是否是编辑
const edit = inject('isEdit')
const getDetail = async flag => {
  const { data } = await getCustomerContactByCiId(props.id)
  data.forEach(item => {
    item.isLeader = !item.isLeader ? 0 : 1
    item.sex = !item.sex ? '0' : item.sex
  })
  props.data = Object.assign(props.data, {
    tableData: data.filter(item => item) || []
  })
  console.log('contactTable', props.data)
}
watch(
  edit,
  async () => {
    if (edit.value) {
      // await getDetail(true)
      await getDetail()
      emits('on-edit')
    }
  },
  {
    immediate: true
  }
)
const formRef = ref()
const handleValidate = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!')
      return false
    }
  })
  return result
}

defineExpose({
  handleValidate
})
onMounted(() => {})
</script>
<style lang="scss" scoped></style>
