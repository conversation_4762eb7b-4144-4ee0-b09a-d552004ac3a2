package com.jri.biz.service.visit;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.visit.CustomerVisitConvert;
import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.domain.entity.visit.CustomerVisit;
import com.jri.biz.domain.request.visit.CustomerVisitForm;
import com.jri.biz.domain.request.visit.CustomerVisitQuery;
import com.jri.biz.domain.vo.visit.CustomerVisitListVO;
import com.jri.biz.mapper.CustomerInformationMapper;
import com.jri.biz.mapper.visit.CustomerVisitMapper;
import com.jri.biz.service.CommonBizFileService;
import com.jri.common.annotation.DataScope;
import com.jri.common.core.domain.CommonBizFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Service
public class CustomerVisitService extends ServiceImpl<CustomerVisitMapper, CustomerVisit> {

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    @DataScope(deptAlias = "d", userAlias = "user")
    public IPage<CustomerVisitListVO> listPage(CustomerVisitQuery query) {
        var page = new Page<CustomerVisitListVO>(query.getPageNum(), query.getPageSize());
        //检查是否有sql注入行为
        if (ObjectUtil.isNotNull(query.getOrderByColumn())) {
            SqlInjectionUtils.check(query.getOrderByColumn());
        }
        if (ObjectUtil.isNotNull(query.getIsAsc())) {
            SqlInjectionUtils.check(query.getIsAsc());
        }
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerVisitListVO getDetailById(Long id) {
        CustomerVisitListVO vo = getBaseMapper().getById(id);
        if (ObjectUtil.isNotNull(vo)) {
            vo.setFileList(commonBizFileService.selectByMainIdAndBizType(id, BizType.VISIT));
        }
        return vo;
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CustomerVisitForm form) {
        // todo 完善新增/更新逻辑
        CustomerVisit customerVisit = CustomerVisitConvert.INSTANCE.convert(form);
        // 保存附件
        List<CommonBizFile> fileList = form.getFileList();
        if (ObjectUtil.isNotEmpty(fileList)) {
            fileList.forEach(item -> {
                item.setMainId(form.getId());
                item.setBizType(BizType.VISIT);
            });
            commonBizFileService.saveBatch(fileList);
        }
        saveOrUpdate(customerVisit);
        // 更新客户经纬度
        if (ObjectUtil.isAllNotEmpty(form.getId(), form.getLng(), form.getLat(), form.getLocationName())) {
            customerInformationMapper.update(null, Wrappers.lambdaUpdate(CustomerInformation.class)
                    .eq(CustomerInformation::getCustomerId, form.getCustomerId())
                    .set(CustomerInformation::getLng, form.getLng())
                    .set(CustomerInformation::getLat, form.getLat())
                    .set(CustomerInformation::getAddress, form.getLocationName()));
        }
        return true;
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerVisitForm form) {
        // todo 完善新增/更新逻辑
        CustomerVisit customerVisit = CustomerVisitConvert.INSTANCE.convert(form);
        return updateById(customerVisit);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    /**
     * 批量添加
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(CustomerVisitForm form) {
        List<CustomerVisit> customerVisitList = new ArrayList<>();
        for (Long customerId : form.getCustomerIdList()) {
            CustomerVisit customerVisit = CustomerVisitConvert.INSTANCE.convert(form);
            customerVisit.setCustomerId(customerId);
            customerVisitList.add(customerVisit);
        }
        saveBatch(customerVisitList);
    }

    /**
     * 列表
     */
    @DataScope(deptAlias = "d", userAlias = "user")
    public List<CustomerVisitListVO> getList(CustomerVisitQuery query) {
        return getBaseMapper().getList(query);
    }
}
