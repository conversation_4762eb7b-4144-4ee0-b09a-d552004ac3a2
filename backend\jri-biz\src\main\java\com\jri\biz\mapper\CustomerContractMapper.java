package com.jri.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.CustomerContract;
import com.jri.biz.domain.request.ContractDetailQuery;
import com.jri.biz.domain.request.CustomerContractQuery;
import com.jri.biz.domain.request.DocumentNoForm;
import com.jri.biz.domain.request.ReviewCustomerContractQuery;
import com.jri.biz.domain.vo.ContractListVO;
import com.jri.biz.domain.vo.CustomerContractListVO;
import com.jri.biz.domain.vo.CustomerContractVO;
import com.jri.biz.domain.vo.ReviewCustomerContractListVO;
import com.jri.biz.domain.vo.customerUser.UserCustomerContractListVO;
import com.jri.common.core.domain.entity.SysRole;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerContractMapper extends BaseMapper<CustomerContract> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<CustomerContractListVO> listPage(@Param("query") CustomerContractQuery query, Page<CustomerContractListVO> page);

    /**
     * 用户绑定企业合同查询（需要签名合同）
     *
     * @param query 查询条件
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<UserCustomerContractListVO> userCustomerContractListPage(@Param("query") CustomerContractQuery query, Page<UserCustomerContractListVO> page);


    IPage<CustomerContractListVO> listPageEndTime(@Param("query") CustomerContractQuery query, Page<CustomerContractListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id       id
     * @param showFlag 是否显示删除合同
     * @return 结果
     */
    CustomerContractVO getDetailById(@Param("id") Long id, @Param("showFlag") Boolean showFlag);

    /**
     * 根据id查询明细(查看权限校验)
     *
     * @param query query
     * @return 结果
     */
    CustomerContractVO getDetailByIdCheck(ContractDetailQuery query);

    Boolean delete(@Param("id") Long id, @Param("updateBy") String username);

    /**
     * 根据客户id列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     */
    IPage<ContractListVO> listByCiId(@Param("query") CustomerContractQuery query, @Param("page") Page<ContractListVO> page);

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     */
    IPage<ReviewCustomerContractListVO> listMyCreate(@Param("query") ReviewCustomerContractQuery query, @Param("page") Page<CustomerContractListVO> page);

    /**
     * 由我审批
     *
     * @param query 查询参数
     * @param page  分页参数
     */
    IPage<ReviewCustomerContractListVO> listMyAudit(@Param("query") ReviewCustomerContractQuery query, @Param("page") Page<CustomerContractListVO> page);

    Boolean getIsDelete(@Param("id") Long id);

    /**
     * 根据客户id和业务类型查询合同列表
     *
     * @param customerId 客户id
     * @param typeName   业务类型
     * @return 合同列表
     */
    List<CustomerContract> getContractListByCustomerIdAndType(@Param("customerId") Long customerId,
                                                              @Param("typeName") String typeName);


    /**
     * 根据客户状态和产品类型查询数量
     *
     * @param customerStatus 客户状态
     * @param productName    产品名称
     * @return 数量
     */
    Long countByCustomerStatus(@Param("customerStatus") String customerStatus, @Param("productName") String productName);

    /**
     * 合同金额 按年份 产品id 月份
     *
     * @param productId 产品id
     * @param year      年份
     * @param month     月份
     */
    Long getNumByProductIdAndYear(@Param("productId") Long productId, @Param("year") String year, @Param("month") String month);

    /**
     * 合同金额 按年份 产品id 月份 产品或业务id
     *
     * @param year      年份
     * @param month     月份
     * @param productId 产品id
     */
    Long getTotalCostByYearAndMonthAndProductId(@Param("year") Integer year, @Param("month") Integer month, @Param("productId") Long productId);

    /**
     * 查询企业记账合同数量
     *
     * @param ciId 企业id
     */
    Long getAccountNum(@Param("ciId") Long ciId);

    /**
     * 获取最近合同到期时间
     *
     * @param customerId 客户id
     * @return 最近合同到期时间
     */
    LocalDate getLatestExpireTime(@Param("customerId") Long customerId);

    /**
     * 编辑归档号
     *
     * @param form form
     */
    void updateDocumentNo(@Param("form") DocumentNoForm form);

    /**
     * 查询用户是否有归档员角色
     *
     * @param userId userId
     */
    List<SysRole> getDocRole(@Param("userId") Long userId);


    /**
     * 根据产品名称和客户id查询合同数据
     *
     * @param productName 产品名称
     * @param customerId 客户id
     * @return 合同数据
     */
    CustomerContract getContractByProductNameAndCustomerId(@Param("productName") String productName,
                                                           @Param("customerId") Long customerId);

    /**
     * 审核列表查询
     * @param query 查询条件
     * @param page 分页条件
     * @return 分页
     */
    IPage<ReviewCustomerContractListVO> auditList(@Param("query") ReviewCustomerContractQuery query,
                                                  @Param("page") Page<CustomerContractListVO> page);

    /**
     * 查询注销合同的客户id
     */
    List<Long> getCancelContractCustomerId(@Param("userId") Long userId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("role") String role);
}
