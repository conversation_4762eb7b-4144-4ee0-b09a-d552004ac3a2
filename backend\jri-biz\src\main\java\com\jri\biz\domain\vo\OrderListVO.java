package com.jri.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工单视图列表对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "OrderListVO视图列表对象")
public class OrderListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @Excel(name = "工单编号", sort = 1)
    @ApiModelProperty("工单编号")
    private String orderNo;

    @ApiModelProperty("客户id")
    private Long ciId;

    @Excel(name = "关联客户", sort = 4)
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @Excel(name = "客户编号", sort = 5)
    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @Excel(name = "紧急状态", sort = 6, readConverterExp = "0=一般,1=紧急")
    @ApiModelProperty("紧急状态0-一般 1-紧急")
    private String isUrgent;

    @ApiModelProperty("期望完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("工单类型id")
    private Long orderTypeId;

    @Excel(name = "工单类型", sort = 2)
    @ApiModelProperty("工单类型名称")
    private String orderTypeName;

    @ApiModelProperty("补充说明")
    private String supplementExplain;

    @ApiModelProperty("工单标题")
    private String orderTitle;

    @ApiModelProperty("地区")
    private String address;

    @ApiModelProperty("工单内容")
    private String content;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("完成反馈/回退说明")
    private String feedbackOrDescription;

    @ApiModelProperty("指派给")
    private Long executor;

    @Excel(name = "当前指派给", sort = 7)
    @ApiModelProperty("指派给")
    private String executorName;

    @Excel(name = "工单状态", sort = 3, readConverterExp = "0=待完成,1=已完成,2=回退,3=异常")
    @ApiModelProperty("工单状态0-待完成 1-完成 2-回退")
    private String orderStatus;

    @Excel(name = "完成时间", sort = 10, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    @Excel(name = "创建时间", sort = 8, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Excel(name = "创建人", sort = 9)
    @ApiModelProperty("创建人")
    private String createBy;
}