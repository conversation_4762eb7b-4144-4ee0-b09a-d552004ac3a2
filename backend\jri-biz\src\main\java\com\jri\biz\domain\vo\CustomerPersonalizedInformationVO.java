package com.jri.biz.domain.vo;

import com.jri.biz.domain.entity.CustomerPersonalizedInformation;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CustomerPersonalizedInformationVO视图对象")
public class CustomerPersonalizedInformationVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    public CustomerPersonalizedInformationVO(CustomerPersonalizedInformation entity) {

    }

    /**
     * 个性化信息表id
     */
    private Long id;

    /**
     * 客户信息主表id
     */
    private Long ciId;

    /**
     * 客户性格
     */
    private String personality;

    /**
     * 客户类型
     */
    private List<String> types;

    /**
     * 其他标签
     */
    private List<String> tags;


    /**
     * 客户年龄层次
     */
    private String ageLevel;

    /**
     * 客户性格补充
     */
    private String personalityComplement;

    /**
     * 资料收取要求
     */
    private String collectionRequirement;

    /**
     * 财务处理要求
     */
    private String dealRequirement;

    /**
     * 开票特殊需求
     */
    private String billingDemand;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    private Boolean isDeleted;

    @ApiModelProperty("客户股东家庭关系")
    private List<CommonBizFile> shareholderFamilyRelationshipFileList;

    @ApiModelProperty("附件")
    private List<CommonBizFile> personalizedInformationAttachmentFileList;


}
