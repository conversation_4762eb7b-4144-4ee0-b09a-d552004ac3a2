package com.jri.biz.domain.request.suggestion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;


/**
 * 企业用户建议 表单请求对象
 *
 * <AUTHOR>
 * @since 2024-01-25
 */

@Data
@NoArgsConstructor
@ApiModel(value = "企业用户建议回复请求对象")
public class UserSuggestionResponseForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空")
    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "建议内容不能为空")
    @ApiModelProperty("建议内容")
    private String responseData;

}
