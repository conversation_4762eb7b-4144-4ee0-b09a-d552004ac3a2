package com.jri.web.controller.biz;


import com.jri.biz.domain.request.BasicTaxBureauForm;
import com.jri.biz.domain.request.BasicTaxBureauQuery;
import com.jri.biz.domain.vo.BasicTaxBureauListVO;
import com.jri.biz.domain.vo.BasicTaxBureauVO;
import com.jri.biz.service.BasicTaxBureauService;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.file.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.util.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 税务局信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Validated
@RestController
@RequestMapping("/basicTaxBureau")
@Api(tags = "税务局信息")
public class BasicTaxBureauController {
    @Resource
    private BasicTaxBureauService basicTaxBureauService;

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BasicTaxBureauVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(basicTaxBureauService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid BasicTaxBureauForm form) {
        basicTaxBureauService.add(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(basicTaxBureauService.deleteById(id));
    }

    @GetMapping("/tree")
    @ApiOperation("树结构查询")
    public R<List<BasicTaxBureauListVO>> tree(BasicTaxBureauQuery query) {
        return R.ok(basicTaxBureauService.tree(query));
    }

    @PostMapping("/enable")
    @ApiOperation("状态设置")
    public R<Void> enable(@RequestParam("id") Long id) {
        basicTaxBureauService.enable(id);
        return R.ok();
    }

    @PostMapping("/importTemp")
    @ApiOperation(value = "获取导入模板")
    public void download2Oss(HttpServletResponse response, HttpServletRequest request) {
        try {
            ClassPathResource classPathResource = new ClassPathResource("importTemp/税务局.xls");
            InputStream inputStream = classPathResource.getInputStream();
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, "税务局.xls");
            ServletOutputStream outputStream = response.getOutputStream();
            IOUtils.copy(inputStream, outputStream);
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    public R<Void> upload(@RequestPart("file") MultipartFile file) throws IOException {
        if(file == null || file.isEmpty()){
            return R.fail("文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        if (null != originalFilename) {
            if (!(originalFilename.contains(".xlsx") || originalFilename.contains(".xls"))) {
                throw new ServiceException("导入失败！文件类型错误");
            }
        }
        basicTaxBureauService.upload(file.getInputStream());
        return R.ok();
    }
}

