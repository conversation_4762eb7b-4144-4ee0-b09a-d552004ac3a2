package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerBank;
import com.jri.biz.domain.request.CustomerBankForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Mapper
public interface CustomerBankConvert {
    CustomerBankConvert INSTANCE = Mappers.getMapper(CustomerBankConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerBank convert(CustomerBankForm form);

}