<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jri.message.mapper.MessageDetailMapper">

    <resultMap type="com.jri.message.domain.vo.ExpireDetailVO" id="ExpireDetailVOResult">
        <id property="contractId" column="contract_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerNo" column="customer_no"/>
        <result property="contractName" column="contract_name"/>
        <result property="contractNo" column="contract_no"/>
        <result property="productName" column="product_name"/>
        <result property="contractType" column="contract_type"/>
        <result property="totalCost" column="total_cost"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="manger" column="manger"/>
        <result property="mangerUserId" column="manger_user_id"/>
        <result property="alertTime" column="alert_time"/>
    </resultMap>

    <sql id="selectMessageDetailVO">
        select id,
               biz_type,
               biz_order_id,
               sender,
               title,
               content,
               receive_time,
               read_status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               message_type,
               notification_id,
               extra_data
        from message_detail
    </sql>

    <update id="updateReadStatus">
        update message_detail set read_status=1 where  recipient = #{userId}
        <if test="list != null and list.size() > 0">
        and id in
        <foreach collection="list" item="list" open="(" separator="," close=")">
            #{list}
        </foreach>
        </if>
    </update>
    <update id="updateNotificationTitle">
        update message_detail set title=#{newTitle} where  notification_id=#{notificationId} and message_type="公告"
    </update>

    <select id="listPage" resultType="com.jri.message.domain.vo.MessageDetailVO">
        <include refid="selectMessageDetailVO"/>
        <where>
            <if test="query.readStatus!=null">
                and read_status = #{query.readStatus}
            </if>
            <if test="query.recipient!=null">
                and recipient = #{query.recipient}
            </if>
            <if test="query.messageType!=null and query.messageType!=''">
                and message_type = #{query.messageType}
            </if>
            <if test="query.content!=null and query.content!=''">
                and content like concat('%', #{query.content}, '%')
            </if>
        </where>
        order by   receive_time desc
    </select>
    <select id="selectMessageNum" resultType="java.lang.Integer">
        select count(*)
        from message_detail
        where read_status = #{readStatus}
          and recipient = #{userId}
    </select>

    <select id="selectDetailById" resultType="com.jri.message.domain.vo.MessageDetailVO">
        select detail.id,
               detail.biz_type,
               detail.biz_order_id,
               detail.sender,
               detail.title,
               detail.content,
               detail.receive_time,
               detail.read_status,
               detail.create_by,
               detail.create_time,
               detail.update_by,
               detail.update_time,
               detail.remark,
               detail.message_type,
               detail.notification_id
        from message_detail detail
        LEFT JOIN message_notification notification ON detail.notification_id=notification.id  and is_deleted=0


        where id = #{id}
    </select>
    <select id="getExpireDetail" resultMap="ExpireDetailVOResult">
select a.* from (
        SELECT
        information.customer_id,
        information.customer_name,
        information.customer_no,
        information.manger,
        information.manger_user_id,
        contract.contract_id,
        contract.contract_name,
        contract.contract_no,
        product.product_name,
        CONCAT(DATE_SUB(contract.end_time, INTERVAL job.alert_date DAY),' ',job.send_time) as alert_time,
        contract.contract_type,
        contract.total_cost,
        contract.start_time,
        contract.end_time
        FROM
        customer_contract contract
        LEFT JOIN customer_information information ON contract.ci_id = information.customer_id
        AND information.is_deleted =0
        LEFT JOIN contract_alert_job job on job.contract_type = contract.contract_type and job.is_deleted=0 and
        alert_status=1
        LEFT JOIN business_product product on product.id=contract.product_id and product.is_deleted=0
        where contract.is_deleted=0 and contract.contract_status=1
        <if test="query.contractName!=null and query.contractName!=''">
            and contract.contract_name LIKE concat('%',#{query.contractName},'%')
        </if>
        <if test="query.contractNo!=null and query.contractNo!=''">
            and contract.contract_no LIKE concat('%',#{query.contractNo},'%')
        </if>
        <if test="query.customerName!=null and query.customerName!=''">
            and information.customer_name LIKE concat('%',#{query.customerName},'%')
        </if>
        <if test="query.customerNo!=null and query.customerNo!=''">
            and information.customer_no LIKE concat('%',#{query.customerNo},'%')
        </if>
        order by alert_time desc) a
       where (SELECT DATE_FORMAT(a.alert_time,'%Y-%m-%d'))=CURRENT_DATE
    </select>
</mapper>
