package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-06-01
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="废弃查询对象")
public class CustomerDiscardRecordQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
