package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcRecord;
import com.jri.biz.cus.domain.request.CusCcRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 操作记录对象转换
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Mapper
public interface CusCcRecordConvert {
    CusCcRecordConvert INSTANCE = Mappers.getMapper(CusCcRecordConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCcRecord convert(CusCcRecordForm form);

}