package com.jri.biz.domain.entity.job;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("contract_alert")
@ApiModel(value = "ContractAlert对象", description = "")
public class ContractAlert implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("到期预警id")
    @TableId(value = "id", type = IdType.AUTO)
      private Long id;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
      private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
      private LocalDateTime updateTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;

    @ApiModelProperty("到期预警时间")
    private String alertTime;

    @ApiModelProperty(value = "财税顾问id")
    private Long mangerUserId;

    @ApiModelProperty(value = "开票员id")
    private Long counselorUserId;

    @ApiModelProperty(value = "客户成功id")
    private Long customerSuccessUserId;

    @ApiModelProperty(value = "主办会计id")
    private Long sponsorAccountingUserId;


}
