package com.jri.biz.domain.vo.kanban.payment;

import com.jri.biz.domain.common.ECharsPair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/4 14:41
 */
@Getter
@Setter
@ApiModel(value = "MonthlySalesBreakdown", description = "月营业额明细")
public class MonthlySalesBreakdownVO {

    @ApiModelProperty("已收总额")
    private BigDecimal total;

    @ApiModelProperty("已执行额")
    private BigDecimal executed;

    @ApiModelProperty("月营业额来源饼图")
    private List<ECharsPair<String, BigDecimal>> source;

    @ApiModelProperty("每月营业额折线图")
    private List<ECharsPair<String, MonthlySalesBreakdownBarGraph>> monthly;

}
