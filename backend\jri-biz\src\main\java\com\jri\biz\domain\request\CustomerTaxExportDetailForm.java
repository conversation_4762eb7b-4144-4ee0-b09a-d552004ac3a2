package com.jri.biz.domain.request;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Data
@NoArgsConstructor
@ApiModel(value = "出口退税明细表单请求对象")
public class CustomerTaxExportDetailForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 出口日期
     */
    @Excel(name = "出口日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 报关单号
     */
    @Excel(name = "报关单号")
    private String customsDeclarationNumber;

    /**
     * 品名
     */
    @Excel(name = "品名")
    private String tradeName;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private Integer number;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private String unitFirst;

    /**
     * 货源地
     */
    @Excel(name = "货源地")
    private String supplySource;

    /**
     * 进项发票号码
     */
    @Excel(name = "进项发票号码")
    private String receiptInvoiceNumber;

    /**
     * 进项品名
     */
    @Excel(name = "品名")
    private String receiptTradeName;

    /**
     * 进项数量
     */
    @Excel(name = "进项数量")
    private Integer receiptNumber;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位")
    private String unit;

    /**
     * 余数
     */
    @Excel(name = "余数")
    private Integer remainder;

    /**
     * 是否申报
     */
    @Excel(name = "是否申报")
    private String declareFlag;

    /**
     * 是否退税
     */
    @Excel(name = "是否退税")
    private String taxRebateFlag;

    /**
     * 报关单
     */
    @Excel(name = "报关单", readConverterExp = "true=√,false= ")
    private Boolean customsDeclarationFlag;

    /**
     * 提单
     */
    @Excel(name = "提单", readConverterExp = "true=√,false= ")
    private Boolean billLadingFlag;

    /**
     * 外销合同
     */
    @Excel(name = "外销合同", readConverterExp = "true=√,false= ")
    private Boolean exportContractFlag;

    /**
     * 进货合同
     */
    @Excel(name = "进货合同", readConverterExp = "true=√,false= ")
    private Boolean importContractFlag;

    /**
     * 通关无纸化证书
     */
    @Excel(name = "通关无纸化证书", readConverterExp = "true=√,false= ")
    private Boolean paperlessCertificateFlag;

    /**
     * 装箱单
     */
    @Excel(name = "装箱单", readConverterExp = "true=√,false= ")
    private Boolean packingListFlag;

    /**
     * 进仓单
     */
    @Excel(name = "进仓单", readConverterExp = "true=√,false= ")
    private Boolean deliveryNoteFlag;

    /**
     * 形式发票
     */
    @Excel(name = "形式发票", readConverterExp = "true=√,false= ")
    private Boolean proformaInvoiceFlag;

    /**
     * 货代发票
     */
    @Excel(name = "货代发票", readConverterExp = "true=√,false= ")
    private Boolean forwarderInvoiceFlag;

    /**
     * 收汇流水
     */
    @Excel(name = "收汇流水", readConverterExp = "true=√,false= ")
    private Boolean collectionStatementFlag;

    /**
     * 付款流水
     */
    @Excel(name = "付款流水", readConverterExp = "true=√,false= ")
    private Boolean paymentStatementFlag;

    /**
     * 货代发票流水
     */
    @Excel(name = "货代发票流水", readConverterExp = "true=√,false= ")
    private Boolean forwarderInvoiceStatementFlag;



    /**
     * 主表id
     */
    private Long mainId;

}
