package com.jri.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 *
 * 客户注销办理信息VO
 *
 * <AUTHOR>
 * @since 2024/3/22 11:12
 *
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "客户注销办理信息VO")
public class CancellationRecordVO {

    @ApiModelProperty("合同编号")
    private String contractNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("合同完成时间")
    private LocalDateTime contractTime;

    @ApiModelProperty("合同完成人")
    private String contractUserName;

    @ApiModelProperty("工单编号")
    private String orderNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("工单完成时间")
    private LocalDateTime orderTime;

    @ApiModelProperty("工单完成人")
    private String orderUserName;

    @ApiModelProperty("任务编号")
    private String taskNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("任务完成时间")
    private LocalDateTime taskTime;

    @ApiModelProperty("任务完成人")
    private String taskUserName;

}
