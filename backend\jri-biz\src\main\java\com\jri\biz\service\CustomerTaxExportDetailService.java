package com.jri.biz.service;

import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.jri.biz.domain.convert.CustomerTaxExportDetailConvert;
import com.jri.biz.domain.request.CustomerTaxExportDetailForm;
import com.jri.biz.domain.request.CustomerTaxExportDetailQuery;
import com.jri.biz.domain.vo.CustomerTaxExportDetailListVO;
import com.jri.biz.domain.vo.CustomerTaxExportDetailVO;
import com.jri.biz.mapper.CustomerTaxExportDetailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerTaxExportDetailService extends ServiceImpl<CustomerTaxExportDetailMapper, CustomerTaxExportDetail> {


    @Resource
    private CustomerTaxExportDetailMapper CustomerTaxExportDetailMapper;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerTaxExportDetailListVO> listPage(CustomerTaxExportDetailQuery query) {
        var page = new Page<CustomerTaxExportDetailListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CustomerTaxExportDetailVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CustomerTaxExportDetailForm form) {
        // todo 完善新增/更新逻辑
        CustomerTaxExportDetail customerTaxExportDetail = CustomerTaxExportDetailConvert.INSTANCE.convert(form);
        //更新
        if(form.getId()!=null){
            customerTaxExportDetail.setUpdateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }else {
            customerTaxExportDetail.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }
        return saveOrUpdate(customerTaxExportDetail);
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerTaxExportDetailForm form) {
        // todo 完善新增/更新逻辑
        CustomerTaxExportDetail customerTaxExportDetail = CustomerTaxExportDetailConvert.INSTANCE.convert(form);
        return updateById(customerTaxExportDetail);
    }

    /**
    * 根据id删除
    *
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long[] ids) {
        String updateBy = SecurityUtils.getLoginUser().getUser().getNickName();
        return  CustomerTaxExportDetailMapper.deleteByIds(ids,updateBy);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByMainId(Long mainId) {
        return  CustomerTaxExportDetailMapper.deleteByMainId(mainId);
    }
}
