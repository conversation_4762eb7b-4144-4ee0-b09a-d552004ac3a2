package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 股东信息查询类
 *
 * <AUTHOR>
 * @since 2023-11-08
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="股东信息查询对象")
public class CusCcShareholderInfoQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
