package com.jri.biz.cus.domain.request;

import com.jri.biz.cus.domain.entity.CusCcTags;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 更新标签
 * @Auther: wyt
 * @Date: 2023/8/10
 */
@Data
@NoArgsConstructor
@ApiModel(value="更新标签表单请求对象")
public class UpdateTagsForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索/客户id")
    private Long id;

    @ApiModelProperty("标签列表")
    private List<CusCcTags> tags;
}
