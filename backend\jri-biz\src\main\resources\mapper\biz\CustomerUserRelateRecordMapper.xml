<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerUserRelateRecordMapper">

    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerUserRelateRecordVO">
        select
        record.id,
        record.user_id as userId,
        user.nick_name as userName,
        record.pre_user_id as preUserId,
        record.role,
        record.operation_time as operationTime,
        record.operation_user as operationUser,
        operation_user.nick_name as operationUserName,
        record.end_time as endTime,
        record.end_user as endUser,
        end_user.nick_name as endUserName
        from customer_user_relate_record as record
        left join sys_user as user on record.user_id = user.user_id
        left join sys_user as operation_user on record.operation_user = operation_user.user_id
        left join sys_user as end_user on record.operation_user = end_user.user_id
        where customer_id = #{query.customerId}
        <if test="query.role != null and query.role != ''">
            and record.role = #{query.role}
        </if>
        order by record.operation_time desc
    </select>

    <select id="customerUserFlowAnalyse" resultType="com.jri.biz.domain.vo.CustomerUserFlowAnalyseVO">
        select
        user.user_id,
        user.nick_name as user_name,
        user.dept_id,
        dept.dept_name as dept_name
        from sys_user as user
        left join sys_dept as dept on user.dept_id = dept.dept_id
        <where>
            and user.del_flag = '0' and enterprise_flag = 0
            <if test="query.userName != null and query.userName != ''">
                and user.nick_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.deptId != null">
                and (user.dept_id = #{query.deptId} OR user.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE
                find_in_set(#{query.deptId}, ancestors) ))
            </if>
            <if test="query.userStatus != null and query.userStatus != ''">
                and user.status = #{query.userStatus}
            </if>
        </where>
    </select>
    <select id="monthStartCount" resultType="java.lang.Long">
        SELECT
            distinct record.customer_id
        FROM customer_user_relate_record record
        LEFT JOIN sys_user as user ON record.user_id = user.user_id
        LEFT JOIN sys_dept as dept ON user.dept_id = dept.dept_id
        INNER JOIN (
            SELECT MAX(id) AS max_id, customer_id
            FROM customer_user_relate_record
            <where>
                <if test="userId != null">
                    AND user_id = #{userId}
                </if>
                <if test="role != null and role != ''">
                    AND role = #{role}
                </if>
            </where>
            GROUP BY customer_id
        ) t ON record.id = t.max_id
        WHERE record.operation_time &lt;= #{endTime}
        AND user.del_flag = '0'
        <if test="userId != null">
            AND record.user_id = #{userId}
        </if>
        AND record.end_time is NULL
        <if test="role != null and role != ''">
            AND record.role = #{role}
        </if>
        <if test="deptId != null">
            and (FIND_IN_SET(#{deptId},dept.ancestors)>0 or
            #{deptId}=dept.dept_id)
        </if>
    </select>
    <select id="migrateInCount" resultType="java.lang.Long">
        SELECT
        DISTINCT record.customer_id
        FROM
        customer_user_relate_record record
        WHERE record.operation_time &gt;= #{startTime}
        AND record.operation_time &lt;= #{endTime}
        AND record.user_id = #{userId}
        AND record.pre_user_id is not NULL
        AND record.role = #{role}
        AND NOT EXISTS ( SELECT 1
            FROM customer_user_relate_record sub
            WHERE sub.customer_id = record.customer_id
              AND sub.user_id = #{userId}
              AND sub.operation_time &lt; record.operation_time
              AND sub.operation_time &gt;= #{startTime}
              AND sub.end_time IS NOT NULL
              AND sub.role = #{role})
    </select>
    <select id="migrateOutCount" resultType="java.lang.Long">
        SELECT
        DISTINCT record.customer_id
        FROM
        customer_user_relate_record record
        WHERE record.operation_time &gt;= #{startTime}
        AND record.operation_time &lt;= #{endTime}
        AND record.user_id = #{userId}
        AND record.end_time is not NULL
        AND record.role = #{role}
        AND NOT EXISTS ( SELECT 1
            FROM customer_user_relate_record sub
            WHERE sub.customer_id = record.customer_id
              AND sub.user_id = #{userId}
              AND sub.operation_time &gt; record.operation_time
              AND sub.operation_time &lt;= #{endTime}
              AND sub.end_time IS NULL
              AND sub.role = #{role})
    </select>
    <select id="newRelateCount" resultType="java.lang.Long">
        SELECT DISTINCT
            record.customer_id
        FROM
            customer_user_relate_record record
        WHERE record.operation_time &gt;= #{startTime}
          AND record.operation_time &lt;= #{endTime}
          AND record.user_id = #{userId}
          AND record.pre_user_id IS NULL
          AND record.role = #{role}
          AND NOT EXISTS ( SELECT 1
                FROM customer_user_relate_record sub
                WHERE sub.customer_id = record.customer_id
                AND sub.user_id = #{userId}
                AND sub.operation_time &lt; record.operation_time
                AND sub.end_time IS NULL
                AND sub.role = #{role})
    </select>
    <select id="customerUserFlowAnalyseList" resultType="com.jri.biz.domain.vo.CustomerUserFlowAnalyseVO">
        select
        user.user_id,
        user.nick_name as user_name,
        user.dept_id,
        dept.dept_name as dept_name
        from sys_user as user
        left join sys_dept as dept on user.dept_id = dept.dept_id
        <where>
            and user.del_flag = '0' and enterprise_flag = 0
            <if test="query.userName != null and query.userName != ''">
                and user.nick_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.deptId != null">
                and (user.dept_id = #{query.deptId} OR user.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE
                find_in_set(#{query.deptId}, ancestors) ))
            </if>
            <if test="query.userStatus != null and query.userStatus != ''">
                and user.status = #{query.userStatus}
            </if>
        </where>
    </select>
</mapper>