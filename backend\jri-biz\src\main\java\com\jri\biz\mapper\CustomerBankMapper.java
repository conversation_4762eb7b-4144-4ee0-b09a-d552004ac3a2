package com.jri.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.CustomerBank;
import com.jri.biz.domain.request.CustomerBankQuery;
import com.jri.biz.domain.vo.CustomerBankListVO;
import com.jri.biz.domain.vo.CustomerBankVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerBankMapper extends BaseMapper<CustomerBank> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page  分页参数
     * @return 查询结果
     */
    IPage<CustomerBankListVO> listPage(@Param("query") CustomerBankQuery query, Page<CustomerBankListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerBankVO getDetailById(@Param("id") Long id);

    Boolean deleteByIdAndUpdateBy(@Param("id") Long id, @Param("updateBy") String updateBy);

    CustomerBankVO getDetailByCiId(Long ciId);
}
