package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 行政区划查询类
 *
 * <AUTHOR>
 * @since 2023-07-31
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="行政区划查询对象")
public class BasicAdministrativeQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 使用状态0-停用 1-启用
     */
    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;
}
