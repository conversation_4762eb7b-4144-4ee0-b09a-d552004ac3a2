<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerLicenseMapper">
    <delete id="removeByCiId">
        delete from customer_license where ci_id = #{ciId}
    </delete>
    <delete id="deleteById">
        update customer_license set is_deleted=1,update_by=#{updateBy} where license_id=#{id}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerLicenseListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerLicenseVO">
        select * from customer_license where is_deleted=0 and license_id= #{id}
    </select>

    <select id="getDetailByCiId" resultType="com.jri.biz.domain.vo.CustomerLicenseVO">
        select * from customer_license where is_deleted=0 and ci_id=#{ciId}
    </select>

    <select id="countByLicenseName" resultType="java.lang.Long">
        select count(*)
        from customer_license as license
        left join customer_information as info on license.ci_id = info.customer_id
        where license.is_deleted = 0 and info.is_deleted = 0
          and license.license_name = #{licenseName}
    </select>
</mapper>
