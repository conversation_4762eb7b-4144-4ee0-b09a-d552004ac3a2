package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/6/6 10:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "biz_progress对象")
@TableName(value = "biz_progress")
public class Progress {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "状态0进行中，1完成 2失败")
    private Integer status;

    @ApiModelProperty(value = "数据")
    private String data;

    @ApiModelProperty(value = "失败原因")
    private String reason;

    @ApiModelProperty(value = "附件名称")
    private String fileName;

    @ApiModelProperty(value = "类型 upload-导入 download-导出")
    private String opType;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
