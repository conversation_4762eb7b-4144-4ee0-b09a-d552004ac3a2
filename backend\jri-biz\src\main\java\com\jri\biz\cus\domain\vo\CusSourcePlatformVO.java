package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 客资来源 平台 关联视图对象
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusSourcePlatformVO视图对象")
public class CusSourcePlatformVO extends CusSourceVO {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("单价")
    private BigDecimal price;
}