package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerDiscardRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerDiscardRecordListVO;
import com.jri.biz.domain.vo.CustomerDiscardRecordVO;
import com.jri.biz.domain.request.CustomerDiscardRecordQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
public interface CustomerDiscardRecordMapper extends BaseMapper<CustomerDiscardRecord> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerDiscardRecordListVO> listPage(@Param("query") CustomerDiscardRecordQuery query, Page<CustomerDiscardRecordListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerDiscardRecordVO getDetailById(@Param("id") Long id);

    List<CustomerDiscardRecordVO> getDetailByCiId(@Param("ciId")Long ciId);

    /**
     * 查询最新废弃记录
     *
     * @param id 客户信息id
     */
    LocalDateTime getRecentDiscardTime(@Param("id") Long id);
}
