<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerFollowMapper">

    <resultMap type="CustomerFollow" id="CustomerFollowResult">
        <result property="id" column="id"/>
        <result property="followUpRecord" column="follow_up_record"/>
        <result property="contactName" column="contact_name"/>
        <result property="mode" column="mode"/>
        <result property="date" column="date"/>
        <result property="followUpName" column="follow_up_name"/>
        <result property="dept" column="dept"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="ciId" column="ci_id"/>
    </resultMap>

    <sql id="selectCustomerFollowVo">
        select id,
               follow_up_record,
               contact_name,
               mode, date, follow_up_name, dept, create_by, create_time, update_by, update_time, is_deleted, ci_id
        from customer_follow
    </sql>

    <select id="selectCustomerFollowList" parameterType="CustomerFollow" resultMap="CustomerFollowResult">
        <include refid="selectCustomerFollowVo"/>
        <where>
            is_deleted = 0
            <if test="query.text != null  and query.text != ''">
                and (follow_up_record like concat('%', #{query.text}, '%') or contact_name like concat('%',
                #{query.text}, '%') or mode like concat('%', #{query.text}, '%') or follow_up_name like
                concat('%', #{query.text}, '%') or dept like concat('%', #{query.text}, '%'))
            </if>
            <if test="query.ciId != null ">and ci_id = #{query.ciId}</if>
        </where>
        order by date desc
    </select>

    <select id="selectCustomerFollowById" parameterType="Long" resultMap="CustomerFollowResult">
        <include refid="selectCustomerFollowVo"/>
        where id = #{id}
    </select>
    <select id="getNewByCiId" resultType="com.jri.biz.domain.entity.CustomerFollow">
        <include refid="selectCustomerFollowVo"/>
        where
            is_deleted = 0 and ci_id = #{ciId}
        order by date desc limit 1
    </select>

    <insert id="insertCustomerFollow" parameterType="CustomerFollow">
        insert into customer_follow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="followUpRecord != null">follow_up_record,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="mode != null">mode,</if>
            <if test="date != null">date,</if>
            <if test="followUpName != null">follow_up_name,</if>
            <if test="dept != null">dept,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="ciId != null">ci_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="followUpRecord != null">#{followUpRecord},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="mode != null">#{mode},</if>
            <if test="date != null">#{date},</if>
            <if test="followUpName != null">#{followUpName},</if>
            <if test="dept != null">#{dept},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="ciId != null">#{ciId},</if>
        </trim>
    </insert>

    <update id="updateCustomerFollow" parameterType="CustomerFollow">
        update customer_follow
        <trim prefix="SET" suffixOverrides=",">
            <if test="followUpRecord != null">follow_up_record = #{followUpRecord},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="mode != null">mode = #{mode},</if>
            <if test="date != null">date = #{date},</if>
            <if test="followUpName != null">follow_up_name = #{followUpName},</if>
            <if test="dept != null">dept = #{dept},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="ciId != null">ci_id = #{ciId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerFollowById" parameterType="Long">
        update customer_follow
        set is_deleted='1',
            update_by=#{updateBy}
        where id = #{id}
    </delete>

</mapper>
