package com.jri.biz.cus.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 股东信息视图对象
 *
 * <AUTHOR>
 * @since 2023-11-08
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCcShareholderInfoVO视图对象")
public class CusCcShareholderInfoVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}