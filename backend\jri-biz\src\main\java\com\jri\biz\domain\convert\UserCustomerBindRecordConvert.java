package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.UserCustomerBindRecord;
import com.jri.biz.domain.request.UserCustomerBindRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 用户客户绑定记录对象转换
 *
 * <AUTHOR>
 * @since 2024-01-24
 */

@Mapper
public interface UserCustomerBindRecordConvert {
    UserCustomerBindRecordConvert INSTANCE = Mappers.getMapper(UserCustomerBindRecordConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    UserCustomerBindRecord convert(UserCustomerBindRecordForm form);

}