package com.jri.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "合同评审列表对象")
public class ReviewCustomerContractListVO {

    @Excel(name = "合同编号")
    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("合同名称")
    private String contractName;

    @Excel(name = "合同类型", readConverterExp = "0=记账合同,1=一次性合同,2=地址服务协议合同")
    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @Excel(name = "审批结果", readConverterExp = "0=待审批,1=通过,2=驳回")
    @ApiModelProperty("审批结果0-待审批 1-通过 2-驳回")
    private String reviewStatus;

    @ApiModelProperty("起始时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date endTime;

    @Excel(name = "客户名称")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @Excel(name = "客户编号")
    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @Excel(name = "服务产品")
    @ApiModelProperty("产品名称")
    private String productName;

    @Excel(name = "合同总金额")
    @ApiModelProperty("合同总金额")
    private BigDecimal totalCost;

    @Excel(name = "业务类型", readConverterExp = "0=新增合同,1=变更合同")
    @ApiModelProperty("业务类型0-新增合同1-变更合同")
    private String bizType;

    @Excel(name = "提交人")
    @ApiModelProperty("创建者")
    private String createBy;

    @Excel(name = "提交时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("客户信息主表id")
    private Long ciId;

}
