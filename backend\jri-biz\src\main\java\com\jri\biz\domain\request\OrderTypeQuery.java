package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 工单类型查询类
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="工单类型查询对象")
public class OrderTypeQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String typeName;

    /**
     * 状态0-停用 1-正常
     */
    @ApiModelProperty("状态0-停用 1-正常")
    private String status;
}
