package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusCcRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusCcRecordListVO;
import com.jri.biz.cus.domain.vo.CusCcRecordVO;
import com.jri.biz.cus.domain.request.CusCcRecordQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 操作记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface CusCcRecordMapper extends BaseMapper<CusCcRecord> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusCcRecordListVO> listPage(@Param("query") CusCcRecordQuery query, Page<CusCcRecordListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCcRecordVO getDetailById(@Param("id") Long id);

    /**
     * 查询操作记录列表
     *
     * @param ccId 客户线索id
     */
    List<CusCcRecordListVO> listByCcId(@Param("ccId") Long ccId);

    /**
     * 查询操作记录列表(商机)
     *
     * @param ccId 客户线索id
     */
    List<CusCcRecordListVO> businessListByCcId(@Param("ccId") Long ccId);
}
