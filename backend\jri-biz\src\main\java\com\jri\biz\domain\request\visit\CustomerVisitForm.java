package com.jri.biz.domain.request.visit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 *  表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-28
 */

@Data
@NoArgsConstructor
@ApiModel(value = "拜访表单请求对象")
public class CustomerVisitForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("计划标题")
    private String planName;

    @ApiModelProperty("关联客户id")
    private Long customerId;

    @ApiModelProperty("关联客户id列表")
    private List<Long> customerIdList;

    @ApiModelProperty("计划拜访日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDate;

    @ApiModelProperty("实际拜访日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date actualPlanDate;

    @ApiModelProperty("拜访人id")
    private Long visitorId;

    @ApiModelProperty("拜访方式")
    private String planVisitMethod;

    @ApiModelProperty("实际拜访方式")
    private String actualVisitMethod;

    @ApiModelProperty("完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date completeTime;

    @ApiModelProperty("状态 0 待完成 1已完成 2已取消")
    private String status;

    @ApiModelProperty("拜访目的")
    private String visitPurpose;

    @ApiModelProperty("拜访反馈")
    private String visitFeedback;

    @ApiModelProperty("取消原因")
    private String cancelReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("位置名称")
    private String locationName;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;

    @ApiModelProperty("附件")
    private List<CommonBizFile> fileList;

}
