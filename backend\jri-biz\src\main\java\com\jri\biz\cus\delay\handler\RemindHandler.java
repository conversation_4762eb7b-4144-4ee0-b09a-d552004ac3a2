package com.jri.biz.cus.delay.handler;

import cn.hutool.core.util.ObjectUtil;
import com.jri.biz.constants.BizType;
import com.jri.biz.cus.domain.entity.CusCcContact;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.entity.CusSea;
import com.jri.biz.cus.domain.vo.CusCustomerOrClueVO;
import com.jri.biz.cus.domain.vo.CusSeaInventoryVO;
import com.jri.biz.cus.mapper.CusCcContactMapper;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.biz.cus.service.CusCcRecordService;
import com.jri.biz.cus.service.CusCustomerOrClueService;
import com.jri.biz.cus.service.CusSeaInventoryService;
import com.jri.biz.cus.service.CusSeaService;
import com.jri.message.constants.MessageConstant;
import com.jri.message.domain.request.MessageDetailForm;
import com.jri.message.domain.vo.ExtraDataVO;
import com.jri.message.service.MessageDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 回收公海提醒延迟队列处理
 * @Auther: wyt
 * @Date: 2023/8/24
 */
@Component
@Slf4j
public class RemindHandler implements RedisDelayQueueHandler<Long> {

    @Resource
    private CusCustomerOrClueService cusCustomerOrClueService;

    @Resource
    private MessageDetailService messageDetailService;
    /**
     * 回收公海
     *
     * @param id 线索客户id
     */
    @Override
    public void execute(Long id) {
        System.out.println(id + "************回收公海提醒延迟处理开始****************");
        CusCustomerOrClueVO res = cusCustomerOrClueService.getDetailById(id);

        List<Long> sendUserIdListOfChange = new ArrayList<>();
        sendUserIdListOfChange.add(res.getCurrentUserId());
        MessageDetailForm messageDetailForm2 = new MessageDetailForm();
        Map<String, String> contentParams2 = new HashMap<>();
        if ("0".equals(res.getType())) {
            messageDetailForm2.setCode(BizType.CLUE_RECOVERY_REMIND);
            contentParams2.put("name", res.getContactName());
            messageDetailForm2.setBizType("clue");
        } else {
            messageDetailForm2.setCode(BizType.CUS_RECOVERY_REMIND);
            contentParams2.put("name", res.getCompanyName());
            messageDetailForm2.setBizType("cus");
        }
        messageDetailForm2.setTitleParam(null);
        messageDetailForm2.setContentParam(contentParams2);
        messageDetailForm2.setMessageType(MessageConstant.ALERT);
        messageDetailForm2.setNickName(MessageConstant.SYSTEM);
        ExtraDataVO extraDataVO = new ExtraDataVO(null, ""+res.getId());
        messageDetailForm2.setExtraDataVO(extraDataVO);
        messageDetailForm2.setRecipients(sendUserIdListOfChange);

        try {
            messageDetailService.send(messageDetailForm2);
        } catch (Exception e) {
            log.error("消息发送失败:{}", e.getMessage());
        }
        System.out.println(id + "************回收公海提醒延迟处理结束****************");
    }
}
