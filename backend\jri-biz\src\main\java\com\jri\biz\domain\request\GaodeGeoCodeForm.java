package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "高德 逆地理编码 请求实体")
public class GaodeGeoCodeForm {

    @ApiModelProperty(value = "经度", required = true)
    private Double lon;

    @ApiModelProperty(value = "纬度", required = true)
    private Double lat;
}
