<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-05-29 09:47:30
 * @LastEditTime: 2023-06-16 14:06:01
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center :close-on-click-modal="false" title="获取工商信息" v-model="visible" @close="handleClose">
    <div class="fl-center">
      <el-icon size="20" style="margin-right: 20px"><Warning color="#e6a23c" /></el-icon>
      当前企业名称无匹配信息，请输入统一社会信用代码后再次尝试！
    </div>
    <el-form :model="formData" ref="formRef" :rules="rules" label-position="top">
      <el-form-item label="企业名称">
        <el-input v-model="formData.customerName" disabled placeholder="请输入" />
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="crediCode">
        <el-input v-model="formData.crediCode" placeholder="请输入"> </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="handleSubmit(formRef)">查询</el-button>
      <el-button @click="handleClose"> 关闭 </el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { getCommercialDetail } from '@/api/customer/file'
import { Warning } from '@element-plus/icons-vue'

const visible = ref(true)
const emits = defineEmits(['on-close', 'search-success'])
const handleClose = () => {
  emits('on-close')
}
const formData = ref({
  customerName: '',
  crediCode: ''
})
const props = defineProps({
  data: Object
})
watch(
  props.data,
  () => {
    formData.value = {
      customerName: props.data.customerName || '',
      crediCode: props.data.crediCode || ''
    }
  },
  {
    immediate: true
  }
)
const rules = {
  crediCode: [{ required: true, message: '请输入', trigger: 'blur' }]
}
const getCommercial = async () => {
  const { crediCode } = formData.value
  const { data } = await getCommercialDetail({
    keyword: crediCode
  })
  handleClose()
  emits('search-success', data)
}

// 提交表单
const formRef = ref()
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      getCommercial()
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>
<style lang="scss" scoped>
.fl-center {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
</style>
