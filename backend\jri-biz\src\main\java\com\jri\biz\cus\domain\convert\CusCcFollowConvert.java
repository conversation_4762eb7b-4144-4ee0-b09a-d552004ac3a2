package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcFollow;
import com.jri.biz.cus.domain.request.CusCcFollowForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 跟进记录对象转换
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Mapper
public interface CusCcFollowConvert {
    CusCcFollowConvert INSTANCE = Mappers.getMapper(CusCcFollowConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCcFollow convert(CusCcFollowForm form);

}