package com.jri.biz.domain.vo.address;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 房屋产权证明视图列表对象
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "AddressPropertyOwnershipListVO视图列表对象")
public class AddressPropertyOwnershipListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("房本名称")
    private String name;

    @ApiModelProperty("出租人id")
    private Long lessorId;

    @ApiModelProperty("出租人姓名")
    private String lessorName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("租赁开始时间")
    private LocalDate leaseStartDate;

    @ApiModelProperty("租赁结束时间")
    private LocalDate leaseEndDate;

    @ApiModelProperty("租赁价格")
    private BigDecimal leasePrice;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("领用状态")
    private String useStatus;

    @ApiModelProperty("使用状态 闲置-idle 已到期-expired 使用中-in_use")
    private String enable;

}