package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerTaxExportDetailListVO;
import com.jri.biz.domain.vo.CustomerTaxExportDetailVO;
import com.jri.biz.domain.request.CustomerTaxExportDetailQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerTaxExportDetailMapper extends BaseMapper<CustomerTaxExportDetail> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerTaxExportDetailListVO> listPage(@Param("query") CustomerTaxExportDetailQuery query, Page<CustomerTaxExportDetailListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerTaxExportDetailVO getDetailById(@Param("id") Long id);

    List<CustomerTaxExportDetail> selectByMainId(Long id);

    Boolean deleteByIds(@Param("id")Long[] id,@Param("updateBy")String updateBy);

    Boolean deleteByMainId(@Param("mainId")Long mainId);

}
