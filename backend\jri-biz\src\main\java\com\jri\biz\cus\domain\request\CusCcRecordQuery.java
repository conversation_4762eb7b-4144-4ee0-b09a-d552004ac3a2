package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 操作记录查询类
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="操作记录查询对象")
public class CusCcRecordQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
