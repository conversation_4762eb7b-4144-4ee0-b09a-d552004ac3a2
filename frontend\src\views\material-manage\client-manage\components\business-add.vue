<!--
 * @Description: 新增商机
 * @Author: thb
 * @Date: 2023-05-30 09:56:01
 * @LastEditTime: 2023-11-16 14:12:40
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    :title="type === 'add' ? '新增商机' : '编辑商机'"
    width="800"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="商机名称" prop="name">
            <el-input v-model="formData.name" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联客户名称" prop="companyName">
            <el-input v-model="formData.companyName" maxlength="20" readonly placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="预计成交金额" prop="expectAmount">
            <NumberInput v-model="formData.expectAmount">
              <template #suffix> 元 </template>
            </NumberInput>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="预计成交时间" prop="expectTime">
            <el-date-picker
              v-model="formData.expectTime"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              type="date"
              placeholder="请选择日期"
            /> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="销售阶段" prop="stage">
            <el-select v-model="formData.stage" clearable placeholder="请选择" @change="handleChange">
              <el-option v-for="(item, index) in stage" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阶段百分比" prop="stagePercentage">
            <el-input v-model="stagePercentageMap[formData.stage]" maxlength="20" readonly placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-input v-model="statusMap[formData.stage]" maxlength="20" readonly placeholder="请输入" />
          </el-form-item>
        </el-col>
        <!-- 赢单阶段下 -->
        <template v-if="(formData.stage === '赢单' || formData.stage === '已收定金,待打尾款') && show">
          <el-col :span="24">
            <div style="float: right; margin-bottom: 5px">
              <el-button plain type="primary" size="small" @click="handleAdd">新增</el-button>
            </div>
            <FormTable
              ref="formTableRef"
              :formData="{
                tableData: formData.list,
                rules: rules_for_list
              }"
              :option="option"
            >
              <template #productId="{ row }">
                <el-tree-select
                  placeholder="请选择"
                  v-model="row.productId"
                  filterable
                  :data="businessList"
                  @node-click="(node, nodeData) => nodeClick(node, nodeData, row)"
                  @current-change="(node, nodeData) => nodeChange(node, nodeData, row)"
                  @clear="isDisabled = false"
                  clearable
                  :props="defaultPopsFunction(row)"
                  :render-after-expand="false"
                />
              </template>
              <template #actualAmount="{ row }">
                <NumberInput @blur="handleBlurInput" v-model="row.actualAmount" placeholder="请输入">
                  <template #suffix> 元 </template>
                </NumberInput>
              </template>
              <template #action="{ row, $index }">
                <el-button type="danger" text @click="handleDelete(row, $index)" :disabled="formData.list.length === 1"
                  >删除</el-button
                >
              </template>
            </FormTable>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="实际成交金额" prop="actualAmount">
              <NumberInput v-model="formData.actualAmount">
                <template #suffix> 元 </template>
              </NumberInput>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="index === 0 ? '签约业务' : ''"
              v-for="(item, index) in formData.list"
              :key="item"
              :prop="'list.' + index + '.productId'"
              :rules="{
                required: true,
                message: '请选择',
                trigger: 'change'
              }"
            >
              <div class="list-item">
                <el-tree-select
                  v-model="item.productId"
                  filterable
                  :data="businessList"
                  @node-click="(node, nodeData) => nodeClick(node, nodeData, item)"
                  @current-change="(node, nodeData) => nodeChange(node, nodeData, item)"
                  @clear="isDisabled = false"
                  clearable
                  :props="defaultPopsFunction(item)"
                  :render-after-expand="false"
                />
                <el-icon :size="24" color="#409EFF" @click="handleAddBusinessSelect(index)">
                  <CirclePlus />
                </el-icon>

                <el-icon :size="24" color="red" v-if="index !== 0" @click="handleDeleteBusinessSelect(index, item)"
                  ><Remove
                /></el-icon>
              </div>
            </el-form-item>
          </el-col> -->
        </template>
        <!-- 输单阶段下 -->
        <template v-if="formData.stage === '输单'">
          <el-col :span="12">
            <el-form-item label="输单原因" prop="reason">
              <el-select v-model="formData.reason" clearable placeholder="请选择">
                <el-option v-for="(item, index) in reason_list" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="输单描述" prop="remark">
              <el-input
                v-model="formData.remark"
                maxlength="1000"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              ></el-input>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="handleSubmit(formRef)"
        v-if="formData.stage !== '赢单' || formData.stage === '已收定金,待打尾款'"
        >确定</el-button
      >
      <el-button type="primary" @click="handleSubmit(formRef)" v-else>保存并建档</el-button>
    </template>
  </el-dialog>

  <fileAdd v-if="fileShow" :data="detail" :businessData="formData" @on-close="fileShow = false" @on-success="handleSuccess" />
</template>
<script setup>
import { saveOrUpdateClientBusiness } from '@/api/material-manage/client'
import NumberInput from '@/components/NumberInput'
import { getBusinessList } from '@/api/business/business'
import fileAdd from './file-add'
import { ElMessageBox, ElMessage } from 'element-plus'
import FormTable from '@/components/FormTable'
import { nextTick } from 'vue'

const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const { proxy } = getCurrentInstance()
const { reason_list } = proxy.useDict('reason_list')
// 销售阶段
const stage = [
  {
    label: '需求确认,有明确意向',
    value: '需求确认,有明确意向'
  },
  {
    label: '已报价,待确认',
    value: '已报价,待确认'
  },
  {
    label: '异议处理',
    value: '异议处理'
  },
  {
    label: '待打款',
    value: '待打款'
  },
  {
    label: '已收定金,待打尾款',
    value: '已收定金,待打尾款'
  },
  {
    label: '赢单',
    value: '赢单'
  },
  {
    label: '输单',
    value: '输单'
  }
]
const stagePercentageMap = {
  '需求确认,有明确意向': '20%',
  '已报价,待确认': '40%',
  异议处理: '70%',
  待打款: '90%',
  '已收定金,待打尾款': '95%',
  赢单: '100%',
  输单: '0%'
}

const statusMap = {
  '需求确认,有明确意向': '初始状态',
  '已报价,待确认': '进行中',
  异议处理: '进行中',
  待打款: '进行中',
  '已收定金,待打尾款': '进行中',
  赢单: '结束',
  输单: '结束'
}
const props = defineProps({
  detail: {
    type: Object,
    default: () => {
      return {}
    }
  },
  data: {
    type: Object,
    default: () => {
      return {}
    }
  },
  type: {
    type: String,
    default: 'add' //
  }
})

// const { expectAmount, expectTime, loseTime, name, stage, stagePercentage, status } = props.detail
const formData = ref({
  expectAmount: props.data.expectAmount || '', //
  expectTime: props.data.expectTime || '', //  预计成交时间
  loseTime: props.data.loseTime || '', // 输单时间
  reason: '', // 输单原因
  remark: '', //输单描述
  name: props.data.name || '', // 商机名称
  stage: props.data.stage || '', // 销售阶段
  stagePercentage: props.data.stagePercentage || '', // 阶段百分比
  status: props.data.status || '', // 状态
  winTime: '', // 赢单时间
  companyName: props.data.companyName || props.detail.companyName,
  list: [], // 业务list
  id: props.data.id, // 商机id
  ccId: props.data.ccId || '' // 关联客户id
})
const rules = {
  name: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  stage: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  actualAmount: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  reason: [
    {
      required: true,
      message: '请输入',
      trigger: 'change'
    }
  ]
}

// 获取签约业务列表
const businessList = ref([])
// 工商注册(包括 内资注册 、工资注册-单办证、外资注册、外资注册-单办证)
const specialProList = ['内资注册', '内资注册 - 单办证', '外资注册', '外资注册 - 单办证']
const defaultPopsFunction = row => {
  return {
    value: 'id',
    label: 'name',

    disabled: data => {
      return (
        (data?.type === '业务类型' && !data?.children.length) ||
        (data?.type === '产品类型' &&
          row.productId !== data.id &&
          formData.value.list.map(item => item.productId).includes(data.id)) ||
        (data?.type === '产品类型' &&
          row.productId !== data.id &&
          (specialProList.includes(row.name) ? false : isDisabled.value && specialProList.includes(data.name)))
      )
    }
  }
}
const getList = async () => {
  const { data } = await getBusinessList({
    pageSize: 1000,
    pageNum: 1
  })
  // businessList.value = data || []
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          type: '产品类型',
          id: child.id // 产品类型id
        })
      })
    }
  })
  businessList.value = revertData || []
}
getList()
// 业务节点选择事件

const isDisabled = ref(false)
const nodeClick = (node, nodeData, row) => {
  // console.log('nodeClick', node, row)
  // console.log(node.type === '产品类型', specialProList.includes(node.name), !isDisabled.value)
  if (node.type === '产品类型' && specialProList.includes(node.name) && !isDisabled.value) {
    row.productId = node.id
    row.name = node.name
    isDisabled.value = true
  }
}

// node-change事件
const nodeChange = (node, nodeData, row) => {
  // console.log('nodeChange', node, row)
  // console.log(node.type === '产品类型', specialProList.includes(node.name), !specialProList.includes(node.name))
  if (node.type === '产品类型' && specialProList.includes(row.name) && !specialProList.includes(node.name)) {
    isDisabled.value = false
    row.name = node.name
  }
}

const show = ref(true)
const handleChange = value => {
  formData.value.stagePercentage = stagePercentageMap[value]
  // formData.value.status = ''
  formData.value.status = statusMap[value]
  if (value === '赢单' || value === '已收定金,待打尾款') {
    formData.value.list = []
    // 则需要将list 默认添加一个
    formData.value.list.push({
      productId: '',
      actualAmount: ''
    })
  } else {
    formData.value.list = []
    formData.value.reason = ''
    formData.value.remark = ''
  }
  // console.log('formData.value.list', JSON.parse(JSON.stringify(formData.value.list?.[0])))
  isDisabled.value = false
  // http://10.100.1.235:8082/bug-view-14071.html?tid=xe4uf044 功能：选择赢单，再修改成已收定金,待打尾款，签约业务无法选中
  // 具体原因未知，用以下方式
  show.value = false
  nextTick(() => {
    show.value = true
  })
}

// 新增商机业务选择项
const handleAddBusinessSelect = index => {
  formData.value.list.splice(index + 1, 0, {
    productId: ''
  })
}

// 删除商机业务选择项
const handleDeleteBusinessSelect = (index, item) => {
  if (specialProList.includes(item.name)) {
    isDisabled.value = false
  }
  formData.value.list.splice(index, 1)
}
const formRef = ref()

const fileShow = ref(false)
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      if (formTableRef.value && !(await formTableRef.value.handleValidate())) return
      const result = await saveOrUpdateClientBusiness({
        ...formData.value,
        // stagePercentage: stagePercentageMap.value[formData.value.stage],
        // status: statusMap[formData.value.stage],
        ccId: props.data.ccId || props.detail.id
      })
      if (result.code === 200) {
        // 非赢单情况下
        if (result.msg === '操作成功') {
          proxy.$modal.msgSuccess(`保存成功!`)
          handleClose()
          emits('on-success')
        }
        // 赢单情况下
        //未建档需要建档

        if (result.msg === '客户未建档，请先建档！') {
          // 弹出建档弹窗
          fileShow.value = true
        }
        // 已经建档的
        if (result.msg === '该客户已办理过工商新注册业务，请确认签约业务后再次尝试！') {
          ElMessageBox.confirm(result.msg, '温馨提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {})
        }

        // 已经建档的
        if (result.msg === '是否需要新增合同？') {
          ElMessageBox.confirm(result.msg, '温馨提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              // 选择确定 需要跳转到新增合同
              handleClose()
              emits(
                'on-success',
                formData.value.list.map(item => item.productId), // 选择的业务ids
                result.data
                // 档案id,用于获取档案详情与合同关联
              )
            })
            .catch(() => {
              handleClose()
              emits('on-success')
            })
        }
      }
    } else {
    }
  })
}

const handleSuccess = customerId => {
  console.log('handleSuccess---customerId', customerId)
  handleClose()
  emits(
    'on-success',
    formData.value.list.map(item => item.productId),
    customerId // 企业档案id
  )
}

const formTableRef = ref(null)
const option = [
  {
    prop: 'productId',
    label: '签约业务'
  },
  {
    prop: 'actualAmount',
    label: '实际成交金额'
  },
  {
    prop: 'action',
    label: '操作',
    width: '160px'
  }
]

// 新增行
const handleAdd = () => {
  if (formData.value?.list?.length) {
    formData.value.list.push({
      productId: '',
      actualAmount: ''
    })
  } else {
    formData.value.list = [
      {
        productId: '',
        actualAmount: ''
      }
    ]
  }
}
// 删除行
const handleDelete = (row, index) => {
  formData.value.list.splice(index, 1)
}

const handleBlurInput = () => {
  console.log('handleBlurInput')
  formTableRef.value.handleValidate()
}
const rules_for_list = {
  productId: [{ required: true, message: '请选择', trigger: 'change' }],
  actualAmount: [{ required: true, message: '请输入', trigger: 'change' }]
}
</script>
<style lang="scss" scoped>
.el-select {
  flex: 1;
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
.list-item {
  display: flex;
  align-items: center;
  width: 100%;
}

:deep(.el-form-item--default) {
  .el-form-item__content {
    width: 100%;
  }
}

.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
