package com.jri.biz.cus.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.convert.CusSeaInventoryConvert;
import com.jri.biz.cus.domain.entity.CusSeaInventory;
import com.jri.biz.cus.domain.request.CusSeaInventoryForm;
import com.jri.biz.cus.domain.vo.CusSeaInventoryVO;
import com.jri.biz.cus.mapper.CusSeaInventoryMapper;
import com.jri.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 保有量设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Service
public class CusSeaInventoryService extends ServiceImpl<CusSeaInventoryMapper, CusSeaInventory> {

    /**
    * 根据id获取详情
    *
    * @return 结果
    */
    public CusSeaInventoryVO getDetailById() {
        return getBaseMapper().getDetailById();
    }

    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusSeaInventoryForm form) {
        CusSeaInventory cusSeaInventory = CusSeaInventoryConvert.INSTANCE.convert(form);

        Integer clueDuration = cusSeaInventory.getClueDuration();
        Integer clueRecovery = cusSeaInventory.getClueRecovery();
        if (ObjectUtil.isEmpty(clueDuration)) {
            if (ObjectUtil.isNotEmpty(clueRecovery)) {
                throw new ServiceException("未填写线索资源回收规则,不能填写线索回收提醒");
            }
        } else {
            if (ObjectUtil.isEmpty(cusSeaInventory.getClueSeaId())) {
                throw new ServiceException("填写线索资源回收规则,必须填写线索回收公海");
            }
            if (ObjectUtil.isNotEmpty(clueRecovery)) {
                if (clueDuration <= clueRecovery) {
                    throw new ServiceException("线索资源回收规则必须大于线索回收提醒");
                }
            }
        }

        Integer cusDuration = cusSeaInventory.getCusDuration();
        Integer cusRecovery = cusSeaInventory.getCusRecovery();
        if (ObjectUtil.isEmpty(cusDuration)) {
            if (ObjectUtil.isNotEmpty(cusRecovery)) {
                throw new ServiceException("未填写客户资源回收规则,不能填写客户回收提醒");
            }
        } else {
            if (ObjectUtil.isEmpty(cusSeaInventory.getCusSeaId())) {
                throw new ServiceException("填写客户资源回收规则,必须填写客户回收公海");
            }
            if (ObjectUtil.isNotEmpty(cusRecovery)) {
                if (cusDuration <= cusRecovery) {
                    throw new ServiceException("客户资源回收规则必须大于客户回收提醒");
                }
            }
        }
        return saveOrUpdate(cusSeaInventory);
    }
}
