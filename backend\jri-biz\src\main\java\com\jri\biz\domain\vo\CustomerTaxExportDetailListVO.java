package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 视图列表对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CustomerTaxExportDetailListVO视图列表对象")
public class CustomerTaxExportDetailListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}