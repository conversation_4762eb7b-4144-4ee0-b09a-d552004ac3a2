package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 线索/客户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_customer_or_clue")
@ApiModel(value = "CusCustomerOrClue对象", description = "线索客户信息")
public class CusCustomerOrClue implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 线索来源(客户来源)
     */
    private String source;

    /**
     * 来源id
     */
    private Long sourceId;

    /**
     * 客户介绍id
     */
    private Long introductionCustomerId;

    /**
     * 公司名称(客户名称)
     */
    private String companyName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 行业
     */
    private String industry;

    /**
     * 地区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 税务性质
     */
    private String taxNature;

    /**
     * 首次跟进时间
     */
    private LocalDate firstFollowTime;

    /**
     * 客户等级
     */
    private String level;

    /**
     * 最近跟进时间
     */
    private LocalDateTime lastFollowTime;

    /**
     * 最近跟进人
     */
    private String lastFollowUser;

    /**
     * 领取/分配时间
     */
    private LocalDateTime getTime;

    /**
     * 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
     */
    private String followStatus;

    /**
     * 公海id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long seaId;

    /**
     * 是否正在公海中0-否1-是
     */
    private String isSea;

    /**
     * 主联系人id
     */
    private Long mainContactId;

    /**
     * 当前处理人
     */
    private Long currentUserId;

    /**
     * 是否已转为企业档案0-否1-是
     */
    private String isChange;

    /**
     * 类型0-线索1-客户2-线索关联已有客户
     */
    private String type;

    /**
     * 回收原因
     */
    private String reason;

    /**
     * 成为客户时间
     */
    private LocalDateTime becomeTime;

    /**
     * 录入方式0-手动录入1-公海录入
     */
    private String entryType;

    /**
     * 电话
     */
    private String phone;

    /**
     * 法人身份证正面
     */
    private String legalIdCardFront;

    /**
     * 法人身份证反面
     */
    private String legalIdCardBack;

    /**
     * 法人联系方式
     */
    private String legalPhone;

    /**
     * 监事身份证正面
     */
    private String supervisorIdCardFront;

    /**
     * 监事身份证反面
     */
    private String supervisorIdCardBack;

    /**
     * 监事联系方式
     */
    private String supervisorPhone;

    /**
     * 股份比例
     */
    private String share;

    /**
     * 其它附件
     */
    private String otherUrls;

    /**
     * 企业档案id
     */
    private Long customerId;

    /**
     * 回收时长(重置掉保时长时更新)
     */
    private Integer duration;

    /**
     * 最近修改时间
     */
    private LocalDateTime lastModifiedTime;

    /**
     * 保护开始时间
     */
    private LocalDateTime protectionStartTime;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 申诉标志
     */
    private Boolean appealFlag;
}
