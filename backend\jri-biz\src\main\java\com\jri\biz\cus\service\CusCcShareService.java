package com.jri.biz.cus.service;

import com.jri.biz.cus.domain.entity.CusCcShare;
import com.jri.biz.cus.mapper.CusCcShareMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.vo.CusCcShareListVO;
import com.jri.biz.cus.domain.vo.CusCcShareVO;
import com.jri.biz.cus.domain.request.CusCcShareForm;
import com.jri.biz.cus.domain.request.CusCcShareQuery;
import com.jri.biz.cus.domain.convert.CusCcShareConvert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 线索/客户共享 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class CusCcShareService extends ServiceImpl<CusCcShareMapper, CusCcShare> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCcShareListVO> listPage(CusCcShareQuery query) {
        var page = new Page<CusCcShareListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CusCcShareVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusCcShareForm form) {
        // todo 完善新增/更新逻辑
        CusCcShare cusCcShare = CusCcShareConvert.INSTANCE.convert(form);
        return save(cusCcShare);
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CusCcShareForm form) {
        // todo 完善新增/更新逻辑
        CusCcShare cusCcShare = CusCcShareConvert.INSTANCE.convert(form);
        return updateById(cusCcShare);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }
}
