package com.jri.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 视图列表对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CustomerContractListVO视图列表对象")
public class CustomerContractListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("客户信息主表id")
    private Long ciId;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同状态0-待审批 1-通过 2-驳回")
    private String contractStatus;

    @ApiModelProperty("0-录入合同1-模板创建合同")
    private String type;

    @ApiModelProperty("起始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("起始时间")
    private Date startTimeOrg;

    @ApiModelProperty("结束时间")
    private Date endTimeOrg;

    @ApiModelProperty("合同总金额")
    private BigDecimal totalCost;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime terminationTime;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("业务类型0-新增合同1-变更合同")
    private String bizType;


    //=== 客户信息表 ===
    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    @ApiModelProperty(value = "客户名称")
    private String customerName;


    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("合同类型名称")
    private String contractTypeName;

    @ApiModelProperty("客户经理")
    private String manager;

    @ApiModelProperty("归档号")
    private String documentNo;

    @ApiModelProperty(value = "开票员")
    private String counselor;

    @ApiModelProperty(value = "客户成功")
    private String customerSuccess;

    @ApiModelProperty(value = "主办会计")
    private String sponsorAccounting;

    public String getContractTypeName() {
        String contractType = getContractType();
        if ("0".equals(contractType)) {
            return "记账合同";
        } else if ("1".equals(contractType)) {
            return "一次性合同";
        } else if ("2".equals(contractType)) {
            return "地址服务协议合同";
        } else {
            return null;
        }
    }
}
