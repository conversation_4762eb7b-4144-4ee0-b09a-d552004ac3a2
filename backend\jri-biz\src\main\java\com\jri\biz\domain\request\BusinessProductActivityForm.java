package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 产品活动价 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-11-02
 */

@Data
@NoArgsConstructor
@ApiModel(value="产品活动价表单请求对象")
public class BusinessProductActivityForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

}
