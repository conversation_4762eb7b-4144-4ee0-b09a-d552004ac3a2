package com.jri.biz.domain.vo;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CustomerBusinessInformationVO视图对象")
public class CustomerBusinessInformationVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value =  " 工商信息id")
    private Long businessInformationId;

    @ApiModelProperty(value =  " 企业名称")
    private String customerName;

    @ApiModelProperty(value =  " 客户信息主表")
    private Long ciId;

    @ApiModelProperty(value =  " 公司类型")
    private String type;

    @ApiModelProperty(value =  " 统一社会信用代码")
    private String crediCode;

    @ApiModelProperty(value =  " 法定代表人")
    private String legalPerson;

    @ApiModelProperty(value =  " 注册地址")
    private String registeredAddress;

    @ApiModelProperty(value =  " 联系方式")
    private String contract;

    @ApiModelProperty(value =  " 经营范围")
    private String scope;

    @ApiModelProperty(value =  " 网址")
    private String website;

    @ApiModelProperty(value =  " 经营状态")
    private String bussinessStatus;

    @ApiModelProperty(value =  " 登记机关")
    private String registrationAuthority;

    @ApiModelProperty(value =  " 成立日期")
    private String establishDate;

    @ApiModelProperty(value =  " 注册资金")
    private String registeredCapital;

    @ApiModelProperty(value =  " 行业")
    private String industry;

    @ApiModelProperty(value =  " 注册号")
    private String registrationNumber;

    @ApiModelProperty(value =  " 营业开始时间")
    private String openDate;

    @ApiModelProperty(value =  " 营业结束时间")
    private String openEnd;

    @ApiModelProperty(value =  " 组织机关代码")
    private String organizationCode;

    @ApiModelProperty(value =  " 核准日期")
    private String approvalDate;

//    @ApiModelProperty(value =  " 国税账号")
//    private String nationalTaxAccount;
//
//    @ApiModelProperty(value =  " 国税密码")
//    private String nationalTaxPassward;
//
//    @ApiModelProperty(value =  " 个税账号")
//    private String individualTaxAccount;
//
//    @ApiModelProperty(value =  " 个税密码")
//    private String individualTaxPassword;

    @ApiModelProperty(value =  " 个税密码")
    private CommonBizFile individualTaxPasswordFile;

    @ApiModelProperty("营业执照")
    private List<CommonBizFile> businessFileList;

    @ApiModelProperty("注册资料")
    private List<CommonBizFile> registrationInformationFileList;

    @ApiModelProperty("公司章程")
    private List<CommonBizFile> businessConstitutionFileList;

    @ApiModelProperty("股东会决议")
    private List<CommonBizFile> shareholderCommitteeRessolutionFileList;

    @ApiModelProperty("地址")
    private List<CommonBizFile> adressFileList;

    @ApiModelProperty("身份证件")
    private List<CommonBizFile> identityDocumentFileList;

    @ApiModelProperty(value =  " 变更信息")
    private List<CommonBizFile> businessChangeInfoFileList;

    @ApiModelProperty("其它")
    private List<CommonBizFile> businessOtherFileList;

    @ApiModelProperty("交接单附件")
    private List<CommonBizFile> handoverDocumentFileList;

    @ApiModelProperty(value =  " 逻辑删除标志 1表示删除 0表示未删除")
    private Boolean isDeleted;
}
