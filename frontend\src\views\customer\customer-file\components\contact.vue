<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-05-29 16:43:59
 * @LastEditTime: 2023-09-15 16:01:58
 * @LastEditors: thb
-->
<template>
  <!-- feat-2023-09-11 编辑不需要在详情页面中显示 -->
  <!-- 新增或者编辑 -->
  <template v-if="!disabled">
    <contactAdd ref="tableRef" :id="props.modelValue.ciId" :data="formData.contactTable" @on-edit="handleEdit" />
  </template>
  <template v-else>
    <el-table :data="tableData">
      <el-table-column type="expand">
        <template #default="{ row }">
          <el-row :gutter="20">
            <el-col :span="4">性别：{{ row.sex === '0' ? '未知' : row.sex === '1' ? '男' : '女' }} </el-col>
            <el-col :span="8">邮箱：{{ row.email || '--' }}</el-col>
            <el-col :span="6">QQ: {{ row.qq || '--' }} </el-col>
            <el-col :span="6">生日：{{ row.birthday || '--' }} </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="name" align="center" />
      <el-table-column label="手机号" prop="phone" align="center" />
      <el-table-column label="常用联系人" prop="isOften" align="center">
        <template #default="{ row }">
          {{ row.isOften === 0 ? '否' : '是' }}
        </template>
      </el-table-column>
      <el-table-column label="职位" prop="post" align="center" />
      <el-table-column label="微信号" prop="wechat" align="center" />
      <el-table-column label="关键决策人" prop="isLeader" align="center">
        <template #default="{ row }">
          {{ row.isLeader === 0 ? '否' : '是' }}
        </template>
      </el-table-column>
    </el-table>
  </template>
</template>
<script setup>
import FormTable from '@/components/FormTable'
import { useRemote } from '@/hooks/useRemote'
import { saveCustomerContact, getCustomerContactByCiId } from '@/api/customer/file'
import { cloneDeep, orderBy } from 'lodash'
import contactAdd from './contact-add'

const disabled = inject('disabled')

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

// 获取详情的时候将数据存储一份作为源数据
const originData = ref([])
const isShow = ref(false)
const tableData = ref([])
const getDetail = async flag => {
  const { data } = await getCustomerContactByCiId(props.modelValue.ciId)
  // 对返回的数据显示进行排序(元素为常用联系人的排位靠前，依次排序)
  tableData.value = orderBy(data || [], 'isOften', 'desc')
}
watch(
  disabled,
  () => {
    if (disabled.value) {
      getDetail()
    }
  },
  {
    immediate: true
  }
)
const emits = defineEmits(['update:modelValue', 'on-edit'])

const handleEdit = () => {
  console.log('handleEdit', formData.value)
  emits('on-edit')
}
const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})
const tableRef = ref()
const saveRemote = async () => {
  const validateResult = await tableRef.value.handleValidate()
  if (validateResult) {
    const id = await useRemote(
      saveCustomerContact,
      {
        list: formData.value.contactTable.tableData.map(item => {
          return {
            ...item,
            ciId: formData.value.ciId
          }
        })
      },
      [],
      '联系人'
    )

    formData.value.contractId = id
    return id
  } else {
    return validateResult
  }
}

defineExpose({
  saveRemote
})
</script>
<style lang="scss" scoped></style>
