package com.jri.biz.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Data
@NoArgsConstructor
@ApiModel(value = "个性化表单请求对象")
public class CustomerPersonalizedInformationForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 个性化信息表id
     */
    private Long id;

    /**
     * 客户信息主表id
     */
    private Long ciId;

    /**
     * 客户性格
     */
    private String personality;

    /**
     * 客户类型
     */
    private List<String> types;

    /**
     * 其他标签
     */
    private List<String> tags;

    /**
     * 客户年龄层次
     */
    private String ageLevel;

    /**
     * 客户性格补充
     */
    private String personalityComplement;

    /**
     * 资料收取要求
     */
    private String collectionRequirement;

    /**
     * 财务处理要求
     */
    private String dealRequirement;

    /**
     * 开票特殊需求
     */
    private String billingDemand;

    private Boolean isDeleted;

    @ApiModelProperty("客户股东家庭关系")
    private List<CommonBizFile> shareholderFamilyRelationshipFileList;

    @ApiModelProperty("附件")
    private List<CommonBizFile> personalizedInformationAttachmentFileList;

}
