package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 一般户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_bank_common")
@ApiModel(value = "CustomerBankCommon对象", description = "一般户信息")
public class CustomerBankCommon implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 银行信息id
     */
    private Long bankId;

    /**
     * 一般户开户银行
     */
    private String commonBankName;

    /**
     * 一般户账号
     */
    private String commonBankAccount;

    /**
     * 是否有一般户网银(0-没有 1-有)
     */
    private String commonInternetbankFlag;

    /**
     * 一般户回单卡账号
     */
    private String commonInternetbankAccount;

    /**
     * 一般户回单卡密码
     */
    private String commonReceiptCardPassword;

    /**
     * 一般户回单卡类型
     */
    private String commonInternetbankType;

    /**
     * 是否有一般户回单卡(0-没有 1-有)
     */
    private String commonReceiptCardFlag;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;


}
