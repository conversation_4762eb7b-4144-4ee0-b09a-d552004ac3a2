package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.suggestion.UserSuggestionAddForm;
import com.jri.biz.domain.request.suggestion.UserSuggestionQuery;
import com.jri.biz.domain.request.suggestion.UserSuggestionResponseForm;
import com.jri.biz.domain.request.suggestion.UserSuggestionStarForm;
import com.jri.biz.domain.vo.UserSuggestionListVO;
import com.jri.biz.domain.vo.UserSuggestionVO;
import com.jri.biz.service.UserSuggestionService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 企业用户建议 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Validated
@RestController
@RequestMapping("/userSuggestion")
@Api(tags = "企业用户建议")
public class UserSuggestionController {
    @Resource
    private UserSuggestionService userSuggestionService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<UserSuggestionListVO>> listPage(UserSuggestionQuery query) {
        return R.ok(userSuggestionService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<UserSuggestionVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(userSuggestionService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Void> save(@RequestBody @Valid UserSuggestionAddForm form) {
        userSuggestionService.add(form);
        return R.ok();
    }

    @PostMapping("/response")
    @ApiOperation("回复")
    public R<Boolean> response(@RequestBody @Valid UserSuggestionResponseForm form) {
        userSuggestionService.response(form);
        return R.ok();
    }

    @PostMapping("/star")
    @ApiOperation("评价建议")
    public R<Void> star(@RequestBody @Valid UserSuggestionStarForm form) {
        userSuggestionService.star(form);
        return R.ok();
    }

}

