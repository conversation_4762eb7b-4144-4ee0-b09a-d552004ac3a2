package com.jri.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单类型视图列表对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="OrderTypeListVO视图列表对象")
public class OrderTypeListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 名称
     */
    private String typeName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态0-停用 1-正常
     */
    private String status;

    /**
     * 补充说明
     */
    private String supplementExplain;

    /**
     * 备注
     */
    private String remark;

    /**
     * 父id
     */
    @ApiModelProperty("父id")
    private Long parentId;

    /**
     * 祖级列表
     */
    @ApiModelProperty("祖级列表")
    private String ancestors;

    @ApiModelProperty("子节点")
    private List<OrderTypeListVO> child;
}