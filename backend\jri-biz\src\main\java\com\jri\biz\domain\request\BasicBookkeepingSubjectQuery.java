package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 日记账科目查询类
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="日记账科目查询对象")
public class BasicBookkeepingSubjectQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日记账科目
     */
    @ApiModelProperty("日记账科目")
    private String name;

    /**
     * 使用状态0-停用 1-启用
     */
    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;
}
