package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 客户联系人视图列表对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerContactListVO视图列表对象")
public class CustomerContactListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 客户主表id
     */
    private Long ciId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 部门
     */
    private String dept;

    /**
     * 职务
     */
    private String post;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 是否决策人
     */
    private Integer isLeader;

    /**
     * 联系人详情
     */
    private String details;

    /**
     * 是否常用联系人0-否1-是
     */
    private Integer isOften;

    /**
     * 性别0-未知 1-男 2-女
     */
    private String sex;

    /**
     * QQ
     */
    private String qq;

    /**
     * 生日
     */
    private String birthday;
}