package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("business_product")
@ApiModel(value = "BusinessProduct对象", description = "")
public class BusinessProduct implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品报价
     */
    private String quotation;

    /**
     * 是否在合同中定义0-否1-是
     */
    private String isInContract;

    /**
     * 收费类型0-一次性收费 1-每年收费 2-每月收费
     */
    private String feeType;

    /**
     * 产品代码
     */
    private String code;

    /**
     * 业务类型id
     */
    private Long typeId;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 活动价是否启用0-否1-是
     */
    private String activityStatus;

    /**
     * 活动报价
     */
    private String activityQuotation;

    /**
     * 优惠时长(月)
     */
    private Integer activityDiscountTime;

    @ApiModelProperty("活动价列表")
    @TableField(exist = false)
    private List<BusinessProductActivity> activityList;

    /**
     * 是否给企业用户显示
     */
    private Boolean enterpriseShowFlag;

    /**
     * 使用状态0-停用 1-启用
     */
    private Boolean enable;

}
