package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 保有量设置查询类
 *
 * <AUTHOR>
 * @since 2023-08-22
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="保有量设置查询对象")
public class CusSeaInventoryQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
