package com.jri.web.controller.biz;

import com.alibaba.fastjson2.JSON;
import com.jri.biz.domain.request.CustomerInformationForm;
import com.jri.framework.security.context.AuthenticationContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * <AUTHOR>
 * @since 2023/5/31 13:24
 */
@SpringBootTest
class CustomerInformationControllerTest {
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private CustomerInformationController controller;

    @BeforeEach
    void before() {
        var authenticationToken = new UsernamePasswordAuthenticationToken("admin", "admin123");
        AuthenticationContextHolder.setContext(authenticationToken);
        var authentication = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }

    @Test
    void listPage() throws Exception {
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/customerInformation/list")
                .param("pageNum", "2");
//                .param("pageSize","10")
//                .param("discard","1");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void getDetailById() throws Exception {
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .get("/customerInformation/getById")
                .param("id", "4");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void save() throws Exception {
        var body = new CustomerInformationForm();
        body.setCustomerName("张三1");
        body.setManger("李四1");
        body.setContactPerson("王五1");
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .post("/customerInformation/save")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(body));
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void deleteByIds() throws Exception{
        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .delete("/customerInformation/deleteByIds")
                .param("ids", "4");
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(result);
    }

    @Test
    void upload() throws Exception {

        var mvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders
                .post("/customerInformation/upload");
        var start = System.currentTimeMillis();
        var result = mvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString();
        System.out.println(System.currentTimeMillis() - start);
        System.out.println(result);
        Thread.sleep(10100L);
    }
}