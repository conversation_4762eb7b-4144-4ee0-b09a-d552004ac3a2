package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusCcContact;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusCcContactListVO;
import com.jri.biz.cus.domain.vo.CusCcContactVO;
import com.jri.biz.cus.domain.request.CusCcContactQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 联系人 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface CusCcContactMapper extends BaseMapper<CusCcContact> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusCcContactListVO> listPage(@Param("query") CusCcContactQuery query, Page<CusCcContactListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCcContactVO getDetailById(@Param("id") Long id);

    /**
     * 列表查询
     *
     * @param ccId 客户id
     */
    List<CusCcContactListVO> listByCcId(@Param("ccId") Long ccId);

    List<CusCcContact> getListNotMain(@Param("id") Long id, @Param("mainContactId") Long mainContactId);
}
