// 客户状态字典集合
export const customerStatus = [
  {
    label: '计划记账',
    value: '计划记账'
  },
  {
    label: '正在记账',
    value: '正在记账'
  },
  {
    label: '停止记账',
    value: '停止记账'
  },
  {
    label: '免费白干',
    value: '免费白干'
  },

  {
    label: '个体开票',
    value: '个体开票'
  },
  {
    label: '办证客户',
    value: '办证客户'
  },
  {
    label: '商标客户',
    value: '商标客户'
  },
  {
    label: '其他业务客户',
    value: '其他业务客户'
  }
]

// 客户性质字典集合
export const customerProperty = [
  {
    label: '零申报',
    value: '零申报'
  },
  {
    label: '小规模',
    value: '小规模'
  },
  {
    label: '一般纳税人零申报',
    value: '一般纳税人零申报'
  },
  {
    label: '一般纳税人',
    value: '一般纳税人'
  },
  {
    label: '进出口',
    value: '进出口'
  },
  {
    label: '高新企业',
    value: '高新企业'
  },
  {
    label: '省科小企业',
    value: '省科小企业'
  }
  // {
  //   label: '高新',
  //   value: '高新'
  // }
]
// 客户从事的行业
export const customerIndustry = [
  {
    label: '运输',
    value: '运输'
  },
  {
    label: '房产中介',
    value: '房产中介'
  },
  {
    label: '教育培训',
    value: '教育培训'
  },
  {
    label: '企业服务',
    value: '企业服务'
  },
  {
    label: '信息金融',
    value: '信息金融'
  },
  {
    label: '装饰装修',
    value: '装饰装修'
  },
  {
    label: '其他工程',
    value: '其他工程'
  },
  {
    label: '电子商务',
    value: '电子商务'
  },
  {
    label: '服务箱包',
    value: '服务箱包'
  },
  {
    label: '光伏',
    value: '光伏'
  },
  {
    label: '大宗商品',
    value: '大宗商品'
  },
  {
    label: '快消品',
    value: '快消品'
  },
  {
    label: '进出口',
    value: '进出口'
  },
  {
    label: '生产',
    value: '生产'
  },
  {
    label: '贸易',
    value: '贸易'
  }
]
// 收款是否审核
export const collectionIsCheckedArr = [
  {
    label: '未审核',
    value: 0
  },
  {
    label: '通过',
    value: 1
  },
  {
    label: '驳回',
    value: 2
  }
]
// 收款情况
export const receiveStatusArr = [
  {
    label: '已收款',
    value: '已收款'
  },
  {
    label: '未收款',
    value: '未收款'
  },
  {
    label: '部分收款',
    value: '部分收款'
  }
]
// 合同类型
export const contractTypeArr = [
  {
    label: '记账合同',
    value: '0'
  },
  {
    label: '一次性合同',
    value: '1'
  },
  {
    label: '地址服务协议合同',
    value: '2'
  }
]
// 办理阶段 bizStage
export const bizStageArr = [
  {
    label: '待派工',
    value: 'pending',
    active: 0,
    // activeMap: {
    //   domestic_business_registration: 0,
    //   foreign_business_registration: 0,
    //   bank_account_open: 3
    // },
    finishSteps: [],
    disabledSteps: [],
    couldSteps: [0]
  },
  {
    label: '资料收集',
    value: 'data_collection',
    active: 1,
    finishSteps: [0],
    disabledSteps: [0],
    couldSteps: [0, 1]
  },
  {
    label: '执照办理中',
    value: 'license_processing',
    active: 2,
    finishSteps: [0, 1],
    disabledSteps: [0, 1],
    couldSteps: [0, 1, 2]
  },
  {
    label: '资料上传',
    value: 'info_uploading',
    active: 3,
    finishSteps: [0, 1, 2],
    disabledSteps: [0, 1, 2],
    couldSteps: [0, 1, 2, 3]
  },
  {
    label: '银行开户',
    value: 'bank_account_open',
    active: 4,
    finishSteps: [0, 1, 2, 3],
    disabledSteps: [0, 1, 2, 3],
    couldSteps: [0, 1, 2, 3, 4]
  },
  {
    label: '已完成',
    value: 'completed',
    active: 4,
    finishSteps: [0, 1, 2, 3, 4],
    disabledSteps: [0, 1, 2, 3, 4],
    couldSteps: [0, 1, 2, 3, 4]
  },
  {
    label: '已作废',
    value: 'deprecated',
    active: 0,
    finishSteps: [],
    disabledSteps: [0, 1, 2, 3, 4],
    couldSteps: [0, 1, 2, 3, 4]
  },
  {
    label: '工商注销',
    value: 'business_cancellation'
  },
  {
    label: '变更进度',
    value: 'change_progress'
  },
  {
    label: '资料更新',
    value: 'data_updates'
  },
  {
    label: '银行注销',
    value: 'bank_cancellation'
  }
]

// 业务状态 bizStatus
export const bizStatusArr = [
  {
    label: '办理中',
    value: 'processing'
  },
  {
    label: '已完成',
    value: 'completed'
  },
  {
    label: '已废弃',
    value: 'deprecated'
  }
]

// resolved: 移除证书管理相关的业务类型定义
// 业务名称 bizType
// export const bizTypeArr = [
//   {
//     label: '工商新注册 - 内资',
//     value: 'domestic_business_registration',
//     typeSteps: [0, 1, 2, 3, 4]
//   },
//   {
//     label: '工商新注册 - 外资',
//     value: 'foreign_business_registration',
//     typeSteps: [0, 1, 2, 3, 4]
//   },
//   {
//     label: '银行开户',
//     value: 'bank_account_open',
//     typeSteps: [0, 4]
//   },
//   {
//     label: '许可证 - 进出口',
//     value: 'permit_in_and_out'
//   },
//   {
//     label: '许可证 - 劳务派遣',
//     value: 'permit_labor_dispatch'
//   },
//   {
//     label: '许可证 - 验资',
//     value: 'permit_capital_verification'
//   },
//   {
//     label: '许可证 - 食品证',
//     value: 'permit_food_certificate'
//   },
//   {
//     label: '许可证 - 道路运输',
//     value: 'permit_road_transport'
//   },
//   {
//     label: '工商注销',
//     value: 'business_cancellation'
//   },
//   {
//     label: '工商变更',
//     value: 'business_change'
//   }
// ]

// 资料库存类型
export const materialCategoryArr = [
  { label: '营业执照正本', value: '营业执照正本' },
  { label: '营业执照副本', value: '营业执照副本' },
  { label: '股东会议决议', value: '股东会议决议' },
  { label: '章程', value: '章程' },
  { label: '法人身份证', value: '法人身份证' },
  { label: '非法人身份证', value: '非法人身份证' },
  { label: '公章', value: '公章' },
  { label: '财务章', value: '财务章' },
  { label: '法人章', value: '法人章' },
  { label: '发票章', value: '发票章' },
  { label: '税控盘', value: '税控盘' },
  { label: '税控钥匙', value: '税控钥匙' },
  { label: '空白普票', value: '空白普票' },
  { label: '空白增票', value: '空白增票' },
  { label: '开户信息', value: '开户信息' },
  { label: '网银U盾', value: '网银U盾' },
  { label: '结算卡', value: '结算卡' },
  { label: '密码器', value: '密码器' },
  { label: '回单卡', value: '回单卡' },
  { label: '海关登记证', value: '海关登记证' },
  { label: '电子口岸', value: '电子口岸' },
  { label: '中英文条形章', value: '中英文条形章' },
  { label: '报关章', value: '报关章' },
  { label: '手签章', value: '手签章' },
  { label: '许可证件', value: '许可证件' },
  { label: '合同章', value: '合同章' },
  { label: '凭证', value: '凭证' },
  { label: '账册', value: '账册' },
  { label: '其他账务资料', value: '其他账务资料' },
  { label: '其他资料', value: '其他资料' },
  { label: '预包装备案', value: '预包装备案' }
]

// 风险客户-风险原因
export const reasonDict = [
  {
    label: '注销',
    value: '注销',
    children: [
      { label: '地址问题', value: '地址问题' },
      { label: '经营不善', value: '经营不善' },
      { label: '其他新注册公司', value: '其他新注册公司' },
      { label: '到外地', value: '到外地' }
    ]
  },
  {
    label: '流失',
    value: '流失',
    children: [
      { label: '全职会计', value: '全职会计' },
      { label: '兼职会计', value: '兼职会计' },
      { label: '会计事务所', value: '会计事务所' },
      { label: '传统代账', value: '传统代账' },
      { label: '互联网+', value: '互联网' }
    ]
  },
  {
    label: '拒绝接账',
    value: '拒绝接账',
    children: [
      { label: '欠费', value: '欠费' },
      { label: '发票有问题', value: '发票有问题' },
      { label: '经常欠税金', value: '经常欠税金' },
      { label: '无法满足需求', value: '无法满足需求' }
    ]
  }
]

// 风险客户阶段
export const stageDict = [
  {
    label: '风险审核',
    value: '风险审核',
    children: [
      { label: '财税顾问回访', value: '财税顾问回访' },
      { label: '财税顾问下户', value: '财税顾问下户' },
      { label: '会计主管回访', value: '会计主管回访' },
      { label: '会计主管下户', value: '会计主管下户' },
      { label: '客户经理审批', value: '客户经理审批' },
      { label: '会计经理审批', value: '会计经理审批' },
      { label: '总经理审批', value: '总经理审批' },
      { label: '已挽回', value: '已挽回' }
    ]
  },
  {
    label: '流失清理',
    value: '流失清理',
    children: [
      { label: '出纳确认', value: '出纳确认' },
      { label: '工作清理', value: '工作清理' },
      { label: '正式停账', value: '正式停账' },
      { label: '已完成', value: '已完成' }
    ]
  }
]
