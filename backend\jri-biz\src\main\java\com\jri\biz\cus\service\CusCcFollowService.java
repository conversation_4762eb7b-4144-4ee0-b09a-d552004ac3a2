package com.jri.biz.cus.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.delay.RedisDelayQueueEnum;
import com.jri.biz.cus.delay.util.RedisDelayQueueUtil;
import com.jri.biz.cus.domain.convert.CusCcFollowConvert;
import com.jri.biz.cus.domain.entity.CusCcBusiness;
import com.jri.biz.cus.domain.entity.CusCcFollow;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.entity.CusSea;
import com.jri.biz.cus.domain.request.CusCcFollowForm;
import com.jri.biz.cus.domain.request.CusCcFollowQuery;
import com.jri.biz.cus.domain.vo.CusCcFollowVO;
import com.jri.biz.cus.domain.vo.CusSeaInventoryVO;
import com.jri.biz.cus.mapper.CusCcBusinessMapper;
import com.jri.biz.cus.mapper.CusCcFollowMapper;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 跟进记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class CusCcFollowService extends ServiceImpl<CusCcFollowMapper, CusCcFollow> {

    @Resource
    private CusCustomerOrClueMapper cusCustomerOrClueMapper;

    @Resource
    private CusCcBusinessMapper cusCcBusinessMapper;

    @Resource
    private CusSeaInventoryService cusSeaInventoryService;

    @Resource
    private CusSeaService cusSeaService;

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CusCcFollowVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusCcFollowForm form) {
        CusCcFollow cusCcFollow = CusCcFollowConvert.INSTANCE.convert(form);
        CusCustomerOrClue cusCustomerOrClue = cusCustomerOrClueMapper.selectById(cusCcFollow.getCcId());
        if ("1".equals(cusCustomerOrClue.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }
        final LocalDateTime now = LocalDateTime.now();
        cusCustomerOrClue.setProtectionStartTime(now);
        cusCustomerOrClue.setLastFollowTime(now);
        cusCustomerOrClue.setLastFollowUser(SecurityUtils.getLoginUser().getUser().getNickName());
        String followStatus = cusCustomerOrClue.getFollowStatus();
        if ("0".equals(followStatus)) {
            cusCustomerOrClue.setFollowStatus("1");

        }
        cusCustomerOrClueMapper.updateById(cusCustomerOrClue);
        boolean res = save(cusCcFollow);

        // 修改队列
        Integer duration = 0;
        Integer recovery = 0;
        if (ObjectUtil.isEmpty(cusCustomerOrClue.getSeaId())) {// 私海新建
            CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
            if (null != inventory) {
                if ("0".equals(cusCustomerOrClue.getType())) {
                    duration = inventory.getClueDuration();
                    recovery = inventory.getClueRecovery();
                } else {
                    duration = inventory.getCusDuration();
                    recovery = inventory.getCusRecovery();
                }
            }
        } else { // 领取或者分配
            CusSea byId = cusSeaService.getById(cusCustomerOrClue.getSeaId());
            if (null != byId) {// 公海存在
                duration = byId.getDuration();
                recovery = byId.getRecovery();
            } else { // 公海删除
                CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
                if (null != inventory) {
                    if ("0".equals(cusCustomerOrClue.getType())) {
                        duration = inventory.getClueDuration();
                        recovery = inventory.getClueRecovery();
                    } else {
                        duration = inventory.getCusDuration();
                        recovery = inventory.getCusRecovery();
                    }
                }
            }
        }
        redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        cusCustomerOrClueMapper.updateDuration(cusCustomerOrClue.getId(), 0);
        if (ObjectUtil.isNotEmpty(duration) && duration > 0) {
            cusCustomerOrClueMapper.updateDuration(cusCustomerOrClue.getId(), duration);
            redisDelayQueueUtil.addDelayQueue(cusCustomerOrClue.getId(), duration, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
            if (ObjectUtil.isNotEmpty(recovery) && recovery > 0) {
                if (duration - recovery > 0) {
                    redisDelayQueueUtil.addDelayQueue(cusCustomerOrClue.getId(), duration - recovery, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                }
            }
        }
        return res;
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CusCcFollowForm form) {
        // todo 完善新增/更新逻辑
        CusCcFollow cusCcFollow = CusCcFollowConvert.INSTANCE.convert(form);
        return updateById(cusCcFollow);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    /**
     * 列表查询
     *
     * @param query 查询条件
     */
    public List<CusCcFollow> list(CusCcFollowQuery query) {
        return getBaseMapper().list(query);
    }

    /**
     * 新增商机跟进记录
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBusiness(CusCcFollowForm form) {

        CusCcFollow cusCcFollow = CusCcFollowConvert.INSTANCE.convert(form);
        cusCcFollow.setBizType("1");
        CusCcBusiness cusCcBusiness = cusCcBusinessMapper.selectById(cusCcFollow.getCcId());

        CusCustomerOrClue cusCustomerOrClue = cusCustomerOrClueMapper.selectById(cusCcBusiness.getCcId());
        if ("1".equals(cusCustomerOrClue.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }
        final LocalDateTime now = LocalDateTime.now();
        cusCustomerOrClue.setProtectionStartTime(now);
        cusCustomerOrClue.setLastFollowTime(now);
        cusCustomerOrClue.setLastFollowUser(SecurityUtils.getLoginUser().getUser().getNickName());
        cusCustomerOrClueMapper.updateById(cusCustomerOrClue);

        String followStatus = cusCcBusiness.getFollowStatus();
        if ("0".equals(followStatus)) {
            cusCcBusiness.setFollowStatus("1");// 跟进状态0-未跟进 1-跟进中 2-赢单 3-输单
        }
        cusCcBusinessMapper.updateById(cusCcBusiness);
        boolean save = save(cusCcFollow);

        // 修改队列
        Integer duration = 0;
        Integer recovery = 0;
        if (ObjectUtil.isEmpty(cusCustomerOrClue.getSeaId())) {// 私海新建
            CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
            if (null != inventory) {
                if ("0".equals(cusCustomerOrClue.getType())) {
                    duration = inventory.getClueDuration();
                    recovery = inventory.getClueRecovery();
                } else {
                    duration = inventory.getCusDuration();
                    recovery = inventory.getCusRecovery();
                }
            }
        } else { // 领取或者分配
            CusSea byId = cusSeaService.getById(cusCustomerOrClue.getSeaId());
            if (null != byId) {// 公海存在
                duration = byId.getDuration();
                recovery = byId.getRecovery();
            } else { // 公海删除
                CusSeaInventoryVO inventory = cusSeaInventoryService.getDetailById();
                if (null != inventory) {
                    if ("0".equals(cusCustomerOrClue.getType())) {
                        duration = inventory.getClueDuration();
                        recovery = inventory.getClueRecovery();
                    } else {
                        duration = inventory.getCusDuration();
                        recovery = inventory.getCusRecovery();
                    }
                }
            }
        }
        redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
        redisDelayQueueUtil.removeDelayedQueue(cusCustomerOrClue.getId(), RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
        cusCustomerOrClueMapper.updateDuration(cusCustomerOrClue.getId(), 0);
        if (ObjectUtil.isNotEmpty(duration) && duration > 0) {
            cusCustomerOrClueMapper.updateDuration(cusCustomerOrClue.getId(), duration);
            redisDelayQueueUtil.addDelayQueue(cusCustomerOrClue.getId(), duration, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_TO_SEA.getCode());
            if (ObjectUtil.isNotEmpty(recovery) && recovery > 0) {
                if (duration - recovery > 0) {
                    redisDelayQueueUtil.addDelayQueue(cusCustomerOrClue.getId(), duration - recovery, TimeUnit.DAYS, RedisDelayQueueEnum.RECOVERY_REMIND.getCode());
                }
            }
        }
        return save;
    }

    /**
     * 商机跟进记录列表查询
     *
     * @param query 查询条件
     */
    public List<CusCcFollow> listBusiness(CusCcFollowQuery query) {
        return getBaseMapper().listBusiness(query);
    }
}
