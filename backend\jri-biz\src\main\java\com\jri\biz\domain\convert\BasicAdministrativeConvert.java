package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicAdministrative;
import com.jri.biz.domain.request.BasicAdministrativeForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 行政区划对象转换
 *
 * <AUTHOR>
 * @since 2023-07-31
 */

@Mapper
public interface BasicAdministrativeConvert {
    BasicAdministrativeConvert INSTANCE = Mappers.getMapper(BasicAdministrativeConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BasicAdministrative convert(BasicAdministrativeForm form);

}