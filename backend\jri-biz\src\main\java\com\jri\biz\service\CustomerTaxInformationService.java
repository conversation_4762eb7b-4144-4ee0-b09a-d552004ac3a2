package com.jri.biz.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.CustomerTaxInformationConvert;
import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.jri.biz.domain.entity.CustomerTaxInformation;
import com.jri.biz.domain.entity.CustomerTaxRebateIdentified;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.request.CustomerTaxInformationForm;
import com.jri.biz.domain.request.CustomerTaxInformationQuery;
import com.jri.biz.domain.vo.CustomerTaxInformationListVO;
import com.jri.biz.domain.vo.CustomerTaxInformationVO;
import com.jri.biz.mapper.CustomerTaxExportDetailMapper;
import com.jri.biz.mapper.CustomerTaxInformationMapper;
import com.jri.biz.mapper.CustomerTaxRebateIdentifiedMapper;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerTaxInformationService extends ServiceImpl<CustomerTaxInformationMapper, CustomerTaxInformation> {

    @Resource
    private CustomerTaxRebateIdentifiedService customerTaxRebateIdentifiedService;

    @Resource
    private CustomerTaxExportDetailService customerTaxExportDetailService;

    @Resource
    private CustomerTaxInformationMapper customerTaxInformationMapper;

    @Resource
    private CustomerTaxRebateIdentifiedMapper customerTaxRebateIdentifiedMapper;

    @Resource
    private CustomerTaxExportDetailMapper customerTaxExportDetailMapper;

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CompletenessService completenessService;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerTaxInformationListVO> listPage(CustomerTaxInformationQuery query) {
        var page = new Page<CustomerTaxInformationListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CustomerTaxInformationVO getDetailById(Long id) {
        //查询详细
        CustomerTaxInformationVO customerTaxInformationVO = getBaseMapper().getDetailById(id);
        List<CommonBizFile> customerBillingInformationFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.CUSTOMER_BILLING_INFORMATION);
        List<CommonBizFile> taxInformationFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAX_INFORMATION);
        List<CommonBizFile> taxpayerIdentificationFormFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAXPAYER_IDENTIFICATION_FORM);
        List<CommonBizFile> customerFirstConfirmList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.CUSTOMER_FIRST_CONFIRM);
        customerTaxInformationVO.setCustomerBillingInformationFileList(customerBillingInformationFileList);
        customerTaxInformationVO.setTaxInformationFileList(taxInformationFileList);
        customerTaxInformationVO.setTaxpayerIdentificationFormFileList(taxpayerIdentificationFormFileList);
        customerTaxInformationVO.setCustomerFirstConfirmList(customerFirstConfirmList);

        CustomerTaxRebateIdentified customerTaxRebateIdentified = customerTaxRebateIdentifiedMapper.selectByMainId(customerTaxInformationVO.getId());
        List<CustomerTaxExportDetail> CustomerTaxExportDetail =customerTaxExportDetailMapper.selectByMainId(customerTaxInformationVO.getId());
        customerTaxInformationVO.setCustomerTaxRebateIdentified(customerTaxRebateIdentified);
        customerTaxInformationVO.setCustomerTaxExportDetail(CustomerTaxExportDetail);
        return customerTaxInformationVO;
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdate(CustomerTaxInformationForm form) {
        // todo 完善新增/更新逻辑
        CustomerTaxInformation customerTaxInformation = CustomerTaxInformationConvert.INSTANCE.convert(form);
        CustomerTaxRebateIdentified customerTaxRebateIdentified = form.getCustomerTaxRebateIdentified();
        List<CustomerTaxExportDetail> customerTaxExportDetail = form.getCustomerTaxExportDetail();
        String content = "编辑";
        if(form.getId() == null){
            content = "新增";
        }
        saveOrUpdate(customerTaxInformation);
        customerChangeRecordService.sendChangeMessage(customerTaxInformation.getCiId());
        CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
        changeRecordForm.setCiId(customerTaxInformation.getCiId());
        changeRecordForm.setContent(content);
        changeRecordForm.setInfoSection("税务信息");
        customerChangeRecordService.add(changeRecordForm);
        if (null != customerTaxRebateIdentified) {
            LambdaQueryWrapper<CustomerTaxRebateIdentified> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerTaxRebateIdentified::getMainId, customerTaxInformation.getId());
            wrapper.last("limit 1");
            CustomerTaxRebateIdentified one = customerTaxRebateIdentifiedService.getOne(wrapper);
            if (null != one) {
                customerTaxRebateIdentified.setId(one.getId());
            }
            customerTaxRebateIdentified.setMainId(customerTaxInformation.getId());
            customerTaxRebateIdentifiedService.saveOrUpdate(customerTaxRebateIdentified);
        }
        if (null != customerTaxExportDetail) {
//            Long[] longs = customerTaxExportDetail.stream().map(CustomerTaxExportDetail::getMainId).toArray(Long[]::new);
            CustomerTaxInformationVO customerTaxInformationVO = getDetailByCiId(form.getCiId());
            if (null != customerTaxInformationVO) {
                customerTaxExportDetailService.deleteByMainId(customerTaxInformationVO.getId());
            }
            customerTaxExportDetail.forEach(detail -> detail.setMainId(customerTaxInformation.getId()));
            customerTaxExportDetailService.saveOrUpdateBatch(customerTaxExportDetail);
        }

        //保存附件
        commonBizFileService.deleteByMainIdAndBizType(customerTaxInformation.getCiId(), BizType.CUSTOMER_BILLING_INFORMATION);
        commonBizFileService.deleteByMainIdAndBizType(customerTaxInformation.getCiId(), BizType.TAX_INFORMATION);
        commonBizFileService.deleteByMainIdAndBizType(customerTaxInformation.getCiId(), BizType.TAXPAYER_IDENTIFICATION_FORM);
        commonBizFileService.deleteByMainIdAndBizType(customerTaxInformation.getCiId(), BizType.CUSTOMER_FIRST_CONFIRM);

        List<CommonBizFile> customerBillingInformationFileList =form.getCustomerBillingInformationFileList();
        List<CommonBizFile> taxInformationFileList =form.getTaxInformationFileList();
        List<CommonBizFile> taxpayerIdentificationFormFileList =form.getTaxpayerIdentificationFormFileList();
        List<CommonBizFile> customerFirstConfirmList =form.getCustomerFirstConfirmList();
        if (ObjectUtil.isNotEmpty(customerBillingInformationFileList)) {
            customerBillingInformationFileList.forEach(item -> {
                item.setMainId(customerTaxInformation.getCiId());
                item.setBizType(BizType.CUSTOMER_BILLING_INFORMATION);
            });
            commonBizFileService.saveBatch(customerBillingInformationFileList);
        }
        if (ObjectUtil.isNotEmpty(taxInformationFileList)) {
            taxInformationFileList.forEach(item -> {
                item.setMainId(customerTaxInformation.getCiId());
                item.setBizType(BizType.TAX_INFORMATION);
            });
            commonBizFileService.saveBatch(taxInformationFileList);
        }
        if (ObjectUtil.isNotEmpty(taxpayerIdentificationFormFileList)) {
            taxpayerIdentificationFormFileList.forEach(item -> {
                item.setMainId(customerTaxInformation.getCiId());
                item.setBizType(BizType.TAXPAYER_IDENTIFICATION_FORM);
            });
            commonBizFileService.saveBatch(taxpayerIdentificationFormFileList);
        }
        if (ObjectUtil.isNotEmpty(customerFirstConfirmList)) {
            customerFirstConfirmList.forEach(item -> {
                item.setMainId(customerTaxInformation.getCiId());
                item.setBizType(BizType.CUSTOMER_FIRST_CONFIRM);
            });
            commonBizFileService.saveBatch(customerFirstConfirmList);
        }
        completenessService.UpdateCompleteness(customerTaxInformation.getCiId());
        return  customerTaxInformation.getId();
    }

//    /**
//    * 修改
//    *
//    * @param form 表单
//    * @return 结果
//    */
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean update(CustomerTaxInformationForm form) {
//        // todo 完善新增/更新逻辑
//        CustomerTaxInformation customerTaxInformation = CustomerTaxInformationConvert.INSTANCE.convert(form);
//        return updateById(customerTaxInformation);
//    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        String updateBy= SecurityUtils.getLoginUser().getUser().getNickName();
        CustomerTaxInformationVO customerTaxInformationVO = getDetailById(id);
        // resolved: 移除已删除的BizType常量引用
        // commonBizFileService.deleteByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.CUSTOMER_BILLING_INFORMATION);
        // commonBizFileService.deleteByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAX_INFORMATION);
        // commonBizFileService.deleteByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAXPAYER_IDENTIFICATION_FORM);
        return customerTaxInformationMapper.deleteById(id,updateBy);
    }

    public CustomerTaxInformationVO getDetailByCiId(Long ciId) {
        //查询详细
        CustomerTaxInformationVO customerTaxInformationVO = getBaseMapper().getDetailByCiId(ciId);
        if (null != customerTaxInformationVO) {
            List<CommonBizFile> customerBillingInformationFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.CUSTOMER_BILLING_INFORMATION);
            List<CommonBizFile> taxInformationFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAX_INFORMATION);
            List<CommonBizFile> taxpayerIdentificationFormFileList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.TAXPAYER_IDENTIFICATION_FORM);
            List<CommonBizFile> customerFirstConfirmList = commonBizFileService.selectByMainIdAndBizType(customerTaxInformationVO.getCiId(), BizType.CUSTOMER_FIRST_CONFIRM);
            customerTaxInformationVO.setCustomerBillingInformationFileList(customerBillingInformationFileList);
            customerTaxInformationVO.setTaxInformationFileList(taxInformationFileList);
            customerTaxInformationVO.setTaxpayerIdentificationFormFileList(taxpayerIdentificationFormFileList);
            customerTaxInformationVO.setCustomerFirstConfirmList(customerFirstConfirmList);

            CustomerTaxRebateIdentified customerTaxRebateIdentified = customerTaxRebateIdentifiedMapper.selectByMainId(customerTaxInformationVO.getId());
            List<CustomerTaxExportDetail> CustomerTaxExportDetail =customerTaxExportDetailMapper.selectByMainId(customerTaxInformationVO.getId());
            customerTaxInformationVO.setCustomerTaxRebateIdentified(customerTaxRebateIdentified);
            customerTaxInformationVO.setCustomerTaxExportDetail(CustomerTaxExportDetail);
        }
        return customerTaxInformationVO;
    }
}
