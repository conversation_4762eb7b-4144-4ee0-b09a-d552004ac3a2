package com.jri.biz.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 流程历史视图列表对象
 *
 * <AUTHOR>
 * @since 2023-07-04
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BizNodeHistoryListVO视图列表对象")
public class BizNodeHistoryListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}