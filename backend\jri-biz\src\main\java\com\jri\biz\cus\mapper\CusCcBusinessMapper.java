package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusCcBusiness;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusCcBusinessListVO;
import com.jri.biz.cus.domain.vo.CusCcBusinessVO;
import com.jri.biz.cus.domain.request.CusCcBusinessQuery;
import com.jri.biz.cus.domain.vo.CustomerBusinessListVO;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 商机 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public interface CusCcBusinessMapper extends BaseMapper<CusCcBusiness> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusCcBusinessListVO> listPage(@Param("query") CusCcBusinessQuery query, Page<CusCcBusinessListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCcBusinessVO getDetailById(@Param("id") Long id);

    /**
     * 查询商机列表
     *
     * @param ccId 客户id
     */
    List<CustomerBusinessListVO> listByCcId(@Param("ccId") Long ccId);

    /**
     * 查询客户赢单商机数量
     *
     * @param id 客户id
     */
    Long getWinNum(@Param("id") Long id);

    /**
     * 查询客户输单商机数量
     *
     * @param id 客户id
     */
    Long getLoseNum(@Param("id") Long id);

    /**
     * 查询客户其它商机数量
     *
     * @param id 客户id
     */
    Long getOtherNum(@Param("id") Long id);

    /**
     * 商机数量
     *
     */
    Long getNum();

    /**
     * 按阶段查询数量
     *
     * @param stage 阶段
     */
    Long getNumByStage(@Param("stage") String stage);

    /**
     * 赢单金额
     *
     */
    Long getAmount();

    /**
     * 赢单金额 按来源
     *
     */
    Long getAmountBySource(@Param("source") String source);

    /**
     * 赢单金额 按年月
     *
     * @param year year
     * @param month month
     */
    Long getAmountByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 赢单数量 按年月
     *
     * @param year year
     * @param month month
     */
    Long getCountByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询企业档案关联客户的赢单商机
     *
     * @param ciId 企业id
     */
    List<CusCcBusiness> getByCustomerId(@Param("ciId") Long ciId);

    /**
     * 查询账单编号
     *
     * @param id 账单id
     */
    String getPaymentNoById(@Param("id") String id);
}
