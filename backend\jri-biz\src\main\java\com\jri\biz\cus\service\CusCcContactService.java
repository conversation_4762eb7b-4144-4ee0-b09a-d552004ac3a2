package com.jri.biz.cus.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.convert.CusCcContactConvert;
import com.jri.biz.cus.domain.entity.CusCcContact;
import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.request.CusCcContactBatchForm;
import com.jri.biz.cus.domain.request.CusCcContactForm;
import com.jri.biz.cus.domain.request.CusCcContactQuery;
import com.jri.biz.cus.domain.vo.CusCcContactListVO;
import com.jri.biz.cus.domain.vo.CusCcContactVO;
import com.jri.biz.cus.mapper.CusCcContactMapper;
import com.jri.biz.cus.mapper.CusCustomerOrClueMapper;
import com.jri.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 联系人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class CusCcContactService extends ServiceImpl<CusCcContactMapper, CusCcContact> {

    @Resource
    private CusCustomerOrClueMapper cusCustomerOrClueMapper;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCcContactListVO> listPage(CusCcContactQuery query) {
        var page = new Page<CusCcContactListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CusCcContactVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(CusCcContactForm form) {
        // todo 完善新增/更新逻辑
        CusCcContact cusCcContact = CusCcContactConvert.INSTANCE.convert(form);
        return save(cusCcContact);
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CusCcContactForm form) {
        // todo 完善新增/更新逻辑
        CusCcContact cusCcContact = CusCcContactConvert.INSTANCE.convert(form);
        return updateById(cusCcContact);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    /**
     * 列表查询
     *
     * @param ccId 客户id
     */
    public List<CusCcContactListVO> listByCcId(Long ccId) {
        return getBaseMapper().listByCcId(ccId);
    }

    /**
     * 批量保存数据
     * 
     * @param form form
     */
    public void saveBatch(CusCcContactBatchForm form) {
        CusCustomerOrClue customerOrClue = cusCustomerOrClueMapper.selectById(form.getCcId());
        if ("1".equals(customerOrClue.getIsSea())) {
            throw new ServiceException("客户已回收至公海");
        }
        List<CusCcContactListVO> oldList = getBaseMapper().listByCcId(form.getCcId());
        List<CusCcContactForm> newList = form.getList();
        if (ObjectUtil.isEmpty(newList)) {
            LambdaQueryWrapper<CusCcContact> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CusCcContact::getCcId, form.getCcId());
            remove(wrapper);
        } else {
            if (ObjectUtil.isNotEmpty(oldList)) {
                for (CusCcContactListVO item : oldList) {
                    if (isNotIn(item, newList)) {
                        removeById(item.getId());
                    }
                }
            }
            List<CusCcContact> saveList = new ArrayList<>();
            for (CusCcContactForm item : newList) {
                item.setCcId(form.getCcId());
                CusCcContact cusCcContact = CusCcContactConvert.INSTANCE.convert(item);
                saveList.add(cusCcContact);
            }
            saveOrUpdateBatch(saveList);
        }

    }

    // 是否在新列表存在 不存在就删除
    private boolean isNotIn(CusCcContactListVO item, List<CusCcContactForm> newList) {
        for (CusCcContactForm form : newList) {
            if (null == form.getId()) {
                continue;
            }
            if (form.getId().equals(item.getId())) {
                return false;
            }
        }
        return true;
    }
}
