package com.jri.biz.service.customerUser;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.constants.LicenseBizTypeEnum;
import com.jri.biz.domain.request.CustomerContractQuery;
import com.jri.biz.domain.request.CustomerInformationQuery;
import com.jri.biz.domain.request.license.LicenseBizTaskQuery;
import com.jri.biz.domain.vo.customerUser.UserCustomerContractListVO;
import com.jri.biz.domain.vo.customerUser.UserCustomerInformationListVO;
import com.jri.biz.domain.vo.license.UserCustomerLicenseBizTaskListVO;
import com.jri.biz.mapper.CustomerContractMapper;
import com.jri.biz.mapper.CustomerInformationMapper;
import com.jri.biz.mapper.license.LicenseBizTaskMapper;
import com.jri.biz.mapper.license.LicenseBizTaskProcessRecordMapper;
import com.jri.common.core.domain.entity.SysUser;
import com.jri.common.exception.CheckedException;
import com.jri.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 企业用户相关 service
 *
 * <AUTHOR>
 * @since 2024/1/25 11:16
 */
@Slf4j
@Service
public class CustomerUserService {

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    private CustomerContractMapper customerContractMapper;

    @Resource
    private LicenseBizTaskMapper licenseBizTaskMapper;

    @Resource
    private LicenseBizTaskProcessRecordMapper licenseBizTaskProcessRecordMapper;


    /**
     * 用户绑定企业列表查询
     *
     * @param query 查询条件
     * @return 分页列表
     */
    public IPage<UserCustomerInformationListVO> customerList(CustomerInformationQuery query) {
        checkEnterprise();
        Long userId = SecurityUtils.getUserId();
        query.setUserId(userId);
        var page = new Page<UserCustomerInformationListVO>(query.getPageNum(), query.getPageSize());
        return customerInformationMapper.userCustomerListPage(page, query);
    }

    /**
     * 查询需要签名的合同
     *
     * @param query 查询条件
     * @return 分页列表
     */
    public IPage<UserCustomerContractListVO> contractList(CustomerContractQuery query) {
        checkEnterprise();
        Long userId = SecurityUtils.getUserId();
        query.setUserId(userId);
        var page = new Page<UserCustomerContractListVO>(query.getPageNum(), query.getPageSize());
        var pageList = customerContractMapper.userCustomerContractListPage(query, page);
        for (var vo : pageList.getRecords()) {
            // 根据合同类型截取时间
            if ("0".equals(vo.getContractType())) {
                vo.setStartTime(vo.getStartTime().substring(0, 7));
                vo.setEndTime(vo.getEndTime().substring(0, 7));
            }
        }
        return pageList;
    }

    /**
     * 获取企业用户工商办证
     * @param query 查询条件
     * @return 分页列表
     */
    public IPage<UserCustomerLicenseBizTaskListVO> licenseBizTaskList(LicenseBizTaskQuery query) {
        var page = new Page<UserCustomerLicenseBizTaskListVO>(query.getPageNum(), query.getPageSize());
        // 模糊查询业务类型处理
        String keyword = query.getKeyword();
        if (StrUtil.isNotBlank(keyword)) {
            var bizTypeList = LicenseBizTypeEnum.getEnumByKeyword(keyword);
            query.setBizTypeList(bizTypeList);
        }
        query.setUserId(SecurityUtils.getUserId());
        IPage<UserCustomerLicenseBizTaskListVO> result = licenseBizTaskMapper.userCustomerListPage(query, page);
        for (var record : result.getRecords()) {
            record.setBizStageList(licenseBizTaskProcessRecordMapper.listByTaskId(record.getId()));
        }
        return result;
    }

    /**
     * 获取财税顾问信息
     */
    public SysUser counselorInfo() {
        checkEnterprise();
        Long userId = SecurityUtils.getUserId();
        return customerInformationMapper.getCounselorUserByCustomerUserId(userId);
    }

    /**
     * 企业用户检查
     */
    private void checkEnterprise() {
        if (ObjectUtil.equal(Boolean.FALSE, SecurityUtils.getLoginUser().getUser().getEnterpriseFlag())) {
            throw new CheckedException("非企业用户，无法操作");
        }
    }
}
