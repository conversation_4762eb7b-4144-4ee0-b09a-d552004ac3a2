package com.jri.biz.cus.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.cus.domain.convert.CusSourceDirectConvert;
import com.jri.biz.cus.domain.entity.CusSourceDirect;
import com.jri.biz.cus.domain.request.CusSourceDirectForm;
import com.jri.biz.cus.domain.request.CusSourceDirectQuery;
import com.jri.biz.cus.mapper.CusSourceDirectMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客资来源 直投 关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Service
public class CusSourceDirectService extends ServiceImpl<CusSourceDirectMapper, CusSourceDirect> {

    /**
     * 新增
     *
     * @param form 表单
     */
    public void add(CusSourceDirectForm form) {
        CusSourceDirect cusSourceDirect = CusSourceDirectConvert.INSTANCE.convert(form);
        save(cusSourceDirect);
    }

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return 分页列表
     */
    public IPage<CusSourceDirect> listPage(CusSourceDirectQuery query) {
        var page = new Page<CusSourceDirect>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(page, query);
    }

}
