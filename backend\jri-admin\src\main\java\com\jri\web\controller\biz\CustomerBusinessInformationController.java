package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerBusinessInformationForm;
import com.jri.biz.domain.request.CustomerBusinessInformationQuery;
import com.jri.biz.domain.vo.CustomerBusinessInformationListVO;
import com.jri.biz.domain.vo.CustomerBusinessInformationVO;
import com.jri.biz.service.CustomerBusinessInformationService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerBusinessInformation")
@Api(tags = "客户工商信息")
public class CustomerBusinessInformationController {
    @Resource
    private CustomerBusinessInformationService customerBusinessInformationService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerBusinessInformationListVO>> listPage(CustomerBusinessInformationQuery query) {
        return R.ok(customerBusinessInformationService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerBusinessInformationVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerBusinessInformationService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Long> saveOrUpdate(@RequestBody @Valid CustomerBusinessInformationForm form) {
        return R.ok(customerBusinessInformationService.saveOrUpdate(form));
    }

    @GetMapping("/getByCiId")
    @ApiOperation("通过ciId获取详情")
    public R<CustomerBusinessInformationVO> getDetailByCiId(@RequestParam("id") Long ciId) {
        return R.ok(customerBusinessInformationService.getDetailByCiId(ciId));
    }

//    @PutMapping("/update")
//    @ApiOperation("更新数据")
//    public R<Boolean> update(@RequestBody @Valid CustomerBusinessInformationForm form) {
//        return R.ok(customerBusinessInformationService.update(form));
//    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerBusinessInformationService.deleteById(id));
    }
}

