package com.jri.biz.service.finance;

import org.junit.jupiter.api.Test;


import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

class FinancePaymentServiceTest {
    @Resource
    private FinancePaymentService financePaymentService;
    @Test
    void dateAddMonth() {
        LocalDate localDate =  LocalDate.of(2023, 6, 1);
        int addMonth[]={1,2,2};
        if(localDate.getDayOfMonth()!=1){
            throw new RuntimeException("账期开始时间不是从1号开始");
        }
        LocalDate lastDayOfPreviousMonth =localDate.plusMonths(Arrays.stream(addMonth).sum()).withDayOfMonth(1).minusDays(1);
        System.out.println(lastDayOfPreviousMonth);
    }
}
