package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.cus.domain.entity.CusSourceDirect;
import com.jri.biz.cus.domain.request.CusSourceDirectQuery;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 客资来源 直投 关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface CusSourceDirectMapper extends BaseMapper<CusSourceDirect> {

    /**
     * 根据主键查询记录
     *
     * @param mainId 主键id
     * @return 列表
     */
    default List<CusSourceDirect> getListByMainId(Long mainId) {
        LambdaQueryWrapper<CusSourceDirect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CusSourceDirect::getMainId, mainId);
        return selectList(queryWrapper);
    }

    /**
     * 分页
     *
     * @param page  分页条件
     * @param query 查询条件
     * @return 分页列表
     */
    default IPage<CusSourceDirect> listPage(@Param("page") Page<CusSourceDirect> page, @Param("query") CusSourceDirectQuery query) {
        LambdaQueryWrapper<CusSourceDirect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CusSourceDirect::getMainId, query.getMainId());
        queryWrapper.orderByDesc(CusSourceDirect::getChargeTime);
        return selectPage(page, queryWrapper);
    }

    /**
     * 获取最近的充值记录
     *
     * @param date 日期
     * @return 充值记录列表
     */
    List<CusSourceDirect> selectListByRecentDate(@Param("date") LocalDate date, @Param("mainId") Long mainId);

    /**
     * 获取最小充值时间
     *
     * @param date 日期
     * @return 日期
     */
    LocalDate getMinChargeTime(@Param("date") LocalDate date, @Param("mainId") Long mainId);

}
