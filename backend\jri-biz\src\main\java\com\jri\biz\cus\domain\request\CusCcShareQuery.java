package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 线索/客户共享查询类
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="线索/客户共享查询对象")
public class CusCcShareQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
