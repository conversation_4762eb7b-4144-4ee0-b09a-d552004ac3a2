package com.jri.biz.constants;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 工商办证业务常量
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LicenseBizTypeEnum {

    DOMESTIC_BUSINESS_REGISTRATION("domestic_business_registration", "工商新注册 - 内资"),
    FOREIGN_BUSINESS_REGISTRATION("foreign_business_registration", "工商新注册 - 外资"),
    PERMIT_IN_AND_OUT("permit_in_and_out", "许可证 - 进出口"),
    PERMIT_LABOR_DISPATCH("permit_labor_dispatch", "许可证 - 劳务派遣"),
    PERMIT_CAPITAL_VERIFICATION("permit_capital_verification", "许可证 - 验资"),
    PERMIT_FOOD_CERTIFICATE("permit_food_certificate", "许可证 - 食品证"),
    PERMIT_ROAD_TRANSPORT("permit_road_transport", "许可证 - 道路运输"),
    BUSINESS_CANCELLATION("business_cancellation", "工商注销"),
    BUSINESS_CHANGE("business_change", "工商变更"),
    BANK_CANCELLATION("bank_cancellation", "银行注销"),
    BANK_ACCOUNT_OPEN("bank_account_open", "银行开户");

    private String code;

    private String value;

    public static String searchValueByCode(String code) {
        for (LicenseBizTypeEnum eventNodeEnum : LicenseBizTypeEnum.values()) {
            if (eventNodeEnum.getCode().equals(code)) {
                return eventNodeEnum.getValue();
            }
        }
        return null;
    }

    public static LicenseBizTypeEnum getEnumByCode(String code) {
        for (LicenseBizTypeEnum eventNodeEnum : LicenseBizTypeEnum.values()) {
            if (eventNodeEnum.getCode().equals(code)) {
                return eventNodeEnum;
            }
        }
        return null;
    }

    /**
     * 模糊查询value返回对应code List
     */
    public static List<String> getEnumByKeyword(String keyword) {
        return Arrays.stream(values())
                .filter(eventNodeEnum -> eventNodeEnum.getValue().contains(keyword))
                .map(LicenseBizTypeEnum::getCode)
                .toList();
    }
}
