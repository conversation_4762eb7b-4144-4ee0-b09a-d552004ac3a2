    package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSeaInventory;
import com.jri.biz.cus.domain.request.CusSeaInventoryForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 保有量设置对象转换
 *
 * <AUTHOR>
 * @since 2023-08-22
 */

@Mapper
public interface CusSeaInventoryConvert {
    CusSeaInventoryConvert INSTANCE = Mappers.getMapper(CusSeaInventoryConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusSeaInventory convert(CusSeaInventoryForm form);

}