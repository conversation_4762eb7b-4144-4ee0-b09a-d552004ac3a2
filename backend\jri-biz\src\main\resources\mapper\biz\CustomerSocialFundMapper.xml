<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerSocialFundMapper">
    <delete id="deleteById">
        update customer_social_fund set is_deleted='1',update_by=#{updateBy} where id =#{id}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerSocialFundListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerSocialFundVO">
        select * from customer_social_fund where is_deleted=0 and id= #{id}
    </select>
    <select id="getDetailByCiId" resultType="com.jri.biz.domain.vo.CustomerSocialFundVO">
        select * from  customer_social_fund where is_deleted=0 and ci_id=#{ciId}
    </select>
</mapper>
