<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-05-25 22:51:26
 * @LastEditTime: 2024-01-09 13:55:19
 * @LastEditors: thb
-->
<template>
  <el-card class="c-sh">
    <!-- <el-row justify="between"> </el-row>
    <el-row class="container" justify="between">

    </el-row> -->
    <div class="container">
      <div
        v-for="(item, index) in leftData"
        :key="index"
        class="list-item-1 list-item"
        @click="handleSearch('customerStatus', item.name)"
      >
        <span class="color-blue">{{ item.name }}</span>
        <div class="item-text">
          <span class="text-1"> {{ item.value }}</span>
          家
        </div>
      </div>
      <div
        v-for="item in rightData"
        :key="item"
        class="list-item-2 list-item"
        :class="item.type === 'companyIdentification' ? 'green-bg' : ''"
        @click="handleSearch(item.type === 'companyIdentification' ? 'companyIdentification' : 'customerProperty', item.name)"
      >
        <span class="color-yellow" :class="item.type === 'companyIdentification' ? 'color-green' : ''">{{ item.name }}</span>
        <div class="item-text">
          <span class="text-1"> {{ item.value }}</span>
          家
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { onMounted, watch } from 'vue'
import { getCustomerStatics } from '@/api/customer/file'
import useAppStore from '@/store/modules/app'
const appStore = useAppStore()
const isCollapse = computed(() => !appStore.sidebar.opened)
const greenCor = ['highTech', 'shengKeXiao']
const leftData = ref({})
const rightData = ref({})
const itemWidth = ref('232px') //246

const getList = async () => {
  const { data } = await getCustomerStatics()
  leftData.value = data.customerStatusList || []
  rightData.value = data.customerPropertyList.concat(
    data.companyIdentificationList.map(item => {
      return {
        ...item,
        type: 'companyIdentification'
      }
    })
  )
}

// 列表item点击实现列表筛选功能
const emits = defineEmits(['on-search'])
const handleSearch = (searchType, searchValue) => {
  emits('on-search', {
    searchType,
    searchValue
  })
}
getList()
onMounted(() => {
  // getList()
})

defineExpose({ getList })
</script>

<style lang="scss" scoped>
.el-card {
  margin-bottom: 20px;
  :deep(.el-card__body) {
    padding: 8px !important;
  }
}
.el-card.is-always-shadow {
  box-shadow: none;
  border: none;
}
.list-item {
  display: flex;
  flex-direction: column;
  padding: 9px 12px;
  flex: 0 0 v-bind(itemWidth);
  height: 72px;
  border-radius: 2px;
  cursor: pointer;
  span:first-child {
    margin-bottom: 8px;
  }
}
.list-item-1 {
  background: #eff7ff;
  &:last-child {
    margin-right: 0;
  }
}

.list-item-2 {
  background: rgba(255, 247, 234, 1);
  margin-bottom: 0;
}
.green-bg {
  background: rgb(*********** / 40%);
}

.container {
  // display: flex;
  // justify-content: space-between;
  // flex-wrap: wrap;
  // grid 布局 两端对齐，最后一行左对齐
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  resize: both;
  &::after {
    content: '';
    display: block;
    flex: 1 1 auto;
  }
}
.color-blue {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #2383e7;
}
.color-yellow {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #f6a01d;
}
.color-green {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: rgb(70 159 93);
}
.c-sh {
  flex-shrink: 0;
}
.item-text {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #7d8592;
}
.text-1 {
  font-size: 20px;
  font-family: Barlow-Bold, Barlow;
  font-weight: bold;
  color: #333333;
}
</style>
