package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 用户客户绑定记录 表单请求对象
 *
 * <AUTHOR>
 * @since 2024-01-24
 */

@Data
@NoArgsConstructor
@ApiModel(value="用户客户绑定记录表单请求对象")
public class UserCustomerBindRecordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "用户id不能为空")
    @ApiModelProperty(value ="用户id")
    private Long userId;

    @NotEmpty(message = "客户id列表不能为空")
    @ApiModelProperty(value ="客户id列表")
    private List<Long> customerIdList;

}
