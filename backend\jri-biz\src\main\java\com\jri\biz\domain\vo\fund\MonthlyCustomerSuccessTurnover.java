package com.jri.biz.domain.vo.fund;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "客户成功月营业额度明细")
public class MonthlyCustomerSuccessTurnover implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联企业中完全收回当月到期账单的企业数（付了一半的不算）
     */
    @ApiModelProperty(value = "当月到期账单结清企业数量")
    private Long monthPaymentClearCustomer = 0L;

    @ApiModelProperty(value = "当月实收")
    private BigDecimal allReceiptAmount = BigDecimal.ZERO;

    /**
     * 前三个月到期的账单中没收的钱
     */
    @ApiModelProperty(value = "当月欠费")
    private BigDecimal monthDebt = BigDecimal.ZERO;

    /**
     * 关联企业中前三个月到期的账单有钱没收（完）企业数
     */
    @ApiModelProperty(value = "当月到期账单未结清企业数量")
    private Long monthPaymentDebtCustomer = 0L;

}
