package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 分公司信息 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-02
 */

@Data
@NoArgsConstructor
@ApiModel(value="分公司信息表单请求对象")
public class BasicCompanyForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 分公司名称
     */
    @ApiModelProperty("分公司名称")
    private String name;

    /**
     * 显示顺序
     */
    @ApiModelProperty("显示顺序")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 使用状态0-停用 1-启用
     */
    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable;

    /**
     * 分公司地址
     */
    @ApiModelProperty("分公司地址")
    private String address;

    /**
     * 分公司联系人
     */
    @ApiModelProperty("分公司联系人")
    private String contacts;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String phone;

    /**
     * 账号信息
     */
    @ApiModelProperty("账号信息")
    private String account;
}
