package com.jri.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.entity.CustomerUserLinkRecord;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * 客户用户关联表 mapper接口
 */
public interface CustomerUserLinkRecordMapper extends BaseMapper<CustomerUserLinkRecord> {


    /**
     * 获取还在关联的记录
     *
     * @param customerId 客户id
     * @param userId     用户id
     * @return 记录
     */
    CustomerUserLinkRecord getByLinkRecord(@Param("customerId") Long customerId,
                                           @Param("role") String role,
                                           @Param("userId") Long userId);


    Long linkRecordCount(@Param("userId") Long userId,
                         @Param("endDateStart") LocalDate endDateStart,
                         @Param("customerSuccess") String customerSuccess,
                         @Param("deptId") Long deptId,
                         @Param("role") String role);
}
