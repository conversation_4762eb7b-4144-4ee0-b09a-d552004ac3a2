package com.jri.biz.domain.entity.address;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 出租人
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("address_lessor")
@ApiModel(value = "AddressLessor对象", description = "出租人")
public class AddressLessor implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;

    @ApiModelProperty("出租人姓名")
    private String lessorName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账户")
    private String bankAccount;


}
