package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线索/客户信息视图列表对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCustomerOrClueListVO视图列表对象")
public class CusCustomerOrClueListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    // *************联系人表******************

    @Excel(name = "姓名")
    @ApiModelProperty("姓名")
    private String contactName;

    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("线索来源(客户来源)")
    private String source;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @Excel(name = "线索来源")
    @ApiModelProperty("来源名称")
    private String sourceName;

    @Excel(name = "跟进人")
    @ApiModelProperty("跟进人")
    private String currentUserName;

    @Excel(name = "跟进状态", readConverterExp = "0=未跟进,1=跟进中,2=已转企业,3=申述中")
    @ApiModelProperty("跟进状态")
    private String followStatus;

    @Excel(name = "最近跟进时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFollowTime;

    //************标签*************

    @Excel(name = "标签")
    @ApiModelProperty("标签名")
    private String tagsName;

    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;

    @Excel(name = "创建人")
    @ApiModelProperty("创建者")
    private String createBy;

    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("是否申诉")
    private Boolean appealFlag;

    @ApiModelProperty("产品名称")
    private String productName;

    @Excel(name = "公司名称")
    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("税务性质")
    private String taxNature;

    @Excel(name = "首次跟进时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("首次跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstFollowTime;

    @Excel(name = "职位")
    @ApiModelProperty("职位")
    private String post;

    @Excel(name = "行业")
    @ApiModelProperty("行业")
    private String industry;

    @Excel(name = "邮箱")
    @ApiModelProperty("邮箱")
    private String email;

    @Excel(name = "微信")
    @ApiModelProperty("微信")
    private String wx;

    @Excel(name = "QQ")
    @ApiModelProperty("QQ")
    private String qq;

    @Excel(name = "性别", readConverterExp = "0=未知,1=男,2=女")
    @ApiModelProperty("性别")
    private String sex;

    @Excel(name = "生日")
    @ApiModelProperty("生日")
    private String birthday;

    @Excel(name = "地区")
    @ApiModelProperty("地区")
    private String area;

    @Excel(name = "详细地址")
    @ApiModelProperty("详细地址")
    private String address;

}