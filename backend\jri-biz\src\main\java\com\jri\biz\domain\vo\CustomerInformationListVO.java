package com.jri.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 视图列表对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "CustomerInformationListVO视图列表对象")
public class CustomerInformationListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户名称")
    @Excel(name = "企业名称")
    private String customerName;

    @ApiModelProperty(value = "客户编号")
    @Excel(name = "企业编号")
    private String customerNo;

    @ApiModelProperty(value = "联系人姓名")
    @Excel(name = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系人电话")
    @Excel(name = "手机号")
    private String contactPhone;

    @ApiModelProperty(value = "主联系人id")
    private Long contractMainId;

    @ApiModelProperty(value = "客户经理")
    private String manger;

    @ApiModelProperty(value = "财税顾问用户id")
    private Long mangerUserId;

    @ApiModelProperty(value = "开票员用户id")
    private Long counselorUserId;

    @ApiModelProperty(value = "客户成功用户id")
    private Long customerSuccessUserId;

    @ApiModelProperty(value = "主办会计用户id")
    private Long sponsorAccountingUserId;

    @ApiModelProperty(value = "财税顾问")
    @Excel(name = "财税顾问")
    private String managerUserName;

    @ApiModelProperty(value = "开票员")
    @Excel(name = "开票员")
    private String counselorUserName;

    @ApiModelProperty(value = "客户成功")
    @Excel(name = "客户成功")
    private String customerSuccessUserName;

    @ApiModelProperty(value = "主办会计姓名")
    @Excel(name = "主办会计")
    private String sponsorAccountingUserName;

    @ApiModelProperty(value = "所属分公司")
    @Excel(name = "所属分公司")
    private String branchOffice;

    @ApiModelProperty(value = "实际经营地址")
    @Excel(name = "实际经营地址")
    private String address;

    @ApiModelProperty(value = "企业联系信息备注")
    @Excel(name = "备注")
    private String informationMark;

    @ApiModelProperty(value = "客户状态")
    @Excel(name = "企业状态")
    private String customerStatus;

    @ApiModelProperty(value = "客户性质")
    @Excel(name = "企业性质")
    private String customerProperty;

    @ApiModelProperty(value = "企业认定")
    @Excel(name = "企业认定")
    private String companyIdentification;

    @ApiModelProperty("不收费原因")
    @Excel(name = "不收费原因")
    private String nofeeReason;

    @ApiModelProperty(value = "不收费原因备注")
    @Excel(name = "不收费原因备注")
    private String nofeeReasonMark;

    @ApiModelProperty(value = "是否废弃")
    private Integer discard;

    @ApiModelProperty(value = "废弃原因")
    private String discardReason;

    @ApiModelProperty(value = "从事行业")
    @Excel(name = "从事行业")
    private String industry;

    /*********************************分公司信息********************************/

    @ApiModelProperty("所属公司联系人")
    private String companyPerson;

    @ApiModelProperty("所属公司联系电话")
    private String companyPhone;

    @ApiModelProperty("所属公司地址")
    private String companyAddress;


    /*********************************银行信息********************************/

    private Long bankId;

    @ApiModelProperty("基本户开户银行")
    @Excel(name = "基本户开户银行")
    private String bankBaseName;

    @ApiModelProperty("基本户账号")
    @Excel(name = "基本户账号")
    private String bankBaseAccount;

    @ApiModelProperty("是否有回单卡(0-没有 1-有)")
    @Excel(name = "回单卡", readConverterExp = "0=无,1=有")
    private String receiptCardFlag;

    @ApiModelProperty("回单卡类型")
    @Excel(name = "回单卡类型")
    private String receiptCardType;

    @ApiModelProperty("回单卡账户")
    @Excel(name = "回单卡账号")
    private String receiptCardAccount;

    @ApiModelProperty("回单卡密码")
    @Excel(name = "回单卡密码")
    private String receiptCardPassword;

    @ApiModelProperty("一般户账户数量")
    @Excel(name = "一般户账户数量(个)")
    private Long commonListSize;

    /*********************************工商信息********************************/

    @ApiModelProperty(value = "统一社会信用代码")
    @Excel(name = "统一社会信用代码")
    private String crediCode;

    @ApiModelProperty(value = "公司类型")
    @Excel(name = "公司类型")
    private String type;

    @ApiModelProperty(value = "法定代表人")
    @Excel(name = "法定代表人")
    private String legalPerson;

    @ApiModelProperty(value = "注册地址")
    @Excel(name = "注册地址")
    private String registeredAddress;

    @ApiModelProperty(value = "联系方式")
    @Excel(name = "联系方式")
    private String contract;

    @ApiModelProperty(value = "经营范围")
    @Excel(name = "经营范围")
    private String scope;

    @ApiModelProperty(value = "网址")
    @Excel(name = "网址")
    private String website;

    @ApiModelProperty(value = "经营状态")
    @Excel(name = "经营状态")
    private String bussinessStatus;

    @ApiModelProperty(value = "登记机关")
    @Excel(name = "登记机关")
    private String registrationAuthority;

    @ApiModelProperty(value = "成立日期")
    @Excel(name = "成立日期")
    private String establishDate;

    @ApiModelProperty(value = "注册资金")
    @Excel(name = "注册资金")
    private String registeredCapital;

    @ApiModelProperty(value = "行业(存在同名 修改字段名)")
    @Excel(name = "行业")
    private String businessIndustry;

    @ApiModelProperty(value = "注册号")
    @Excel(name = "注册号")
    private String registrationNumber;

    @ApiModelProperty(value = "营业开始时间")
    @Excel(name = "营业开始时间")
    private String openDate;

    @ApiModelProperty(value = "营业结束时间")
    @Excel(name = "营业结束时间")
    private String openEnd;

    @ApiModelProperty(value = "组织机关代码")
    @Excel(name = "组织机关代码")
    private String organizationCode;

    @ApiModelProperty(value = "核准日期")
    @Excel(name = "核准日期")
    private String approvalDate;

    /*********************************许可证信息********************************/

    @ApiModelProperty(value = "许可证数量")
    @Excel(name = "许可证数量(个)")
    private Integer licenseSize;

    /*********************************社保公积金信息********************************/

    @ApiModelProperty(value = "社保是否开户(0未开 1开户)")
    @Excel(name = "社保开户", readConverterExp = "0=否,1=是")
    private String socialAccountFlag;

    @ApiModelProperty(value = "社保账号")
    @Excel(name = "社保账号")
    private String socialAccount;

    @ApiModelProperty(value = "社保密码")
    @Excel(name = "社保密码")
    private String socialPassword;

    @ApiModelProperty(value = "公积金是否开户(0未开 1开户)")
    @Excel(name = "公积金开户", readConverterExp = "0=否,1=是")
    private String fundAccountFlag;

    @ApiModelProperty(value = "公积金账户")
    @Excel(name = "公积金账户")
    private String fundAccount;

    @ApiModelProperty(value = "公积金密码")
    @Excel(name = "公积金密码")
    private String fundPassword;

    /*********************************税务信息********************************/

    @ApiModelProperty(value = "登记税务机关")
    @Excel(name = "登记税务机关")
    private String taxRegistrationOrgan;

    @ApiModelProperty(value = "税务机关地址")
    @Excel(name = "税务机关地址")
    private String taxOrganAddress;

    @ApiModelProperty(value = "税率登记")
    @Excel(name = "税率登记")
    private String rateRegistration;

    @ApiModelProperty(value = "办税员实名认证")
    @Excel(name = "办税员实名认证", readConverterExp = "true=是,false=否")
    private Boolean taxRealNameFlag;

    @ApiModelProperty(value = "个体户核定")
    @Excel(name = "个体户核定", readConverterExp = "true=是,false=否")
    private Boolean individualCheckFlag;

    @ApiModelProperty(value = "预留手机号")
    @Excel(name = "预留手机号")
    private String reservedPhoneNumber;

    @ApiModelProperty(value = "自然人密码")
    @Excel(name = "自然人密码")
    private String naturalPersonPassword;

    @ApiModelProperty(value = "所得税认定方式")
    @Excel(name = "个体户核定", readConverterExp = "true=核定,false=查账")
    private Boolean identificationMethodFlag;

    @ApiModelProperty(value = "证书账号")
    @Excel(name = "证书账号")
    private String certificateAccount;

    @ApiModelProperty(value = "证书密码")
    @Excel(name = "证书密码")
    private String certificatePassword;

    @ApiModelProperty(value = "开票盘")
    @Excel(name = "发票盘")
    private String drawingSheetFlag;

    @ApiModelProperty(value = "发票")
    @Excel(name = "发票")
    private String invoiceFlag;

    @ApiModelProperty(value = "发票章")
    @Excel(name = "发票章")
    private String invoiceSealFlag;

    @ApiModelProperty(value = "开票盘归属部门")
    @Excel(name = "开票盘归属部门")
    private String drawingSheetDept;

    @ApiModelProperty(value = "发票归属部门")
    @Excel(name = "发票归属部门")
    private String invoiceDept;

    @ApiModelProperty(value = "发票章归属部门")
    @Excel(name = "发票章归属部门")
    private String invoiceSealDept;

    @ApiModelProperty(value = "发票类型")
    @Excel(name = "发票类型")
    private String invoiceType;

    @ApiModelProperty(value = "开票盘类型")
    @Excel(name = "开票盘类型")
    private String drawingSheetType;

    @ApiModelProperty(value = "发票额度")
    private String invoiceLimit;

    @ApiModelProperty(value = "看点时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "看点时间", dateFormat = "yyyy-MM-dd")
    private LocalDate watchTime;

    @ApiModelProperty(value = "进出口认定时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进出口认定时间", dateFormat = "yyyy-MM-dd")
    private LocalDate approvalTime;

    @ApiModelProperty(value = "看点说明")
    @Excel(name = "看点说明")
    private String watchInstructions;

    @ApiModelProperty(value = "所属部门")
    @Excel(name = "所属部门")
    private String dept;


    /*********************************个性化信息********************************/

    @ApiModelProperty(value = "客户性格")
    @Excel(name = "客户性格")
    private String personality;

    @ApiModelProperty(value = "客户类型")
    private List<String> types;
    @Excel(name = "客户类型")
    private String typeStr;

    @ApiModelProperty(value = "客户标签")
    private List<String> tags;
    @Excel(name = "客户标签")
    private String tagStr;

    @ApiModelProperty(value = "客户年龄层次")
    @Excel(name = "客户年龄层次")
    private String ageLevel;

    @ApiModelProperty(value = "客户性格补充")
    @Excel(name = "客户性格补充")
    private String personalityComplement;

    @ApiModelProperty(value = "资料收取要求")
    @Excel(name = "资料收取要求")
    private String collectionRequirement;

    @ApiModelProperty(value = "财务处理要求")
    @Excel(name = "财务处理要求")
    private String dealRequirement;

    @ApiModelProperty(value = "开票特殊需求")
    @Excel(name = "开票特殊需求")
    private String billingDemand;

    //=== 其他关联注入信息 ===
    @ApiModelProperty(value = "月费服务")
    @Excel(name = "月营业额")
    private BigDecimal monthlyFee;

    @ApiModelProperty(value = "记账营业额")
    @Excel(name = "记账营业额")
    private BigDecimal bookkeepingMonthlyFee;

    @ApiModelProperty(value = "地址营业额")
    @Excel(name = "地址营业额")
    private BigDecimal addressMonthlyFee;

    @ApiModelProperty(value = "总欠费合计")
    @Excel(name = "总欠费合计")
    private BigDecimal arrears;

    @Excel(name = "最近合同到期日", dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "最近合同到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate dueDate;

    @ApiModelProperty(value = "最后跟进时间")
    @Excel(name = "最后跟进时间")
    private String latestFollowUpDate;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "资料完善度")
    @Excel(name = "资料完善度")
    private BigDecimal completeness;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;

    @ApiModelProperty(value = "距离")
    private BigDecimal distance;


    public void setTypes(List<String> types) {
        this.types = types;
        if (null == types || types.isEmpty()) {
            this.typeStr =  "";
        } else {
            this.typeStr = String.join(",", types);
        }
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
        if (null == tags || tags.isEmpty()) {
            this.tagStr = "";
        } else {
            this.tagStr = String.join(",", tags);
        }
    }
}
