package com.jri.biz.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.CustomerPersonalizedInformationConvert;
import com.jri.biz.domain.entity.CustomerPersonalizedInformation;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.request.CustomerPersonalizedInformationForm;
import com.jri.biz.domain.request.CustomerPersonalizedInformationQuery;
import com.jri.biz.domain.vo.CustomerPersonalizedInformationListVO;
import com.jri.biz.domain.vo.CustomerPersonalizedInformationVO;
import com.jri.biz.mapper.CustomerPersonalizedInformationMapper;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerPersonalizedInformationService extends ServiceImpl<CustomerPersonalizedInformationMapper, CustomerPersonalizedInformation> {

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CustomerPersonalizedInformationMapper customerPersonalizedInformationMapper;

    @Resource
    private CompletenessService completenessService;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerPersonalizedInformationListVO> listPage(CustomerPersonalizedInformationQuery query) {
        var page = new Page<CustomerPersonalizedInformationListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerPersonalizedInformationVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long add(CustomerPersonalizedInformationForm form) {
        //附件 银行开户信息表 变更信息
        commonBizFileService.deleteByMainIdAndBizType(form.getCiId(), BizType.SHAREHOLDER_FAMILY_RELATIONSHIP);
        commonBizFileService.deleteByMainIdAndBizType(form.getCiId(), BizType.PERSONALIZED_INFORMATION_ATTACHMENT);
        List<CommonBizFile> shareholderFamilyRelationshipFileList = form.getShareholderFamilyRelationshipFileList();
        List<CommonBizFile> personalizedInformationAttachmentFileList = form.getPersonalizedInformationAttachmentFileList();
        if (ObjectUtil.isNotEmpty(shareholderFamilyRelationshipFileList)) {
            shareholderFamilyRelationshipFileList.forEach(item -> {
                item.setMainId(form.getCiId());
                item.setBizType(BizType.SHAREHOLDER_FAMILY_RELATIONSHIP);
            });
            commonBizFileService.saveBatch(shareholderFamilyRelationshipFileList);
        }
        if (ObjectUtil.isNotEmpty(personalizedInformationAttachmentFileList)) {
            personalizedInformationAttachmentFileList.forEach(item -> {
                item.setMainId(form.getCiId());
                item.setBizType(BizType.PERSONALIZED_INFORMATION_ATTACHMENT);
            });
            commonBizFileService.saveBatch(personalizedInformationAttachmentFileList);
        }
        CustomerPersonalizedInformation customerPersonalizedInformation = CustomerPersonalizedInformationConvert.INSTANCE.convert(form);
        String content = "编辑";
        if(form.getId()==null){
            content = "新增";
        }
        if(saveOrUpdate(customerPersonalizedInformation)){
            customerChangeRecordService.sendChangeMessage(customerPersonalizedInformation.getCiId());
            completenessService.UpdateCompleteness(customerPersonalizedInformation.getCiId());
            CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
            changeRecordForm.setCiId(customerPersonalizedInformation.getCiId());
            changeRecordForm.setContent(content);
            changeRecordForm.setInfoSection("个性化信息");
            customerChangeRecordService.add(changeRecordForm);
            return customerPersonalizedInformation.getId();
        }else {
            return -1L;
        }
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerPersonalizedInformationForm form) {
        // todo 完善新增/更新逻辑
        CustomerPersonalizedInformation customerPersonalizedInformation = CustomerPersonalizedInformationConvert.INSTANCE.convert(form);
        return updateById(customerPersonalizedInformation);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        String updateBy= SecurityUtils.getLoginUser().getUser().getNickName();
        CustomerPersonalizedInformationVO customerPersonalizedInformationVO = getDetailById(id);
        commonBizFileService.deleteByMainIdAndBizType(customerPersonalizedInformationVO.getCiId(), BizType.SHAREHOLDER_FAMILY_RELATIONSHIP);
        commonBizFileService.deleteByMainIdAndBizType(customerPersonalizedInformationVO.getCiId(), BizType.INVOICING_SPECIAL_REQUIREMENT);
        commonBizFileService.deleteByMainIdAndBizType(customerPersonalizedInformationVO.getCiId(), BizType.PERSONALIZED_INFORMATION_ATTACHMENT);
        return customerPersonalizedInformationMapper.deleteById(id,updateBy);
    }

    public Optional<CustomerPersonalizedInformationVO> getByCiId(Long ciId) {
        var list = getBaseMapper().getByCiId(ciId);
        if (list != null && list.size() > 0) {
            CustomerPersonalizedInformationVO customerPersonalizedInformationVO = list.get(0);
            List<CommonBizFile> shareholderFamilyRelationshipFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.SHAREHOLDER_FAMILY_RELATIONSHIP);
            List<CommonBizFile> personalizedInformationAttachmentFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.PERSONALIZED_INFORMATION_ATTACHMENT);
            customerPersonalizedInformationVO.setShareholderFamilyRelationshipFileList(shareholderFamilyRelationshipFileList);
            customerPersonalizedInformationVO.setPersonalizedInformationAttachmentFileList(personalizedInformationAttachmentFileList);
            return Optional.ofNullable(list.get(0)) ;
        }else {
            return Optional.empty();
        }
    }

    public Optional<CustomerPersonalizedInformationVO> getByCiId1(Long ciId) {
        var list  = getBaseMapper().getByCiId(ciId);
        if (list != null && list.size() > 0) {
            return Optional.ofNullable(list.get(0)) ;
        }else {
            return Optional.empty();
        }
    }
}
