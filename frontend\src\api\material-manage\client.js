/*
 * @Description:
 * @Author: thb
 * @Date: 2023-08-16 14:49:21
 * @LastEditTime: 2023-11-23 10:55:32
 * @LastEditors: thb
 */
import request from '@/utils/request'
// 获取跟进客户列表
export const getFollowedClientList = params => {
  params.createTime = undefined
  params.lastFollowTime = undefined
  params.becomeTime = undefined
  params.lastModifiedTime = undefined
  return request({
    url: '/cusCustomerOrClue/allCustomerList',
    method: 'get',
    params
  })
}
// 查询客户管理列表
export const getClientList = params => {
  params.createTime = undefined
  params.lastFollowTime = undefined
  params.becomeTime = undefined
  params.lastModifiedTime = undefined
  return request({
    url: '/cusCustomerOrClue/customerList',
    method: 'get',
    params
  })
}

// 获取客户详情
export const getClientDetail = id => {
  return request({
    url: '/cusCustomerOrClue/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 获取联系人列表详情
export const getClientContactList = ccId => {
  return request({
    url: '/cusCcContact/listByCcId',
    method: 'get',
    params: {
      ccId
    }
  })
}

// 批量编辑联系人
export const saveBatchClientContactList = data => {
  return request({
    url: '/cusCcContact/saveBatch',
    method: 'post',
    data
  })
}

// resolved: 移除证书管理相关的API调用
// 编辑办证资料
// export const editCertificationFiles = data => {
//   return request({
//     url: '/cusCustomerOrClue/updateOtherInfo',
//     method: 'post',
//     data
//   })
// }

// 查询办证资料
// export const getCertificationFiles = id => {
//   return request({
//     url: '/cusCustomerOrClue/getOtherInfo',
//     method: 'get',
//     params: {
//       id
//     }
//   })
// }
// 新增或者编辑商机
export const saveOrUpdateClientBusiness = data => {
  return request({
    url: '/cusCcBusiness/saveOrUpdate',
    method: 'post',
    data
  })
}

// 商机列表
export const getClientBusinessList = params => {
  return request({
    url: '/cusCcBusiness/list',
    method: 'get',
    params
  })
}

// 通过ccId获得商机详情
export const getClientBusinessListById = ccId => {
  return request({
    url: '/cusCcBusiness/listByCcId',
    method: 'get',
    params: {
      ccId
    }
  })
}
// 商机详情
export const getClientBusinessDetailById = id => {
  return request({
    url: '/cusCcBusiness/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 商机的操作记录查询
export const getBusinessListById = ccId => {
  return request({
    url: '/cusCcRecord/businessListByCcId',
    method: 'get',
    params: {
      ccId
    }
  })
}

// 商机的跟进记录列表查询
export const getBusinessRecordList = params => {
  return request({
    url: '/cusCcFollow/listBusiness',
    method: 'get',
    params
  })
}
// 商机的跟进记录新增
export const saveBusinessRecord = data => {
  return request({
    url: '/cusCcFollow/saveBusiness',
    method: 'post',
    data
  })
}

// 删除商机
export const deleteBusiness = id => {
  return request({
    url: '/cusCcBusiness/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
// 客户建档
export const establishCustomerFromClient = data => {
  return request({
    url: '/cusCustomerOrClue/establish',
    method: 'post',
    data
  })
}

// 客户建档(关联)
export const associateEstablishCustomer = data => {
  return request({
    url: '/cusCustomerOrClue/associationEstablish',
    method: 'post',
    data
  })
}

// 获取客户公海列表
export const getClientZoneList = params => {
  params.createTime = undefined
  params.lastFollowTime = undefined
  params.becomeTime = undefined
  params.lastModifiedTime = undefined
  return request({
    url: '/cusCustomerOrClue/customerInSeaList',
    method: 'get',
    params
  })
}

// 私海
// 保有量设置
export const saveOrUpdateSeaInventory = data => {
  return request({
    url: '/cusSeaInventory/saveOrUpdate',
    method: 'post',
    data
  })
}

export const getSeaInventory = () => {
  return request({
    url: '/cusSeaInventory/getDetail',
    method: 'get'
  })
}
// 关联已有客户
export const associateExistClient = data => {
  return request({
    url: '/cusCustomerOrClue/clueToCustomer',
    method: 'post',
    data
  })
}

// 标记赢单
export const markWinOrder = data => {
  return request({
    url: '/cusCcBusiness/mark',
    method: 'post',
    data
  })
}

// 跟进客户列表导出
export const cusCustomerOrClueAllCustomerListExport = query => {
  query.pageSize = 999999
  return request({
    url: '/cusCustomerOrClue/allCustomerListExport',
    method: 'post',
    data: query
  })
}

// 我的客户导出
export const cusCustomerOrClueCustomerListExport = query => {
  return request({
    url: '/cusCustomerOrClue/customerListExport',
    method: 'post',
    data: query
  })
}

// 客户公海列表导出
export const cusCustomerOrClueCustomerInSeaListExport = query => {
  return request({
    url: '/cusCustomerOrClue/customerInSeaListExport',
    method: 'post',
    data: query
  })
}
