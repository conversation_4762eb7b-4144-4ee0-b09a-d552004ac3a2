package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.entity.CusSourceChannel;

/**
 * <p>
 * 客资来源 渠道 关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface CusSourceChannelMapper extends BaseMapper<CusSourceChannel> {

    default CusSourceChannel getByMainId(Long mainId) {
        return selectOne(new LambdaQueryWrapper<CusSourceChannel>().eq(CusSourceChannel::getMainId, mainId));
    }
}
