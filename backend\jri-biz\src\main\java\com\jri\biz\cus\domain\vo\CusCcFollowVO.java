package com.jri.biz.cus.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 跟进记录视图对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCcFollowVO视图对象")
public class CusCcFollowVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}