package com.jri.biz.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel(value="完成工单请求对象")
public class OrderCompleteForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("工单id")
    @NotNull(message = "工单id不能为空")
    private Long orderId;

    @ApiModelProperty("完成反馈/回退说明")
    private String feedbackOrDescription;

    @ApiModelProperty("附件列表")
    private List<CommonBizFile> fileList;

    @ApiModelProperty("清税证明")
    private List<CommonBizFile> taxClearanceList;
}
