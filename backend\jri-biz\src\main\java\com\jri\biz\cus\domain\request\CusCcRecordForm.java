package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 操作记录 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Data
@NoArgsConstructor
@ApiModel(value="操作记录表单请求对象")
public class CusCcRecordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

}
