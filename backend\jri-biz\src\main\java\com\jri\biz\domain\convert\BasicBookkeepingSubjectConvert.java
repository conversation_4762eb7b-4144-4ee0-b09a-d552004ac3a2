package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicBookkeepingSubject;
import com.jri.biz.domain.request.BasicBookkeepingSubjectForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 日记账科目对象转换
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Mapper
public interface BasicBookkeepingSubjectConvert {
    BasicBookkeepingSubjectConvert INSTANCE = Mappers.getMapper(BasicBookkeepingSubjectConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BasicBookkeepingSubject convert(BasicBookkeepingSubjectForm form);

}