package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusTag;
import com.jri.biz.cus.domain.request.CusTagForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 标签对象转换
 *
 * <AUTHOR>
 * @since 2023-08-09
 */

@Mapper
public interface CusTagConvert {
    CusTagConvert INSTANCE = Mappers.getMapper(CusTagConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusTag convert(CusTagForm form);

}