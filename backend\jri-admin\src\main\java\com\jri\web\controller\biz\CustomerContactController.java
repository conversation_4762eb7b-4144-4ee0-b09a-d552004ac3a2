package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerContactForm;
import com.jri.biz.domain.request.CustomerContactQuery;
import com.jri.biz.domain.vo.CustomerContactListVO;
import com.jri.biz.domain.vo.CustomerContactVO;
import com.jri.biz.service.CustomerContactService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 客户联系人 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerContact")
@Api(tags = "客户联系人")
public class CustomerContactController {
    @Resource
    private CustomerContactService customerContactService;

    @GetMapping("/list")
        @ApiOperation("列表查询")
        public R<IPage<CustomerContactListVO>> listPage(CustomerContactQuery query) {
        return R.ok(customerContactService.listPage(query));
    }


    @GetMapping("/getById")
        @ApiOperation("详情")
        public R<CustomerContactVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerContactService.getDetailById(id));
    }

    @GetMapping("/getByCiId")
    @ApiOperation("详情")
    public R<List<CustomerContactVO>> getDetailByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerContactService.getDetailByCiId(ciId));
    }

    @PostMapping("/saveOrUpdate")
        @ApiOperation("保存数据")
        public R<Long> saveOrUpdate(@RequestBody @Valid CustomerContactForm form) {
        var id = customerContactService.saveOrUpdate(form);
        return R.ok(id);
    }
    @PostMapping("/saveBatch")
    @ApiOperation("保存数据")
    public R<Boolean> saveBatch(@RequestBody @Valid List<CustomerContactForm> form) {
        if(form.isEmpty()){
            return R.fail("联系人集合不能为空");
        }
        var b = customerContactService.saveBatch(form);
        return R.ok(b);
    }

    @PutMapping("/update")
        @ApiOperation("更新数据")
        public R<Boolean> update(@RequestBody @Valid CustomerContactForm form) {
        return R.ok(customerContactService.update(form));
    }

    @DeleteMapping("/delete")
        @ApiOperation("根据id删除")
        public R<Boolean> deleteById(Long id) {
        return R.ok(customerContactService.deleteById(id));
    }
}

