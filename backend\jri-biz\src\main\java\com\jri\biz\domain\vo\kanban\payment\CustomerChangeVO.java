package com.jri.biz.domain.vo.kanban.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/9/4 13:49
 */
@Getter
@Setter
@ApiModel(value = "CustomerChangeVO", description = "客户数量变化")
public class CustomerChangeVO {

    @ApiModelProperty("存量户数")
    private Long existing;

    @ApiModelProperty("注销户数")
    private Long cancelled;

    @ApiModelProperty("流失户数")
    private Long churned;

    @ApiModelProperty("迁移户数")
    private Long migrated;

}
