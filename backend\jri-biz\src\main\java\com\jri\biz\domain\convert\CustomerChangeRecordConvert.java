package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerChangeRecord;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客户信息修改记录对象转换
 *
 * <AUTHOR>
 * @since 2023-07-14
 */

@Mapper
public interface CustomerChangeRecordConvert {
    CustomerChangeRecordConvert INSTANCE = Mappers.getMapper(CustomerChangeRecordConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerChangeRecord convert(CustomerChangeRecordForm form);

}