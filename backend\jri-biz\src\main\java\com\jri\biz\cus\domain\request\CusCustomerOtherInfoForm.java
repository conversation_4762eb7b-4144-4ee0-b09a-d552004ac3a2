package com.jri.biz.cus.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel(value="客户附加信息表单请求对象")
public class CusCustomerOtherInfoForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("法人身份证正面")
    private String legalIdCardFront;

    @ApiModelProperty("法人身份证反面")
    private String legalIdCardBack;

    @ApiModelProperty("法人联系方式")
    private String legalPhone;

    @ApiModelProperty("监事身份证正面")
    private String supervisorIdCardFront;

    @ApiModelProperty("监事身份证反面")
    private String supervisorIdCardBack;

    @ApiModelProperty("监事联系方式")
    private String supervisorPhone;

    @ApiModelProperty("股份比例")
    private String share;

    @ApiModelProperty("其它附件")
    private List<CommonBizFile> otherDocumentFileList;

    @ApiModelProperty("法人身份证附件")
    private List<CommonBizFile> legalIdentityFileList;

    @ApiModelProperty("监事身份证附件")
    private List<CommonBizFile> supervisorIdentityFileList;

    @ApiModelProperty("股东信息列表")
    private List<CusCcShareholderInfoForm> shareholderInfoList;
}
