package com.jri.biz.domain.vo.job;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视图列表对象
 *
 * <AUTHOR>
 * @since 2023-07-25
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="ContractAlterJobListVO视图列表对象")
public class ContractAlertJobListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;



    @ApiModelProperty("合同预警id")
    private Long contractAlertId;

    @ApiModelProperty("配置名称")
    private String name;

    @ApiModelProperty("预警时间")
    private Integer alertDate;

    @ApiModelProperty("状态0正常 1停用")
    private String alertStatus;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("通知方式 0-站内消息 1-短信")
    private String notificationMethod;

    @ApiModelProperty("通知发送时间")
    private String sendTime;

    @ApiModelProperty("消息模板")
    private String messageTemplate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;


}
