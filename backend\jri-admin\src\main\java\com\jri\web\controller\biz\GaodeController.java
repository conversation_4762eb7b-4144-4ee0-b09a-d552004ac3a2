package com.jri.web.controller.biz;

import com.alibaba.fastjson2.JSONObject;
import com.jri.biz.domain.request.GaodeGeoCodeForm;
import com.jri.biz.service.GaodeService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Auther: wyt
 * @Date: 2023/5/30
 */
@RestController
@RequestMapping(value = "/gaode")
@Api(value = "高德", tags = "高德")
public class GaodeController {

    @Resource
    private GaodeService gaodeService;

    @PostMapping("/geoCode")
    @ApiOperation("逆地理编码 接口格式参考 https://lbs.amap.com/api/webservice/guide/api/georegeo")
    public R<JSONObject> geoCode(@RequestBody GaodeGeoCodeForm form) {
        var res = gaodeService.geoCode(form);
        return R.ok(res);
    }

    @GetMapping("/search")
    @ApiOperation("搜索 接口格式参考 https://lbs.amap.com/api/webservice/guide/api/newpoisearch")
    public R<JSONObject> search(String keyWord) {
        var res = gaodeService.search(keyWord);
        return R.ok(res);
    }
}