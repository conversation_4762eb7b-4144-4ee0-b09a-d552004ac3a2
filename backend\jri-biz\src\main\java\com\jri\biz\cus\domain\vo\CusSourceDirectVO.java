package com.jri.biz.cus.domain.vo;

import com.jri.biz.cus.domain.entity.CusSourceDirect;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.util.List;

/**
 * 客资来源 直投 关联视图对象
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusSourceDirectVO视图对象")
public class CusSourceDirectVO extends CusSourceVO {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "充值列表")
    private List<CusSourceDirect> chargeList;

}