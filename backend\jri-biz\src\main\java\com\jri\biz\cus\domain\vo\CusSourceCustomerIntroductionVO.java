package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 客资来源 客户介绍 关联视图对象
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusSourceCustomerIntroductionVO视图对象")
public class CusSourceCustomerIntroductionVO extends CusSourceVO {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户编号")
    private String customerNo;
}