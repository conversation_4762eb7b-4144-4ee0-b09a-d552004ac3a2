package com.jri.biz.domain.request.suggestion;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 企业用户建议 表单请求对象
 *
 * <AUTHOR>
 * @since 2024-01-25
 */

@Data
@NoArgsConstructor
@ApiModel(value="企业用户建议表单请求对象")
public class UserSuggestionAddForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "客户id不能为空")
    @ApiModelProperty("客户id")
    private Long customerId;

    @NotBlank(message = "建议内容不能为空")
    @ApiModelProperty("建议内容")
    private String suggestionData;

    @ApiModelProperty("附件列表")
    private List<CommonBizFile> fileList;

}
