package com.jri.biz.constants.risk;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 审账整改常量
 *
 * <AUTHOR>
 * @since 2023/11/29 9:49
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RiskCustomerAuditStatusEnum {

    PASS("pass", "通过"),
    NOT_PASS("not_pass", "不通过");

    private String code;

    private String value;

    public static String searchValueByCode(String code) {
        for (var eventNodeEnum : RiskCustomerAuditStatusEnum.values()) {
            if (eventNodeEnum.getCode().equals(code)) {
                return eventNodeEnum.getValue();
            }
        }
        return null;
    }
}
