package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.OrderTypeForm;
import com.jri.biz.domain.request.OrderTypeQuery;
import com.jri.biz.domain.vo.OrderTypeListVO;
import com.jri.biz.domain.vo.OrderTypeVO;
import com.jri.biz.service.OrderTypeService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 工单类型 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Validated
@RestController
@RequestMapping("/orderType")
@Api(tags = "工单类型")
public class OrderTypeController {
    @Resource
    private OrderTypeService orderTypeService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<OrderTypeListVO>> listPage(OrderTypeQuery query) {
        return R.ok(orderTypeService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<OrderTypeVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(orderTypeService.getDetailById(id));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid OrderTypeForm form) {
        orderTypeService.add(form);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(orderTypeService.deleteById(id));
    }

    @GetMapping("/getList")
    @ApiOperation("下拉列表查询")
    public R<List<OrderTypeListVO>> getList() {
        return R.ok(orderTypeService.getList());
    }

    @PostMapping("/setStatus")
    @ApiOperation("状态设置")
    public R<Void> setStatus(@RequestParam("id") Long id) {
        orderTypeService.setStatus(id);
        return R.ok();
    }

    @GetMapping("/tree")
    @ApiOperation("树结构查询")
    public R<List<OrderTypeListVO>> tree(OrderTypeQuery query) {
        return R.ok(orderTypeService.tree(query));
    }
}

