package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 工单记录视图对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="OrderRecordVO视图对象")
public class OrderRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}