package com.jri.biz.cus.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 客资来源 直投 关联 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@ApiModel(value = "客资来源 直投 关联表单请求对象")
public class CusSourceDirectForm extends CusSourceBaseForm {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主表id")
    private Long mainId;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("充值时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate chargeTime;
}
