package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 工单记录 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Data
@NoArgsConstructor
@ApiModel(value="工单记录表单请求对象")
public class OrderRecordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 操作记录名称
     */
    private String recordName;

    /**
     * 指派给
     */
    private Long executor;

    /**
     * 工单id
     */
    private Long orderId;

    /**
     * 备注
     */
    private String remark;

    @ApiModelProperty("创建者")
    private String createBy;
}
