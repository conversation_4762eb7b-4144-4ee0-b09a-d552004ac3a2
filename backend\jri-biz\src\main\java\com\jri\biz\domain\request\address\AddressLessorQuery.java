package com.jri.biz.domain.request.address;

import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 出租人查询类
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="出租人查询对象")
public class AddressLessorQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("出租人姓名")
    private String lessorName;

}
