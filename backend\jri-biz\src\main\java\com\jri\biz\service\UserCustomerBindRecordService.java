package com.jri.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.domain.entity.UserCustomerBindRecord;
import com.jri.biz.domain.request.UserCustomerBindRecordForm;
import com.jri.biz.domain.request.UserCustomerBindRecordQuery;
import com.jri.biz.domain.vo.UserCustomerBindRecordListVO;
import com.jri.biz.mapper.UserCustomerBindRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.jri.biz.constants.UserCustomerBindConstant.BIND_STATUS_BIND;
import static com.jri.biz.constants.UserCustomerBindConstant.BIND_STATUS_UNBIND;

/**
 * <p>
 * 用户客户绑定记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
public class UserCustomerBindRecordService extends ServiceImpl<UserCustomerBindRecordMapper, UserCustomerBindRecord> {

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<UserCustomerBindRecordListVO> listPage(UserCustomerBindRecordQuery query) {
        var page = new Page<UserCustomerBindRecordListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 保存
     *
     * @param form 表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(UserCustomerBindRecordForm form) {
        Long userId = form.getUserId();
        List<Long> customerIdList = form.getCustomerIdList();
        getBaseMapper().addOrUpdateByCustomerIdAndUserId(customerIdList, userId, BIND_STATUS_BIND);
    }

    /**
     * 解绑
     * @param idList id列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void unbind(List<Long> idList) {
        getBaseMapper().batchUnbind(idList, BIND_STATUS_UNBIND);
    }

}
