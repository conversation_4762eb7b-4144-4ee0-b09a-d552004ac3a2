package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BusinessProductActivity;
import com.jri.biz.domain.request.BusinessProductActivityForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 产品活动价对象转换
 *
 * <AUTHOR>
 * @since 2023-11-02
 */

@Mapper
public interface BusinessProductActivityConvert {
    BusinessProductActivityConvert INSTANCE = Mappers.getMapper(BusinessProductActivityConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BusinessProductActivity convert(BusinessProductActivityForm form);

}