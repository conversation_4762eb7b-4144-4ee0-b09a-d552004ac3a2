package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 公海配置查询类
 *
 * <AUTHOR>
 * @since 2023-08-09
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="公海配置查询对象")
public class CusSeaQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("公海类型0-线索公海1-客户公海")
    private String type;

    @ApiModelProperty("公海名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;
}
