package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 保有量设置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_sea_inventory")
@ApiModel(value = "CusSeaInventory对象", description = "保有量设置")
public class CusSeaInventory implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 线索掉保时长
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer clueDuration;

    /**
     * 线索回收公海id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long clueSeaId;

    /**
     * 线索回收提醒提前天数
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer clueRecovery;

    /**
     * 线索员工私海上限
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer clueStaffNum;

    /**
     * 线索管理员私海上限
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer clueAdminNum;

    /**
     * 线索员工私海上限状态0-停用1-启用
     */
    private String clueStaffStatus;

    /**
     * 线索管理员私海上限状态0-停用1-启用
     */
    private String clueAdminStatus;

    /**
     * 客户掉保时长
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer cusDuration;

    /**
     * 客户回收公海id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long cusSeaId;

    /**
     * 客户回收提醒提前天数
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer cusRecovery;

    /**
     * 客户员工私海上限
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer cusStaffNum;

    /**
     * 客户管理员私海上限
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer cusAdminNum;

    /**
     * 客户员工私海上限状态0-停用1-启用
     */
    private String cusStaffStatus;

    /**
     * 客户管理员私海上限状态0-停用1-启用
     */
    private String cusAdminStatus;


}
