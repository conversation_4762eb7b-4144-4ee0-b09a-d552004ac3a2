package com.jri.biz.domain.request.suggestion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;


/**
 * 企业用户建议 表单请求对象
 *
 * <AUTHOR>
 * @since 2024-01-25
 */

@Data
@NoArgsConstructor
@ApiModel(value = "企业用户建议评价请求对象")
public class UserSuggestionStarForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空")
    @ApiModelProperty("id")
    private Long id;

    @NotNull(message = "用户评价不能为空")
    @Max(value = 5, message = "用户评价不能大于5")
    @Min(value = 1, message = "用户评价不能小于1")
    @ApiModelProperty("用户评价")
    private Integer star;

}
