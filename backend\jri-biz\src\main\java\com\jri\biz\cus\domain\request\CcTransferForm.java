package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value="转移线索客户表单请求对象")
public class CcTransferForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索客户id")
    private Long id;

    @ApiModelProperty("类型0-线索转移1-客户转移")
    private String type;

    @ApiModelProperty("公海id")
    private Long seaId;
}
