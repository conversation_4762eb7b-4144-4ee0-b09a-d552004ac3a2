package com.jri.biz.cus.delay;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/11/23
 */

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RedisDelayQueueEnum {

   RECOVERY_REMIND("RECOVERY_REMIND", "回收公海提醒队列", "remindHandler"),
   RECOVERY_TO_SEA("RECOVERY_TO_SEA", "回收公海队列", "recoveryToSeaHandler");

   /**
    * 延迟队列 Redis Key
    */
   private String code;

   /**
    * 中文描述
    */
   private String name;

   /**
    * 延迟队列具体业务实现的 Bean
    * 可通过 Spring 的上下文获取
    */
   private String beanId;
}
