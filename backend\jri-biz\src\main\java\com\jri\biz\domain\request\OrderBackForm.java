package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value="回退工单请求对象")
public class OrderBackForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("工单id")
    @NotNull(message = "工单id不能为空")
    private Long orderId;

    @ApiModelProperty("完成反馈/回退说明")
    private String feedbackOrDescription;
}
