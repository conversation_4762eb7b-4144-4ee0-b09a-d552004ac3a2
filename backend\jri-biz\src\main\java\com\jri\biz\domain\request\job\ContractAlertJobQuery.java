package com.jri.biz.domain.request.job;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-07-25
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="预警查询对象")
public class ContractAlertJobQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("配置名称")
    private String name;

    @ApiModelProperty("状态0正常 1停用")
    private String alertStatus;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("通知方式 0-站内消息 1-短信")
    private String notificationMethod;
}
