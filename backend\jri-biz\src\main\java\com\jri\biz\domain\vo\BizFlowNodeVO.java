package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-06-16
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BizFlowNodeVO视图对象")
public class BizFlowNodeVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}