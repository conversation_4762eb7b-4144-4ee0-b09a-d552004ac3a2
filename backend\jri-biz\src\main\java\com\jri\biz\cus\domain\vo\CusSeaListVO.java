package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 公海配置视图列表对象
 *
 * <AUTHOR>
 * @since 2023-08-09
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusSeaListVO视图列表对象")
public class CusSeaListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("公海类型0-线索公海1-客户公海")
    private String type;

    @ApiModelProperty("公海名称")
    private String name;

    @ApiModelProperty("公海管理员id")
    private Long manageId;

    @ApiModelProperty("公海管理员名称")
    private String manageName;

    @ApiModelProperty("掉保时长")
    private Integer duration;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("资源分配规则0-员工领取1-仅管理员分配2-员工领取+管理员分配")
    private String rule;

    @ApiModelProperty("状态0-停用1-正常")
    private String status;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("回收提醒提前天数")
    private Integer recovery;

    @ApiModelProperty("部门id列表")
    private List<Long> deptIds;
}