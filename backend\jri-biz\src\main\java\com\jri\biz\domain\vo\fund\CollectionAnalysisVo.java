package com.jri.biz.domain.vo.fund;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "TurnoverOfMonthDetailVo视图对象", description = "回款分析vo")
public class CollectionAnalysisVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户成功用户id")
    private Long customerSuccessUserId;

    @ApiModelProperty(value = "客户成功")
    private String customerSuccess;

    @ApiModelProperty(value = "所属部门id")
    private Long deptId;

    @ApiModelProperty(value = "所属部门")
    private String deptName;

    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    @ApiModelProperty(value = "分析月份")
    private LocalDate month;

    @ApiModelProperty(value = "当月营业额")
    private BigDecimal monthTurnover = BigDecimal.ZERO;

    @ApiModelProperty(value = "记账当月营业额")
    private BigDecimal bookkeepingMonthTurnover = BigDecimal.ZERO;

    @ApiModelProperty(value = "地址当月营业额")
    private BigDecimal addressMonthTurnover = BigDecimal.ZERO;

    @ApiModelProperty(value = "当月维护企业数量")
    private Long monthMaintainCustomer;

    @ApiModelProperty(value = "当月应收")
    private BigDecimal allPaymentAmount = BigDecimal.ZERO;

    /**
     * 关联企业中完全收回当月到期账单的企业数（付了一半的不算）
     */
    @ApiModelProperty(value = "当月到期账单结清企业数量")
    private Long monthPaymentClearCustomer;

    @ApiModelProperty(value = "当月实收")
    private BigDecimal allReceiptAmount = BigDecimal.ZERO;

    /**
     * 前三个月到期的账单中没收的钱
     */
    @ApiModelProperty(value = "当月欠费")
    private BigDecimal monthDebt;

    /**
     * 关联企业中前三个月到期的账单有钱没收（完）企业数
     */
    @ApiModelProperty(value = "当月到期账单未结清企业数量")
    private Long monthPaymentDebtCustomer;

    @ApiModelProperty(value = "欠费金额")
    private BigDecimal arrearageAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "欠费实收")
    private BigDecimal receiveArrearageAmount = BigDecimal.ZERO;

}
