package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BorrowRecord;
import com.jri.biz.domain.request.BorrowRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 借阅记录对象转换
 *
 * <AUTHOR>
 * @since 2023-07-11
 */

@Mapper
public interface BorrowRecordConvert {
    BorrowRecordConvert INSTANCE = Mappers.getMapper(BorrowRecordConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BorrowRecord convert(BorrowRecordForm form);

}