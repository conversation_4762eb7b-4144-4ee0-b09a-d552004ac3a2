package com.jri.web.controller.common;

import cn.hutool.core.util.ObjectUtil;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.OSSObject;
import com.jri.biz.domain.request.DownloadForm;
import com.jri.biz.service.CommonBizFileService;
import com.jri.biz.utils.PdfboxUtil;
import com.jri.common.config.JRIConfig;
import com.jri.common.core.domain.AjaxResult;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.core.domain.R;
import com.jri.common.utils.AliyunOSSUtil;
import com.jri.common.utils.SecurityUtils;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.file.FileUploadUtils;
import com.jri.common.utils.file.FileUtils;
import com.jri.common.utils.uuid.Seq;
import com.jri.framework.config.ServerConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.jri.common.constant.Constants.RESOURCE_PREFIX;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
@Api(value = "附件", tags = "附件")
public class CommonController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private CommonBizFileService commonBizFileService;

    @Resource
    private AliyunOSSUtil aliyunOSSUtil;

    private static final String FILE_DELIMETER = ",";

    @Value("${oss.endpoint}")
    String endpoint;
    @Value("${oss.accessKeyId}")
    String accessKeyId;
    @Value("${oss.accessKeySecret}")
    String accessKeySecret;
    @Value("${oss.bucketName}")
    String bucketName;
    @Value("${oss.wwendpoint}")
    String wwendpoint;
//    @Value("${preurl}")
//    String preurl;

    /**
     * 通用下载请求
     *
     * @param form 表单
     */
    @PostMapping("/download")
    @ApiOperation(value = "通用下载请求")
    public void fileDownload(@RequestBody DownloadForm form, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.checkAllowDownload(form.getFileName())) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", form.getFileName()));
            }
            String realFileName = System.currentTimeMillis() + form.getFileName().substring(form.getFileName().indexOf("_") + 1);
            String filePath = JRIConfig.getProfile() + form.getFileName();
            filePath = filePath.replace(RESOURCE_PREFIX, "");

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (form.getDelete()) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/upload")
    @ApiOperation(value = "通用上传请求（单个）")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = JRIConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = fileName;
            // 获取当前时间
            Date currentDate = new Date();
            // 定义日期格式
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 格式化日期
            String uploadTime = dateFormat.format(currentDate);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("uploadTime", uploadTime);
            ajax.put("uploadBy", SecurityUtils.getLoginUser().getUser().getNickName());
            ajax.put("uploadSize", file.getSize() / 1024);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/uploadByType")
    @ApiOperation(value = "通用上传请求（单个）")
    public AjaxResult uploadFile(MultipartFile file, String bizType, Long mainId) throws Exception {
        try {
            // 上传文件路径
            String filePath = JRIConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            // 获取当前时间
            Date currentDate = new Date();
            // 定义日期格式
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 格式化日期
            String uploadTime = dateFormat.format(currentDate);
            String uploadBy = SecurityUtils.getLoginUser().getUser().getNickName();
            Long uploadSize = file.getSize() / 1024;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("uploadTime", uploadTime);
            ajax.put("uploadBy", uploadBy);
            ajax.put("uploadSize", uploadSize);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());

            //保存附件信息
            CommonBizFile commonBizFile = new CommonBizFile(null, mainId, fileName, null, url, bizType,
                    uploadBy, LocalDateTime.now(), uploadSize, false);
            if (ObjectUtil.isNotEmpty(commonBizFile)) {
                commonBizFileService.save(commonBizFile);
            }
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    @ApiOperation(value = "通用上传请求（多个）")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception {
        try {
            // 上传文件路径
            String filePath = JRIConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files) {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    @ApiOperation(value = "本地资源通用下载")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = JRIConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }


    @DeleteMapping("/delete/resource")
    public R<Void> deleteByMainIdAndBizType(Long bizId, String bizType) {
        commonBizFileService.deleteByMainIdAndBizType(bizId, bizType);
        return R.ok();
    }

    @GetMapping("/select/resource")
    public R<List<CommonBizFile>> selectByMainIdAndBizType(Long bizId, String bizType) {
        return R.ok(commonBizFileService.selectByMainIdAndBizType(bizId, bizType));
    }

    @PostMapping("/upload2Oss")
    @ApiOperation("上传请求(OSS)")
    public AjaxResult uploadOssFile(MultipartFile file) throws Exception {
        // 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。
        String originalFilename = file.getOriginalFilename();
        originalFilename = Seq.getId(Seq.uploadSeqType) + "_" + originalFilename;
        String objectName = Objects.requireNonNull(originalFilename);

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
//        String url = null;
        try {
            ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(file.getBytes()));
//            url = "https://oss.xiangfutalent.com/".concat(objectName);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        // 获取当前时间
        Date currentDate = new Date();
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 格式化日期
        String uploadTime = dateFormat.format(currentDate);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("url", objectName);
        ajax.put("uploadTime", uploadTime);
        ajax.put("uploadBy", SecurityUtils.getLoginUser().getUser().getNickName());
        ajax.put("uploadSize", file.getSize() / 1024);
        ajax.put("fileName", objectName);
        ajax.put("newFileName", objectName);
        ajax.put("originalFilename", file.getOriginalFilename());
        return ajax;
    }

    /**
     * 通用下载请求(弃用)
     *
     * @param form 表单
     */
    @PostMapping("/download2Oss")
    @ApiOperation(value = "通用下载请求(OSS)")
    public void download2Oss(@RequestBody DownloadForm form, HttpServletResponse response, HttpServletRequest request) {

        // 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。
        String objectName = form.getFileName();

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // ossObject包含文件所在的存储空间名称、文件名称、文件元信息以及一个输入流。
            OSSObject ossObject = ossClient.getObject(bucketName, objectName);

            // 读取文件内容。
            System.out.println("Object content:");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, objectName);
            ServletOutputStream outputStream = response.getOutputStream();
            System.out.println("***************写入开始****************");
            IOUtils.copy(ossObject.getObjectContent(), outputStream);
            System.out.println("***************写入结束****************");
            // ossObject对象使用完毕后必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
            ossObject.close();
            outputStream.close();

        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (Throwable ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    @GetMapping("/getUrlOss")
    @ApiOperation(value = "获取访问附件url")
    public R<URL> getUrlOss(@RequestParam("objectName") String objectName) {
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(wwendpoint, accessKeyId, accessKeySecret);

        try {
            // 设置签名URL过期时间，单位为毫秒。目前设置10分钟过期
            Date expiration = new Date(new Date().getTime() + 600 * 1000L);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
            System.out.println(url);
            return R.ok(url);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    @GetMapping("/getOssPdfToImg")
    @ApiOperation(value = "获取oss上pdf转化为图片")
    public R<List<String>> getOssPdfToImg(@RequestParam("objectName") String objectName) {
        byte[] bytes = aliyunOSSUtil.getObject(objectName);
        List<String> imgBase64List = PdfboxUtil.pdfToImg(bytes);
        return R.ok(imgBase64List);
    }


}
