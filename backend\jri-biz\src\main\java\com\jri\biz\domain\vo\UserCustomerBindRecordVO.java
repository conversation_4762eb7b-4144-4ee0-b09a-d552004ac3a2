package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户客户绑定记录视图对象
 *
 * <AUTHOR>
 * @since 2024-01-24
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="UserCustomerBindRecordVO视图对象")
public class UserCustomerBindRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}