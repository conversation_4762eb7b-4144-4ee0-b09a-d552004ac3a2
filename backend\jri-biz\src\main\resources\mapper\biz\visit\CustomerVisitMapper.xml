<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.visit.CustomerVisitMapper">

    <sql id="getList">
        select
        visit.id,
        visit.plan_name,
        visit.customer_id,
        visit.plan_visit_date,
        visit.actual_plan_date,
        visit.visitor_id,
        visit.plan_visit_method,
        visit.actual_visit_method,
        if(visit.status="1",visit.actual_plan_date,visit.complete_time) as complete_time,
        visit.status,
        visit.visit_purpose,
        visit.visit_feedback,
        visit.cancel_reason,
        visit.create_by,
        visit.create_time,
        visit.update_by,
        visit.update_time,
        visit.is_deleted,
        information.customer_no,information.customer_name,user.nick_name as visitor,
        contact.phone phone
        from customer_visit visit
        left join customer_information information on information.customer_id=visit.customer_id and
        information.discard=0 and information.is_deleted=0
        left join sys_user user on user.user_id=visit.visitor_id
        left join sys_dept d on user.dept_id = d.dept_id
        left join customer_contact contact on contact.id = information.contract_main_id
        where visit.is_deleted=0
        <if test="query.planName!=null and query.planName!=''">
            and visit.plan_name like concat('%', #{query.planName}, '%')
        </if>
        <if test="query.customerName!=null and query.customerName!='' ">
            and information.customer_name like concat('%', #{query.customerName}, '%')
        </if>
        <if test="query.customerNo!=null and query.customerNo!=''">
            and information.customer_no like concat('%', #{query.customerNo}, '%')
        </if>
        <if test="query.status!=null and query.status!=''">
            and visit.status=#{query.status}
        </if>
        <if test="query.planVisitDate!=null">
            and visit.plan_visit_date = #{query.planVisitDate}
        </if>
        <if test="query.keyword!=null and query.keyword!=''">
            and (information.customer_name like concat('%',#{query.keyword},'%') or
            visit.plan_name like concat('%',#{query.keyword},'%') or
            user.nick_name like concat('%',#{query.keyword},'%')
            )
        </if>
        <if test="query.visitorId != null">
            and visit.visitor_id = #{query.visitorId}
        </if>
        <if test="query.planVisitDateStart != null and query.planVisitDateEnd != null">
            and visit.plan_visit_date between #{query.planVisitDateStart} and #{query.planVisitDateEnd}
        </if>
        <if test="query.idList != null and query.idList.size() > 0">
            <foreach collection="query.idList" item="id" open="and visit.id in (" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="query.actualPlanDateStart != null and query.actualPlanDateEnd != null">
            and visit.actual_plan_date between #{query.actualPlanDateStart} and #{query.actualPlanDateEnd}
        </if>
        <if test="query.planVisitMethod != null and query.planVisitMethod!=''">
            and visit.plan_visit_method = #{query.planVisitMethod}
        </if>
    </sql>

    <select id="listPage" resultType="com.jri.biz.domain.vo.visit.CustomerVisitListVO">
        <include refid="getList"/>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        <choose>
            <when test="query.orderByColumn!=null and query.orderByColumn!=''">
                order by visit.${query.orderByColumn}
                <if test="query.isAsc!=null and query.isAsc!=''">
                    ${query.isAsc}
                </if>
            </when>
            <when test='query.orderByColumn == "no_limit"'>

            </when>
            <otherwise>
                order by visit.plan_visit_date
            </otherwise>
        </choose>

    </select>
    <select id="getById" resultType="com.jri.biz.domain.vo.visit.CustomerVisitListVO">
        select visit.*,information.customer_no,information.customer_name,user.nick_name as visitor
        from customer_visit visit
                 left join customer_information information on information.customer_id=visit.customer_id and
                                                               information.discard=0 and information.is_deleted=0
                 left join sys_user user on user.user_id=visit.visitor_id
        where visit.is_deleted=0
        and visit.id=#{id}
    </select>
    <select id="getList" resultType="com.jri.biz.domain.vo.visit.CustomerVisitListVO">
        <include refid="getList"/>
        ${query.params.dataScope}
        order by visit.plan_visit_date
    </select>

</mapper>
