package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcShareholderInfo;
import com.jri.biz.cus.domain.request.CusCcShareholderInfoForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 股东信息对象转换
 *
 * <AUTHOR>
 * @since 2023-11-08
 */

@Mapper
public interface CusCcShareholderInfoConvert {
    CusCcShareholderInfoConvert INSTANCE = Mappers.getMapper(CusCcShareholderInfoConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCcShareholderInfo convert(CusCcShareholderInfoForm form);

}