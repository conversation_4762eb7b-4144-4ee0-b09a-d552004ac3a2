package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerBankCommon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerBankCommonListVO;
import com.jri.biz.domain.vo.CustomerBankCommonVO;
import com.jri.biz.domain.request.CustomerBankCommonQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 一般户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
public interface CustomerBankCommonMapper extends BaseMapper<CustomerBankCommon> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerBankCommonListVO> listPage(@Param("query") CustomerBankCommonQuery query, Page<CustomerBankCommonListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerBankCommonVO getDetailById(@Param("id") Long id);

    void removeByBankId(@Param("bankId") Long bankId);
}
