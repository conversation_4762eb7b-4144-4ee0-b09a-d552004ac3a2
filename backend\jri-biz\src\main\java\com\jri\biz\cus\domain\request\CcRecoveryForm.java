package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value="回收公海表单请求对象")
public class CcRecoveryForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索客户id")
    private Long id;

    @ApiModelProperty("类型0-线索回收1-客户回收")
    private String type;

    @ApiModelProperty("公海id")
    private Long seaId;

    @ApiModelProperty("回收原因")
    private String reason;
}
