package com.jri.biz.cus.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 客资来源 客户介绍 关联
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("cus_source_customer_introduction")
@ApiModel(value = "CusSourceCustomerIntroduction对象", description = "客资来源 客户介绍 关联")
public class CusSourceCustomerIntroduction implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "main_id", type = IdType.NONE)
    @ApiModelProperty("主表id")
    private Long mainId;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户编号")
    private String customerNo;


}
