package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Data
@NoArgsConstructor
@ApiModel(value = "合同表单请求对象")
public class CustomerContractForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("客户信息主表id")
    private Long ciId;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    @ApiModelProperty("合同编号")
    private String contractNo;

//    @ApiModelProperty("合同状态0-待审批 1-通过 2-驳回")
//    private String contractStatus;

    @ApiModelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系人电话")
    private String contactPhone;

    @ApiModelProperty("0-录入合同1-模板创建合同")
    private String type;

    @ApiModelProperty("纳税人类型")
    private String taxpayerType;

    @ApiModelProperty("网上申报0-零申报 1-非零申报")
    private String declare;

    @ApiModelProperty("起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("服务月份")
    private Integer monthNum;

    @ApiModelProperty("销售收入")
    private String salesRevenue;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("服务费")
    private BigDecimal serviceCost;

    @ApiModelProperty("其它费用")
    private BigDecimal otherCost;

    @ApiModelProperty("合同总金额(中文大写)")
    private String totalCostCn;

    @ApiModelProperty("合同总金额")
    private BigDecimal totalCost;

    @ApiModelProperty("工本费")
    private String productionCost;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("合同附件")
    private CommonBizFile file;

    @ApiModelProperty("其他补充说明")
    private String otherRemark;

    @ApiModelProperty("身份证号码")
    private String identityNumber;

    @ApiModelProperty("拟注册成立企业名称")
    private String companyName;

    @ApiModelProperty("拟注册成立企业法定代表人姓名")
    private String legalPerson;

    @ApiModelProperty("拟注册成立企业法定代表人联系电话")
    private String legalPhone;

    @ApiModelProperty("托管住所地址")
    private String custodyAddress;

    @ApiModelProperty("原合同id")
    private Long originId;

    @ApiModelProperty("业务类型0-新增合同1-变更合同")
    private String bizType = "0";

    @ApiModelProperty("变更原因")
    private String changeReason;

    @ApiModelProperty("html字符串")
    private String htmlStr;

    @ApiModelProperty("模板id")
    private Long tempId;

    @ApiModelProperty(value = "所属分公司")
    private String branchOffice;

    @ApiModelProperty("所属公司联系人")
    private String companyPerson;

    @ApiModelProperty("所属公司联系电话")
    private String companyPhone;

    @ApiModelProperty("所属公司地址")
    private String companyAddress;

    @ApiModelProperty("是否建账0-否1-是")
    private String isEstablish;

    @ApiModelProperty("账册软件费")
    private BigDecimal softwareFee;

    @ApiModelProperty("以后每年")
    private BigDecimal everyYear;

    @ApiModelProperty("办理人联系地址")
    private String contactAddress;

    @ApiModelProperty("乙方账号信息")
    private String accountNumber;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动txt")
    private String activityTxt;

    @ApiModelProperty("人事薪酬服务")
    private String payrollService;

    @ApiModelProperty("活动报价")
    private String activityQuotation;

    @ApiModelProperty("优惠时长(月)")
    private Integer activityDiscountTime;

    @ApiModelProperty("备注")
    private String activityRemark;

    @ApiModelProperty("变更科目")
    private List<String> changeSubjectList;

    @NotNull(message = "价格变动标记不能为空")
    @ApiModelProperty("价格变动标记 1表示变动 0表示未变动 默认为0")
    private Boolean priceChangeFlag = false;

    @ApiModelProperty(value = "优惠类型")
    private String discount;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty("优惠时长(月)")
    private Integer discountTime;

    @ApiModelProperty(value = "金额优惠折扣率")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "时长优惠折扣率")
    private BigDecimal discountTimeRate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "变更开始时间")
    private Date changeStartTime;

}
