package com.jri.biz.cus.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value="线索转客户(关联已有客户)表单请求对象")
public class ClueToCustomerForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索id")
    private Long clueId;

    @ApiModelProperty("客户id")
    private Long customerId;
}
