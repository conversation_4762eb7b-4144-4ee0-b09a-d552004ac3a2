package com.jri.biz.domain.vo.fund;

import com.jri.common.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
public class CollectionAnalysisExportDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Excel(name = "人员名称")
    private String userName;

    @Excel(name = "所属部门")
    private String deptName;

    @Excel(name = "分析月份", dateFormat = "yyyy-MM月")
    private LocalDate month;

    @Excel(name = "当月营业额", scale = 2)
    private BigDecimal monthTurnover = BigDecimal.ZERO;

    @Excel(name = "当月记账营业额", scale = 2)
    private BigDecimal bookkeepingMonthTurnover = BigDecimal.ZERO;

    @Excel(name = "当月地址营业额", scale = 2)
    private BigDecimal addressMonthTurnover = BigDecimal.ZERO;

    @Excel(name = "当月维护企业")
    private Long monthMaintainCustomer;

    @Excel(name = "当月应收", scale = 2)
    private BigDecimal allPaymentAmount = BigDecimal.ZERO;

    @Excel(name = "当月实收企业")
    private Long monthPaymentClearCustomer;

    @Excel(name = "当月实收", scale = 2)
    private BigDecimal allReceiptAmount;

    @Excel(name = "当月欠费企业")
    private Long monthPaymentDebtCustomer;

    @Excel(name = "当月欠费", scale = 2)
    private BigDecimal monthDebt;

    @Excel(name = "当月回款率")
    private String monthReceivePercent;

    @Excel(name = "欠费金额", scale = 2)
    private BigDecimal arrearageAmount = BigDecimal.ZERO;

    @Excel(name = "欠费实收", scale = 2)
    private BigDecimal receiveArrearageAmount = BigDecimal.ZERO;

    @Excel(name = "总回款率")
    private String allReceivePercent;

}
