package com.jri.biz.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.cus.domain.entity.CusCcBusiness;
import com.jri.biz.cus.domain.vo.CusBusinessBizVO;
import com.jri.biz.cus.mapper.CusBusinessBizMapper;
import com.jri.biz.cus.mapper.CusCcBusinessMapper;
import com.jri.biz.domain.convert.CustomerContractConvert;
import com.jri.biz.domain.entity.*;
// resolved: 移除财务管理相关的import
// import com.jri.biz.domain.entity.finance.FinancePayment;
import com.jri.biz.domain.request.*;
import com.jri.biz.domain.vo.*;
import com.jri.biz.mapper.*;
// resolved: 移除财务和证书管理相关的mapper和service import
// import com.jri.biz.mapper.finance.FinancePaymentMapper;
// import com.jri.biz.mapper.license.LicenseBizTaskMapper;
// import com.jri.biz.service.finance.ContractChangeService;
import com.jri.biz.utils.CustomerDataScopeSqlUtil;
import com.jri.biz.utils.CustomerInformationUtil;
import com.jri.common.annotation.DataScope;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.core.domain.R;
import com.jri.common.core.domain.entity.SysDept;
import com.jri.common.core.domain.entity.SysRole;
import com.jri.common.core.domain.entity.SysUser;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.OrderNoUtils;
import com.jri.common.utils.SecurityUtils;
import com.jri.common.utils.StringUtils;
import com.jri.common.utils.uuid.Seq;
import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerContractService extends ServiceImpl<CustomerContractMapper, CustomerContract> {

    @Resource
    private OrderNoUtils orderNoUtils;

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private BizFlowService bizFlowService;

    @Resource
    private BizNodeHistoryMapper bizNodeHistoryMapper;

    @Resource
    private BorrowRecordMapper borrowRecordMapper;

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    private BusinessProductMapper businessProductMapper;

    @Resource
    private BusinessTypeMapper businessTypeMapper;

    @Resource
    private BizNodeHistoryService bizNodeHistoryService;

    @Resource
    private CusCcBusinessMapper cusCcBusinessMapper;

    @Resource
    private CusBusinessBizMapper cusBusinessBizMapper;

    // resolved: 移除财务相关的service注入
    // @Resource
    // private ContractChangeService contractChangeService;

    @Resource
    private BusinessProductActivityService businessProductActivityService;

    // resolved: 移除财务和证书管理相关的mapper注入
    // @Resource
    // private FinancePaymentMapper financePaymentMapper;

    // @Resource
    // private LicenseBizTaskMapper licenseBizTaskMapper;

    @Resource
    private CustomerInformationUtil customerInformationUtil;

    @Resource
    private UserDeptService userDeptService;

    @Value("${oss.endpoint}")
    String endpoint;
    @Value("${oss.accessKeyId}")
    String accessKeyId;
    @Value("${oss.accessKeySecret}")
    String accessKeySecret;
    @Value("${oss.bucketName}")
    String bucketName;

    SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat ymdCn = new SimpleDateFormat("yyyy年MM月dd日");
    SimpleDateFormat ym = new SimpleDateFormat("yyyy-MM");

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerContractListVO> listPage(CustomerContractQuery query) {
        var page = new Page<CustomerContractListVO>(query.getPageNum(), query.getPageSize());
        IPage<CustomerContractListVO> res = getBaseMapper().listPage(query, page);
        List<CustomerContractListVO> records = res.getRecords();
        records.forEach(item -> {
            String contractType = item.getContractType();
            if ("2".equals(contractType)) {
                Date startTimeOrg = item.getStartTimeOrg();
                Date endTimeOrg = item.getEndTimeOrg();
                if (null != startTimeOrg) {
                    item.setStartTime(ymd.format(startTimeOrg));
                }
                if (null != endTimeOrg) {
                    item.setEndTime(ymd.format(endTimeOrg));
                }
            } else if ("0".equals(contractType)) {
                Date startTimeOrg = item.getStartTimeOrg();
                Date endTimeOrg = item.getEndTimeOrg();
                if (null != startTimeOrg) {
                    item.setStartTime(ym.format(startTimeOrg));
                }
                if (null != endTimeOrg) {
                    item.setEndTime(ym.format(endTimeOrg));
                }
            }
        });
        return res;
    }

    /**
     * 根据id获取详情
     *
     * @param id       id
     * @param showFlag 是否显示删除合同
     * @return 结果
     */
    public CustomerContractVO getDetailById(Long id, Boolean showFlag) {
        CustomerContractVO res = getBaseMapper().getDetailById(id, showFlag);
        if (ObjectUtil.isNull(res)) {
            throw new ServiceException("合同信息不存在或已删除");
        }
        Map<Long, SysUser> sysUserMap = new HashMap<>();
        Map<Long, SysDept> sysDeptMap = userDeptService.getSysDeptMap();
        res.setManager(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, res.getManagerUserId()));
        // 3-已终止 4-执行中 5-已到期
        String contractStatus = res.getContractStatus();
        String contractType = res.getContractType();
        if ("1".equals(contractStatus) && ("0".equals(contractType) || "2".equals(contractType))) {
            Date endTimeOrg = res.getEndTimeOrg();
            Instant end = endTimeOrg.toInstant();
            LocalDate endDate = end.atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate now = LocalDate.now();
            if (now.isAfter(endDate)) {
                res.setContractStatus("5");
            } else {
                res.setContractStatus("4");
            }
        }
        if ("1".equals(contractType) && "1".equals(contractStatus)) {
            res.setContractStatus("4");
        }
        // 地址协议合同时间ymd
        if ("2".equals(contractType)) {
            Date startTimeOrg = res.getStartTimeOrg();
            Date endTimeOrg = res.getEndTimeOrg();
            if (null != startTimeOrg) {
                res.setStartTime(ymd.format(startTimeOrg));
            }
            if (null != endTimeOrg) {
                res.setEndTime(ymd.format(endTimeOrg));
            }
        } else if ("0".equals(contractType)) { // 记账合同ym
            Date startTimeOrg = res.getStartTimeOrg();
            Date endTimeOrg = res.getEndTimeOrg();
            if (null != startTimeOrg) {
                res.setStartTime(ym.format(startTimeOrg));
            }
            if (null != endTimeOrg) {
                res.setEndTime(ym.format(endTimeOrg));
            }
        }
        res.setReviewList(bizNodeHistoryMapper.getListByMainIdAndType(res.getContractId(), "2"));
        res.setFile(commonBizFileService.selectOneByMainIdAndBizType(res.getContractId(), BizType.CONTRACT));
        res.setFileList(commonBizFileService.selectByMainIdAndBizType(res.getContractId(), BizType.CONTRACT));
        return res;
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long add(CustomerContractForm form) {
        CustomerContract customerContract = CustomerContractConvert.INSTANCE.convert(form);
        if (ObjectUtil.isNotEmpty(form.getChangeSubjectList())) {
            customerContract.setChangeSubject(String.join(",", form.getChangeSubjectList()));
        }
        if (null != customerContract.getActivityId()) {
            BusinessProductActivity activity = businessProductActivityService.getById(customerContract.getActivityId());
            customerContract.setActivityQuotation(activity.getActivityQuotation());
            customerContract.setActivityDiscountTime(activity.getActivityDiscountTime());
            customerContract.setActivityRemark(activity.getRemark());
        }

        BusinessProduct businessProduct = businessProductMapper.selectById(customerContract.getProductId());
        // 变更合同，原合同存在变更待审核记录 不允许变更
        String quotation = businessProduct.getQuotation();
        if ("1".equals(form.getBizType())) {
            customerContract.setContractStatus("0");
            Long originId = form.getOriginId();
            LambdaQueryWrapper<CustomerContract> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerContract::getOriginId, originId);
            wrapper.eq(CustomerContract::getContractStatus, "0"); // 待审核
            if (count(wrapper) > 0) {
                throw new ServiceException("该合同存在变更记录待审核,不允许变更");
            }
            // todo 判断变更时间不能超过原合同开始结束时间
            CustomerContract oldContract = getById(form.getOriginId());
            oldContract.setChangeStartTime(form.getChangeStartTime());
            updateById(oldContract);
            // resolved: 移除财务相关的欠费检查逻辑
            // 自定义价格检查是否欠费
            // if (StrUtil.isBlank(quotation)) {
            //     contractChangeService.unpaidCheck(oldContract);
            // }
        }

        if (null == customerContract.getContractId()) {
            // 填充 四个角色
            customerInformationUtil.setRelatedUser(customerContract.getCiId(), customerContract);
            customerContract.setDeclareType(form.getDeclare());
            // 记账合同 结束日期默认当月最后一天
            if ("0".equals(customerContract.getContractType())) {
                Date endTime = customerContract.getEndTime();
                Instant instant = endTime.toInstant();
                LocalDate ld = instant.atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate lastDay = ld.with(TemporalAdjusters.lastDayOfMonth());
                customerContract.setEndTime(Date.from(lastDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            if (null == customerContract.getContractId()) {
                customerContract.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
                customerContract.setContractNo(orderNoUtils.generateOrderEventYm("HT"));
            } else {
                customerContract.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
            }
            // 新增合同 状态为待审核
            if (null == customerContract.getContractId()) {
                customerContract.setContractStatus("0");
            }

            // 判断是否是意向合同 一个赢单商机同时存在新注册业务和记账业务
            if ("0".equals(customerContract.getContractType())) {
                Long num = getBaseMapper().getAccountNum(customerContract.getCiId());
                if (num == 0) {
                    List<CusCcBusiness> businessList = cusCcBusinessMapper.getByCustomerId(customerContract.getCiId());
                    for (CusCcBusiness item : businessList) {
                        List<CusBusinessBizVO> list = cusBusinessBizMapper.getBizListByBusinessId(item.getId());
                        if (list.size() > 1) {
                            boolean isNew = false;
                            boolean isAccount = false;
                            for (CusBusinessBizVO it : list) {
                                if ("内资注册".equals(it.getProductName()) || "内资注册 - 单办证".equals(it.getProductName())
                                        || "外资注册".equals(it.getProductName()) || "外资注册 - 单办证".equals(it.getProductName())) {
                                    isNew = true;
                                }
                                if (it.getTypeName().contains("代理记账")) {
                                    isAccount = true;
                                }
                            }
                            if (isNew && isAccount) {
                                customerContract.setIsIntention("1");
                                break;
                            }
                        }
                    }
                }
            }
            // 重复合同检查
            duplicateContractCheck(customerContract);
            saveOrUpdate(customerContract);
            CommonBizFile file = form.getFile();
            commonBizFileService.deleteByMainIdAndBizType(customerContract.getContractId(), BizType.CONTRACT);
            if ("1".equals(form.getType())) {
                createFile(customerContract);
            } else {
                if (ObjectUtil.isNotEmpty(file)) {
                    file.setMainId(customerContract.getContractId());
                    file.setBizType(BizType.CONTRACT);
                    commonBizFileService.save(file);
                }
            }
            // 创建审批记录
            Integer size = bizFlowService.create("2", customerContract.getContractType(), customerContract.getContractId());

            // 填写价格变动时，价格高于标准价，不需要审批；价格低于标准价，需要审批；
            Boolean priceChangeFlag = form.getPriceChangeFlag();
            // 如果填价格变动时输入的金额与标准价一样，则自动选回标准价；

            if (StrUtil.isNotBlank(quotation) && ObjectUtil.compare(form.getServiceCost(), new BigDecimal(quotation)) == 0) {
                priceChangeFlag = false;
            }

            // 仅通过模版添加且产品报价为"在合同中定义"的合同需要人工审批，其余默认审批通过
            if (!("1".equals(form.getType()) && "1".equals(businessProduct.getIsInContract()))) {
                // 不是价格变动或者是 价格高于产品的标准价 自动通过
                if ((!priceChangeFlag || (StrUtil.isNotBlank(quotation) && ObjectUtil.compare(form.getServiceCost(), new BigDecimal(quotation)) > 0)) &&
                        (StrUtil.isBlank(form.getDiscount()) || StrUtil.equals("活动优惠", form.getDiscount()))) {
                    for (int i = 0; i < size; i++) {
                        ReviewAuditForm auditForm = new ReviewAuditForm();
                        auditForm.setMainId(customerContract.getContractId());
                        auditForm.setType("2");
                        auditForm.setReviewStatus("1");
                        auditForm.setAutoAuditFlag(true);
                        if ("1".equals(form.getBizType())) {
                            bizNodeHistoryService.changeReviewAudit(auditForm);
                        } else {
                            bizNodeHistoryService.reviewAudit(auditForm);
                        }
                    }
                }
            }

        }
        return customerContract.getContractId();
    }

    /**
     * 查询4个客户关联人
     */
    public List<Long> selectFourPerson(Long id) {
        CustomerInformationVO customerInformationVO = customerInformationMapper.getDetailById(id);
        HashSet<Long> userIds = new HashSet<>();
        Optional.ofNullable(customerInformationVO.getMangerUserId())
                .ifPresent(userIds::add);
        Optional.ofNullable(customerInformationVO.getCustomerSuccessUserId())
                .ifPresent(userIds::add);
        Optional.ofNullable(customerInformationVO.getCounselorUserId())
                .ifPresent(userIds::add);
        Optional.ofNullable(customerInformationVO.getSponsorAccountingUserId())
                .ifPresent(userIds::add);
        return userIds.stream().toList();
    }

    // 模板创建的合同生成附件
    private void createFile(CustomerContract customerContract) {
        String contractType = customerContract.getContractType();
        BusinessProduct product = businessProductMapper.selectById(customerContract.getProductId());
        if ("1".equals(contractType)) {
            try {
                ClassPathResource classPathResource = new ClassPathResource("templates/disposable.jasper");
                InputStream inputStream = classPathResource.getInputStream();
                Map<String, Object> parameters = new HashMap<>();
                CustomerInformation customerInformation = customerInformationMapper.selectById(customerContract.getCiId());
                parameters.put("customerName", customerInformation.getCustomerName());
                parameters.put("address", customerInformation.getAddress());
                parameters.put("branchOffice", customerContract.getBranchOffice());
                parameters.put("totalCost", customerContract.getTotalCost());
                parameters.put("totalCostCn", customerContract.getTotalCostCn());
                parameters.put("otherRemark", customerContract.getOtherRemark() == null ? " " : customerContract.getOtherRemark());
                parameters.put("check1", "□");
                parameters.put("check2", "□");
                parameters.put("check3", "□");
                parameters.put("check4", "□");
                parameters.put("check5", "□");
                parameters.put("check6", "□");
                String productionCost = customerContract.getProductionCost();
                if ("由乙方全包费用".equals(productionCost)) {
                    parameters.put("check7", "□");
                    parameters.put("check8", "√");
                } else {
                    parameters.put("check7", "√");
                    parameters.put("check8", "□");
                }
                parameters.put("remark1", " ");
                parameters.put("remark2", " ");
                parameters.put("remark3", " ");
                parameters.put("remark4", " ");
                parameters.put("remark5", " ");
                parameters.put("remark6", " ");

                disposableContractTemplateHandle(product, parameters);
                JasperPrint jasperPrint = JasperFillManager.fillReport(inputStream, parameters, new JREmptyDataSource());
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                JRPdfExporter exporter = new JRPdfExporter();
                exporter.setExporterInput(new SimpleExporterInput(jasperPrint));//设置报表模版
                exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(output));//获取pdf文件流
                exporter.exportReport();

                ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(output.toByteArray());
                String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), customerContract.getContractNo() + ".pdf");
                int available = byteArrayInputStream.available();
                CommonBizFile commonBizFile = new CommonBizFile();
                commonBizFile.setFileNames(customerContract.getContractNo() + ".pdf");
                commonBizFile.setUrls(fileName);
                commonBizFile.setFileSize((long) (available / 1024));
                commonBizFile.setUploadBy(SecurityUtils.getLoginUser().getUser().getNickName());
                commonBizFile.setUploadTime(LocalDateTime.now());
                commonBizFile.setMainId(customerContract.getContractId());
                commonBizFile.setBizType(BizType.CONTRACT);
                uploadByOs(output, fileName);
                commonBizFileService.save(commonBizFile);

                inputStream.close();
                output.close();
                byteArrayInputStream.close();
            } catch (IOException | JRException e) {
                log.error(e.getMessage());
            }
        } else if ("0".equals(contractType)) {
            try {
                List<JasperPrint> jasperPrintList = new ArrayList<>();
                String declareType = customerContract.getDeclareType();
                if ("0".equals(declareType)) { // 零申报
                    ClassPathResource classPathResource1 = new ClassPathResource("templates/accounting0.jasper");
                    InputStream inputStream3 = classPathResource1.getInputStream();
                    HashMap<String, Object> parameters = new HashMap<>();
                    ClassPathResource resource1 = new ClassPathResource("img/bofeng1.png");
                    InputStream inputStream1 = resource1.getInputStream();
                    ClassPathResource resource2 = new ClassPathResource("img/bofeng2.png");
                    InputStream inputStream2 = resource2.getInputStream();
                    parameters.put("bofeng1", inputStream1);
                    parameters.put("bofeng2", inputStream2);
                    CustomerInformation customerInformation = customerInformationMapper.selectById(customerContract.getCiId());
                    parameters.put("customerName", customerInformation.getCustomerName());
                    parameters.put("branchOffice", customerContract.getBranchOffice());
                    parameters.put("contactPerson", customerContract.getContactPerson());
                    parameters.put("contactPhone", customerContract.getContactPhone());
                    parameters.put("contractNo", customerContract.getContractNo());
                    parameters.put("address", customerInformation.getAddress());
                    parameters.put("companyPerson", null == customerContract.getCompanyPerson() ? " " : customerContract.getCompanyPerson());
                    parameters.put("companyAddress", null == customerContract.getCompanyAddress() ? " " : customerContract.getCompanyAddress());
                    Date startTime = customerContract.getStartTime();
                    Instant startInstant = startTime.toInstant();
                    LocalDate start = startInstant.atZone(ZoneId.systemDefault()).toLocalDate();
                    int startYear = start.getYear();
                    int startMonth = start.getMonthValue();
                    Date endTime = customerContract.getEndTime();
                    Instant endInstant = endTime.toInstant();
                    LocalDate end = endInstant.atZone(ZoneId.systemDefault()).toLocalDate();
                    int endYear = end.getYear();
                    int endMonth = end.getMonthValue();
                    parameters.put("startYear", String.valueOf(startYear));
                    parameters.put("startMonth", String.valueOf(startMonth));
                    parameters.put("endYear", String.valueOf(endYear));
                    parameters.put("endMonth", String.valueOf(endMonth));

                    BigDecimal serviceCost = customerContract.getServiceCost();
                    parameters.put("serviceCostYear", serviceCost);

                    String taxpayerType = customerContract.getTaxpayerType();
                    if ("小规模纳税人".equals(taxpayerType)) {
                        parameters.put("check1", "√");
                        parameters.put("check2", "□");
                    } else if ("一般纳税人".equals(taxpayerType)) {
                        parameters.put("check1", "□");
                        parameters.put("check2", "√");
                    } else {
                        parameters.put("check1", "□");
                        parameters.put("check2", "□");
                    }

                    String payrollService = customerContract.getPayrollService();
                    if ("有".equals(payrollService)) {
                        parameters.put("check3", "√");
                        parameters.put("check4", "□");
                    } else if ("无".equals(payrollService)) {
                        parameters.put("check3", "□");
                        parameters.put("check4", "√");
                    } else {
                        parameters.put("check3", "□");
                        parameters.put("check4", "□");
                    }

                    parameters.put("remark", null == customerContract.getRemark() ? " " : customerContract.getRemark());
                    parameters.put("accountNumber", null == customerContract.getAccountNumber() ? " " : customerContract.getAccountNumber());

                    BusinessProduct businessProduct = product;
                    if (null == businessProduct) {
                        parameters.put("costType", " ");
                    } else {
                        if ("1".equals(businessProduct.getFeeType())) {
                            parameters.put("costType", "元/年");
                        } else if ("2".equals(businessProduct.getFeeType())) {
                            parameters.put("costType", "元/月");
                        } else {
                            parameters.put("costType", " ");
                        }
                    }
                    JasperPrint jasperPrint1 = JasperFillManager.fillReport(inputStream3, parameters, new JREmptyDataSource());
                    jasperPrintList.add(jasperPrint1);
                    inputStream1.close();
                    inputStream2.close();
                    inputStream3.close();
                } else { // 非零申报
                    ClassPathResource classPathResource1 = new ClassPathResource("templates/accounting1.jasper");
                    InputStream inputStream3 = classPathResource1.getInputStream();
                    HashMap<String, Object> parameters = new HashMap<>();
                    ClassPathResource resource1 = new ClassPathResource("img/bofeng1.png");
                    InputStream inputStream1 = resource1.getInputStream();
                    ClassPathResource resource2 = new ClassPathResource("img/bofeng2.png");
                    InputStream inputStream2 = resource2.getInputStream();
                    parameters.put("bofeng1", inputStream1);
                    parameters.put("bofeng2", inputStream2);
                    CustomerInformation customerInformation = customerInformationMapper.selectById(customerContract.getCiId());
                    parameters.put("customerName", customerInformation.getCustomerName());
                    parameters.put("branchOffice", customerContract.getBranchOffice());
                    parameters.put("contactPerson", customerContract.getContactPerson());
                    parameters.put("contactPhone", customerContract.getContactPhone());
                    parameters.put("contractNo", customerContract.getContractNo());
                    parameters.put("companyPerson", null == customerContract.getCompanyPerson() ? " " : customerContract.getCompanyPerson());
                    Date startTime = customerContract.getStartTime();
                    Instant startInstant = startTime.toInstant();
                    LocalDate start = startInstant.atZone(ZoneId.systemDefault()).toLocalDate();
                    int startYear = start.getYear();
                    int startMonth = start.getMonthValue();
                    Date endTime = customerContract.getEndTime();
                    Instant endInstant = endTime.toInstant();
                    LocalDate end = endInstant.atZone(ZoneId.systemDefault()).toLocalDate();
                    int endYear = end.getYear();
                    int endMonth = end.getMonthValue();
                    parameters.put("startYear", String.valueOf(startYear));
                    parameters.put("startMonth", String.valueOf(startMonth));
                    parameters.put("endYear", String.valueOf(endYear));
                    parameters.put("endMonth", String.valueOf(endMonth));

                    BigDecimal serviceCost = customerContract.getServiceCost();
                    parameters.put("serviceCostYear", serviceCost);

                    String taxpayerType = customerContract.getTaxpayerType();
                    if ("小规模纳税人".equals(taxpayerType)) {
                        parameters.put("check1", "√");
                        parameters.put("check2", "□");
                        parameters.put("check3", "□");
                    } else if ("一般纳税人".equals(taxpayerType)) {
                        parameters.put("check1", "□");
                        parameters.put("check2", "√");
                        parameters.put("check3", "□");
                    } else if ("进出口企业".equals(taxpayerType)) {
                        parameters.put("check1", "□");
                        parameters.put("check2", "□");
                        parameters.put("check3", "√");
                    } else {
                        parameters.put("check1", "□");
                        parameters.put("check2", "□");
                        parameters.put("check3", "□");
                    }
                    String salesRevenue = customerContract.getSalesRevenue();
                    if ("年销售额100万以下".equals(salesRevenue)) {
                        parameters.put("check8", "√");
                        parameters.put("check9", "□");
                        parameters.put("check10", "□");
                        parameters.put("check11", "□");
                    } else if ("年销售额100-500万".equals(salesRevenue)) {
                        parameters.put("check8", "□");
                        parameters.put("check9", "√");
                        parameters.put("check10", "□");
                        parameters.put("check11", "□");
                    } else if ("年销售额500-2000万".equals(salesRevenue)) {
                        parameters.put("check8", "□");
                        parameters.put("check9", "□");
                        parameters.put("check10", "√");
                        parameters.put("check11", "□");
                    } else if ("年销售额2000万以上".equals(salesRevenue)) {
                        parameters.put("check8", "□");
                        parameters.put("check9", "□");
                        parameters.put("check10", "□");
                        parameters.put("check11", "√");
                    } else {
                        parameters.put("check8", "□");
                        parameters.put("check9", "□");
                        parameters.put("check10", "□");
                        parameters.put("check11", "□");
                    }
                    parameters.put("remark", null == customerContract.getRemark() ? " " : customerContract.getRemark());
                    parameters.put("accountNumber", null == customerContract.getAccountNumber() ? " " : customerContract.getAccountNumber());

                    BusinessProduct businessProduct = product;
                    if (null == businessProduct) {
                        parameters.put("costType", " ");
                    } else {
                        if ("1".equals(businessProduct.getFeeType())) {
                            parameters.put("costType", "元/年");
                        } else if ("2".equals(businessProduct.getFeeType())) {
                            parameters.put("costType", "元/月");
                        } else {
                            parameters.put("costType", " ");
                        }
                    }
                    JasperPrint jasperPrint1 = JasperFillManager.fillReport(inputStream3, parameters, new JREmptyDataSource());
                    jasperPrintList.add(jasperPrint1);
                    inputStream1.close();
                    inputStream2.close();
                    inputStream3.close();
                }

                ClassPathResource classPathResource2 = new ClassPathResource("templates/accounting2.jasper");
                InputStream inputStream4 = classPathResource2.getInputStream();
                Map<String, Object> parameters2 = new HashMap<>();
                ClassPathResource resource21 = new ClassPathResource("img/bofeng1.png");
                InputStream inputStream21 = resource21.getInputStream();
                ClassPathResource resource22 = new ClassPathResource("img/bofeng2.png");
                InputStream inputStream22 = resource22.getInputStream();
                parameters2.put("bofeng1", inputStream21);
                parameters2.put("bofeng2", inputStream22);
                JasperPrint jasperPrint2 = JasperFillManager.fillReport(inputStream4, parameters2, new JREmptyDataSource());
                jasperPrintList.add(jasperPrint2);

                ByteArrayOutputStream output = new ByteArrayOutputStream();
                JRPdfExporter exporter = new JRPdfExporter();
                exporter.setExporterInput(SimpleExporterInput.getInstance(jasperPrintList));//设置多个报表模版
                exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(output));//获取pdf文件流
                exporter.exportReport();

                ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(output.toByteArray());
                String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), customerContract.getContractNo() + ".pdf");
                int available = byteArrayInputStream.available();
                CommonBizFile commonBizFile = new CommonBizFile();
                commonBizFile.setFileNames(customerContract.getContractNo() + ".pdf");
                commonBizFile.setUrls(fileName);
                commonBizFile.setFileSize((long) (available / 1024));
                commonBizFile.setUploadBy(SecurityUtils.getLoginUser().getUser().getNickName());
                commonBizFile.setUploadTime(LocalDateTime.now());
                commonBizFile.setMainId(customerContract.getContractId());
                commonBizFile.setBizType(BizType.CONTRACT);
                uploadByOs(output, fileName);
                commonBizFileService.save(commonBizFile);

                inputStream21.close();
                inputStream22.close();
                inputStream4.close();
                output.close();
                byteArrayInputStream.close();
            } catch (IOException | JRException e) {
                log.error(e.getMessage());
            }
        } else if ("2".equals(contractType)) {
            Map<String, Object> parameters = new HashMap<>();
            CustomerInformation customerInformation = customerInformationMapper.selectById(customerContract.getCiId());
            parameters.put("customerName", null == customerInformation.getCustomerName() ? " " : customerInformation.getCustomerName());
            parameters.put("address", null == customerContract.getContactAddress() ? " " : customerContract.getContactAddress());
            parameters.put("contactPerson", customerContract.getContactPerson());
            parameters.put("contactPhone", customerContract.getContactPhone());
            parameters.put("identityNumber", null == customerContract.getIdentityNumber() ? " " : customerContract.getIdentityNumber());
            parameters.put("companyName", null == customerContract.getBranchOffice() ? " " : customerContract.getBranchOffice());
            parameters.put("companyName1", customerContract.getCompanyName());
            parameters.put("legalPerson", customerContract.getLegalPerson());
            parameters.put("legalPhone", customerContract.getLegalPhone());
            parameters.put("custodyAddress", customerContract.getCustodyAddress());
            parameters.put("companyPerson", null == customerContract.getCompanyPerson() ? " " : customerContract.getCompanyPerson());
            parameters.put("companyAddress", null == customerContract.getCompanyAddress() ? " " : customerContract.getCompanyAddress());
            parameters.put("companyPhone", null == customerContract.getCompanyPhone() ? " " : customerContract.getCompanyPhone());
            parameters.put("everyYear", null == customerContract.getEveryYear() ? " " : customerContract.getEveryYear().toString());
            Date startTime = customerContract.getStartTime();
            Date endTime = customerContract.getEndTime();
            parameters.put("startYear", ymdCn.format(startTime));
            parameters.put("endYear", ymdCn.format(endTime));
            parameters.put("money1", String.valueOf(customerContract.getServiceCost()));
            String htmlStr = customerContract.getHtmlStr();
            if (htmlStr.contains("园区服务协议书")) {
                try {
                    ClassPathResource classPathResource1 = new ClassPathResource("templates/park1.jasper");
                    InputStream inputStream = classPathResource1.getInputStream();
                    JasperPrint jasperPrint1 = JasperFillManager.fillReport(inputStream, parameters, new JREmptyDataSource());

                    ClassPathResource classPathResource2 = new ClassPathResource("templates/park2.jasper");
                    InputStream inputStream1 = classPathResource2.getInputStream();
                    JasperPrint jasperPrint2 = JasperFillManager.fillReport(inputStream1, new HashMap<>(), new JREmptyDataSource());

                    List<JasperPrint> jasperPrintList = new ArrayList<>();
                    jasperPrintList.add(jasperPrint1);
                    jasperPrintList.add(jasperPrint2);

                    ByteArrayOutputStream output = new ByteArrayOutputStream();
                    JRPdfExporter exporter = new JRPdfExporter();
                    exporter.setExporterInput(SimpleExporterInput.getInstance(jasperPrintList));//设置多个报表模版
                    exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(output));//获取pdf文件流
                    exporter.exportReport();

                    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(output.toByteArray());
                    String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), customerContract.getContractNo() + ".pdf");
                    int available = byteArrayInputStream.available();
                    CommonBizFile commonBizFile = new CommonBizFile();
                    commonBizFile.setFileNames(customerContract.getContractNo() + ".pdf");
                    commonBizFile.setUrls(fileName);
                    commonBizFile.setFileSize((long) (available / 1024));
                    commonBizFile.setUploadBy(SecurityUtils.getLoginUser().getUser().getNickName());
                    commonBizFile.setUploadTime(LocalDateTime.now());
                    commonBizFile.setMainId(customerContract.getContractId());
                    commonBizFile.setBizType(BizType.CONTRACT);
                    uploadByOs(output, fileName);
                    commonBizFileService.save(commonBizFile);

                    inputStream.close();
                    inputStream1.close();
                    output.close();
                    byteArrayInputStream.close();
                } catch (IOException | JRException e) {
                    log.error(e.getMessage());
                }
            } else {
                try {
                    ClassPathResource classPathResource1 = new ClassPathResource("templates/residence_trusteeship1.jasper");
                    InputStream inputStream = classPathResource1.getInputStream();
                    JasperPrint jasperPrint1 = JasperFillManager.fillReport(inputStream, parameters, new JREmptyDataSource());

                    ClassPathResource classPathResource2 = new ClassPathResource("templates/residence_trusteeship2.jasper");
                    InputStream inputStream1 = classPathResource2.getInputStream();
                    JasperPrint jasperPrint2 = JasperFillManager.fillReport(inputStream1, new HashMap<>(), new JREmptyDataSource());

                    List<JasperPrint> jasperPrintList = new ArrayList<>();
                    jasperPrintList.add(jasperPrint1);
                    jasperPrintList.add(jasperPrint2);

                    ByteArrayOutputStream output = new ByteArrayOutputStream();
                    JRPdfExporter exporter = new JRPdfExporter();
                    exporter.setExporterInput(SimpleExporterInput.getInstance(jasperPrintList));//设置多个报表模版
                    exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(output));//获取pdf文件流
                    exporter.exportReport();

                    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(output.toByteArray());
                    String fileName = StringUtils.format("{}_{}", Seq.getId(Seq.uploadSeqType), customerContract.getContractNo() + ".pdf");
                    int available = byteArrayInputStream.available();
                    CommonBizFile commonBizFile = new CommonBizFile();
                    commonBizFile.setFileNames(customerContract.getContractNo() + ".pdf");
                    commonBizFile.setUrls(fileName);
                    commonBizFile.setFileSize((long) (available / 1024));
                    commonBizFile.setUploadBy(SecurityUtils.getLoginUser().getUser().getNickName());
                    commonBizFile.setUploadTime(LocalDateTime.now());
                    commonBizFile.setMainId(customerContract.getContractId());
                    commonBizFile.setBizType(BizType.CONTRACT);
                    uploadByOs(output, fileName);
                    commonBizFileService.save(commonBizFile);

                    inputStream.close();
                    inputStream1.close();
                    output.close();
                    byteArrayInputStream.close();
                } catch (IOException | JRException e) {
                    log.error(e.getMessage());
                }
            }
        }
    }

    /**
     * 一次性合同模板内容处理
     *
     * @param product 产品
     * @param parameters 模板参数
     */
    private void disposableContractTemplateHandle(BusinessProduct product, Map<String, Object> parameters) {
        // 模板勾选判断
        String productName = product.getProductName();
        BusinessType businessType = businessTypeMapper.selectById(product.getTypeId());
        if (StrUtil.contains(businessType.getTypeName(), "许可证")) {
            parameters.put("check4", "√");
            parameters.put("remark4", productName);
            return;
        }
        if (StrUtil.contains(productName, "银行")) {
            parameters.put("check5", "√");
            parameters.put("remark5", productName);
            return;
        }
        if (StrUtil.contains(productName, "注册")) {
            parameters.put("check1", "√");
            parameters.put("remark1", productName);
            return;
        }
        if (StrUtil.contains(productName, "变更")) {
            parameters.put("check2", "√");
            parameters.put("remark2", productName);
            return;
        }
        if (StrUtil.contains(productName, "注销")) {
            parameters.put("check3", "√");
            parameters.put("remark3", productName);
            return;
        }
        parameters.put("check6", "√");
        parameters.put("remark6", productName);
    }

    private void uploadByOs(ByteArrayOutputStream output, String fileName) {

        // 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。
        String objectName = Objects.requireNonNull(fileName);

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(output.toByteArray()));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        CustomerContract byId = getById(id);
        if (!"3".equals(byId.getContractStatus())) {
            throw new ServiceException("该合同状态不允许删除！");
        }
        return removeById(id);
    }

    /**
     * 批量保存数据
     *
     * @param list 数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBatch(List<CustomerContractForm> list) {
        // 检查是否有重复合同
        list.forEach(form -> {
            CustomerContract contract = CustomerContractConvert.INSTANCE.convert(form);
            if ("0".equals(contract.getContractType())) {
                Date endTime = contract.getEndTime();
                Instant instant = endTime.toInstant();
                LocalDate ld = instant.atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate lastDay = ld.with(TemporalAdjusters.lastDayOfMonth());
                contract.setEndTime(Date.from(lastDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            duplicateContractCheck(contract);
        });
        list.forEach(this::add);
    }

    /**
     * 批量删除
     *
     * @param ids ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        ids.forEach(this::deleteById);
    }

    /**
     * 根据客户id列表查询
     *
     * @param query 查询条件
     */
    public IPage<ContractListVO> listByCiId(CustomerContractQuery query) {
        var page = new Page<ContractListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listByCiId(query, page);
    }

    /**
     * 我提交的
     *
     * @param query 查询条件
     */
    public IPage<ReviewCustomerContractListVO> listMyCreate(ReviewCustomerContractQuery query) {
        var page = new Page<CustomerContractListVO>(query.getPageNum(), query.getPageSize());
        query.setUserId(SecurityUtils.getUserId());
        IPage<ReviewCustomerContractListVO> res = getBaseMapper().listMyCreate(query, page);
        List<ReviewCustomerContractListVO> resRecords = res.getRecords();
        resRecords.forEach(item -> {
            String reviewStatus = item.getReviewStatus();
            if ("3".equals(reviewStatus)) {
                item.setReviewStatus("1");
            }
        });
        return res;
    }

    /**
     * 审核列表
     *
     * @param query 插叙条件
     * @return 列表
     */
    public IPage<ReviewCustomerContractListVO> auditList(ReviewCustomerContractQuery query) {
        var page = new Page<CustomerContractListVO>(query.getPageNum(), query.getPageSize());
        Long userId = SecurityUtils.getUserId();
        query.setDataScopeSql(CustomerDataScopeSqlUtil.getDataScopeSql(null, null));
        IPage<ReviewCustomerContractListVO> res = getBaseMapper().auditList(query, page);
        List<ReviewCustomerContractListVO> resRecords = res.getRecords();
        resRecords.forEach(item -> {
            String reviewStatus = item.getReviewStatus();
            if ("3".equals(reviewStatus)) {
                item.setReviewStatus("1");
            }
        });
        return res;
    }

    /**
     * 由我审批
     *
     * @param query 查询条件
     */
    public IPage<ReviewCustomerContractListVO> listMyAudit(ReviewCustomerContractQuery query) {
        var page = new Page<CustomerContractListVO>(query.getPageNum(), query.getPageSize());
        query.setUserId(SecurityUtils.getUserId());
        return getBaseMapper().listMyAudit(query, page);
    }

    /**
     * 详情(查看权限校验)
     *
     * @param query query
     */
    @DataScope(deptAlias = "d", userAlias = "su")
    public R<CustomerContractVO> getByIdCheck(ContractDetailQuery query) {
        Boolean isDeleted = getBaseMapper().getIsDelete(query.getId());
        if (isDeleted) {
            throw new ServiceException("合同信息不存在或已删除");
        }
        CustomerContractVO res = getBaseMapper().getDetailByIdCheck(query);

        if (null == res) {
            // 如果是四个联系人 可以查看
            CustomerContractVO customerContractVO = getBaseMapper().getDetailById(query.getId(), null);
            List<Long> list = selectFourPerson(customerContractVO.getCiId());
            if (list.contains(SecurityUtils.getUserId())) {
                res = customerContractVO;
            } else {
                // 如果是归档员 可以查看
                List<SysRole> roleList = getBaseMapper().getDocRole(SecurityUtils.getUserId());
                if (ObjectUtil.isNotEmpty(roleList)) {
                    res = customerContractVO;
                }
            }
        }

        // 没有查看权限
        boolean isChange = true;
        if (null == res) {
            // 判断是否有借阅
            LambdaQueryWrapper<BorrowRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BorrowRecord::getMainId, query.getId());
            wrapper.eq(BorrowRecord::getUserId, SecurityUtils.getUserId());
            wrapper.eq(BorrowRecord::getStatus, "1");
            wrapper.ge(BorrowRecord::getExpirationTime, LocalDate.now());
            if (borrowRecordMapper.selectCount(wrapper) > 0) {
                res = getBaseMapper().getDetailById(query.getId(), null);
                isChange = false;
            } else {
                return R.ok(null, "没有该合同查看权限");
            }
        }
        // 财税顾问名字部门组装
        Map<Long, SysUser> sysUserMap = new HashMap<>();
        Map<Long, SysDept> sysDeptMap = userDeptService.getSysDeptMap();
        res.setManager(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, res.getManagerUserId()));
        // 3-已终止 4-执行中 5-已到期
        String contractStatus = res.getContractStatus();
        String contractType = res.getContractType();
        if ("1".equals(contractStatus) && ("0".equals(contractType) || "2".equals(contractType))) {
            Date endTimeOrg = res.getEndTimeOrg();
            Instant end = endTimeOrg.toInstant();
            LocalDate endDate = end.atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate now = LocalDate.now();
            if (now.isAfter(endDate)) {
                res.setContractStatus("5");
            } else {
                res.setContractStatus("4");
            }
        }
        if ("1".equals(contractType) && "1".equals(contractStatus)) {
            res.setContractStatus("4");
        }
        // 地址协议合同时间ymd
        if ("2".equals(contractType)) {
            Date startTimeOrg = res.getStartTimeOrg();
            Date endTimeOrg = res.getEndTimeOrg();
            if (null != startTimeOrg) {
                res.setStartTime(ymd.format(startTimeOrg));
            }
            if (null != endTimeOrg) {
                res.setEndTime(ymd.format(endTimeOrg));
            }
        } else if ("0".equals(contractType)) { // 记账合同ym
            Date startTimeOrg = res.getStartTimeOrg();
            Date endTimeOrg = res.getEndTimeOrg();
            if (null != startTimeOrg) {
                res.setStartTime(ym.format(startTimeOrg));
            }
            if (null != endTimeOrg) {
                res.setEndTime(ym.format(endTimeOrg));
            }
        }
        res.setReviewList(bizNodeHistoryMapper.getListByMainIdAndType(res.getContractId(), "2"));
        res.setFile(commonBizFileService.selectOneByMainIdAndBizType(res.getContractId(), BizType.CONTRACT, false));
        res.setFileList(commonBizFileService.selectByMainIdAndBizType(res.getContractId(), BizType.CONTRACT, false));
        res.setIsChange(isChange);
        return R.ok(res);
    }

    /**
     * 详情(查看权限校验,不校验是否借阅)
     *
     * @param query query
     */
    @DataScope(deptAlias = "d", userAlias = "su")
    public R<CustomerContractVO> getByIdCheckChange(ContractDetailQuery query) {
        Boolean isDeleted = getBaseMapper().getIsDelete(query.getId());
        if (isDeleted) {
            throw new ServiceException("合同信息不存在或已删除");
        }
        CustomerContractVO res = getBaseMapper().getDetailByIdCheck(query);

        if (null == res) {
            // 如果是四个联系人 可以查看
            CustomerContractVO customerContractVO = getBaseMapper().getDetailById(query.getId(), null);
            List<Long> list = selectFourPerson(customerContractVO.getCiId());
            if (list.contains(SecurityUtils.getUserId())) {
                res = customerContractVO;
            } else {
                // 如果是归档员 可以查看
                List<SysRole> roleList = getBaseMapper().getDocRole(SecurityUtils.getUserId());
                if (ObjectUtil.isNotEmpty(roleList)) {
                    res = customerContractVO;
                }
            }
        }

        // 没有查看权限
        if (null == res) {
            // 判断是否有借阅
            return R.ok(null, "没有该合同查看权限");
        }
        // 财税顾问名字部门组装
        Map<Long, SysUser> sysUserMap = new HashMap<>();
        Map<Long, SysDept> sysDeptMap = userDeptService.getSysDeptMap();
        res.setManager(userDeptService.nameDeptCombine(sysUserMap, sysDeptMap, res.getManagerUserId()));
        // 3-已终止 4-执行中 5-已到期
        String contractStatus = res.getContractStatus();
        String contractType = res.getContractType();
        if ("1".equals(contractStatus) && ("0".equals(contractType) || "2".equals(contractType))) {
            Date endTimeOrg = res.getEndTimeOrg();
            Instant end = endTimeOrg.toInstant();
            LocalDate endDate = end.atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate now = LocalDate.now();
            if (now.isAfter(endDate)) {
                res.setContractStatus("5");
            } else {
                res.setContractStatus("4");
            }
        }
        if ("1".equals(contractType) && "1".equals(contractStatus)) {
            res.setContractStatus("4");
        }
        // 地址协议合同时间ymd
        if ("2".equals(contractType)) {
            Date startTimeOrg = res.getStartTimeOrg();
            Date endTimeOrg = res.getEndTimeOrg();
            if (null != startTimeOrg) {
                res.setStartTime(ymd.format(startTimeOrg));
            }
            if (null != endTimeOrg) {
                res.setEndTime(ymd.format(endTimeOrg));
            }
        } else if ("0".equals(contractType)) { // 记账合同ym
            Date startTimeOrg = res.getStartTimeOrg();
            Date endTimeOrg = res.getEndTimeOrg();
            if (null != startTimeOrg) {
                res.setStartTime(ym.format(startTimeOrg));
            }
            if (null != endTimeOrg) {
                res.setEndTime(ym.format(endTimeOrg));
            }
        }
        res.setReviewList(bizNodeHistoryMapper.getListByMainIdAndType(res.getContractId(), "2"));
        res.setFile(commonBizFileService.selectOneByMainIdAndBizType(res.getContractId(), BizType.CONTRACT));
        res.setFileList(commonBizFileService.selectByMainIdAndBizType(res.getContractId(), BizType.CONTRACT));
        return R.ok(res);
    }

    /**
     * 添加合同附件
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public void addFile(ContractAddFileForm form) {
        CustomerContract byId = getById(form.getContractId());
        if (!"1".equals(byId.getContractStatus())) {
            throw new ServiceException("合同评审未通过,不允许更新合同");
        }
        CommonBizFile file = form.getFile();
        if (null != file) {
            file.setMainId(form.getContractId());
            file.setBizType(BizType.CONTRACT);
            commonBizFileService.save(file);
        }
    }

    /**
     * 转正式合同
     *
     * @param form form
     */
    @Transactional(rollbackFor = Exception.class)
    public Long changeToFormal(CustomerContractForm form) {
        CustomerContract customerContract = CustomerContractConvert.INSTANCE.convert(form);

        customerContract.setDeclareType(form.getDeclare());
        // 记账合同 结束日期默认当月最后一天
        if ("0".equals(customerContract.getContractType())) {
            Date endTime = customerContract.getEndTime();
            Instant instant = endTime.toInstant();
            LocalDate ld = instant.atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate lastDay = ld.with(TemporalAdjusters.lastDayOfMonth());
            customerContract.setEndTime(Date.from(lastDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }
        customerContract.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        customerContract.setIsIntention("0");
        updateById(customerContract);
        CommonBizFile file = form.getFile();
        if ("1".equals(form.getType())) {
            createFile(customerContract);
        } else {
            if (ObjectUtil.isNotEmpty(file)) {
                file.setMainId(customerContract.getContractId());
                file.setBizType(BizType.CONTRACT);
                commonBizFileService.save(file);
            }
        }

        // 生成账单
        bizNodeHistoryService.genPayment(customerContract);


        return customerContract.getContractId();
    }

    /**
     * 编辑归档号
     *
     * @param form form
     */
    public void updateDocumentNo(DocumentNoForm form) {
        LambdaQueryWrapper<CustomerContract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerContract::getDocumentNo, form.getDocumentNo());
        wrapper.ne(CustomerContract::getContractId, form.getContractId());
        if (count(wrapper) > 0) {
            throw new ServiceException("归档号已存在");
        }
        getBaseMapper().updateDocumentNo(form);
    }

    /**
     * resolved: 移除财务相关的账单检查方法
     * 校验合同是否存在关联账单
     *
     * @param contractId 合同id
     */
    public Boolean isExistBill(Long contractId) {
        // LambdaQueryWrapper<FinancePayment> wrapper = new LambdaQueryWrapper<>();
        // wrapper.eq(FinancePayment::getContractId, contractId);
        // return financePaymentMapper.selectCount(wrapper) > 0;
        return false; // 财务模块已删除，直接返回false
    }

    /**
     * 终止合同
     *
     * @param contractId 合同id
     */
    public void terminateContract(Long contractId) {
        if (isExistBill(contractId)) {
            throw new ServiceException("当前合同有关联的有效账单，无法进行终止操作");
        }
        CustomerContract byId = getById(contractId);
        byId.setContractStatus("3");
        byId.setTerminationTime(LocalDateTime.now());
        // resolved: 移除证书管理相关的办证流程作废逻辑
        // 合同终止后对应的办证流程自动作废
        // licenseBizTaskMapper.deprecateByContractId(contractId);
        updateById(byId);
    }

    /**
     * 新增/变更/续签
     * 重复合同检查
     */
    private void duplicateContractCheck(CustomerContract contract) {
        // 判断合同类型
        if (StrUtil.equals("1", contract.getContractType())) {
            // 一次性合同 同企业、同产品 创建时间两个月内不能重复添加（待审批、通过的合同中不能重复）
            // 查询
            LambdaQueryWrapper<CustomerContract> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerContract::getContractType, contract.getContractType());
            wrapper.eq(CustomerContract::getCiId, contract.getCiId());
            wrapper.eq(CustomerContract::getProductId, contract.getProductId());
            wrapper.between(CustomerContract::getCreateTime, DateUtil.offsetMonth(DateUtil.date(), -2), DateUtil.date());
            wrapper.in(CustomerContract::getContractStatus, "0", "1");
            if (count(wrapper) > 0) {
                throw new ServiceException("合同不能重复添加");
            }
        } else {
            // 周期性合同 同企业、同业务、同期间不能重复添加（待审批、通过的合同中不能重复）
            // 查询

            Long productId = contract.getProductId();
            BusinessProduct businessProduct = businessProductMapper.selectById(productId);
            BusinessType businessType = businessTypeMapper.selectById(businessProduct.getTypeId());
            List<BusinessProduct> businessProductList = businessProductMapper.selectList(Wrappers.lambdaQuery(BusinessProduct.class).eq(BusinessProduct::getTypeId, businessType.getId()));

            LambdaQueryWrapper<CustomerContract> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerContract::getContractType, contract.getContractType());
            wrapper.eq(CustomerContract::getCiId, contract.getCiId());
            wrapper.eq(CustomerContract::getStartTime, contract.getStartTime());
            wrapper.eq(CustomerContract::getEndTime, contract.getEndTime());
            wrapper.eq(CustomerContract::getMonthNum, contract.getMonthNum());
            wrapper.in(CustomerContract::getContractStatus, "0", "1");
            wrapper.in(CustomerContract::getProductId, businessProductList.stream().map(BusinessProduct::getId).collect(Collectors.toList()));
            wrapper.ne(ObjectUtil.isNotNull(contract.getOriginId()), CustomerContract::getOriginId, contract.getOriginId());

            if (count(wrapper) > 0) {
                throw new ServiceException("合同不能重复添加");
            }

        }
    }
}
