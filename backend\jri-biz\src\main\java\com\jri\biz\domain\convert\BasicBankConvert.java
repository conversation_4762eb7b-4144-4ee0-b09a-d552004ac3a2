package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicBank;
import com.jri.biz.domain.request.BasicBankForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 银行信息对象转换
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Mapper
public interface BasicBankConvert {
    BasicBankConvert INSTANCE = Mappers.getMapper(BasicBankConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BasicBank convert(BasicBankForm form);

}