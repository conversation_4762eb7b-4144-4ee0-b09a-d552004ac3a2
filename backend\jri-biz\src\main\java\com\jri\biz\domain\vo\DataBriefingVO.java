package com.jri.biz.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "DataBriefingVO视图对象")
public class DataBriefingVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索数量")
    private Long clueNum;

    @ApiModelProperty("客户数量")
    private Long cusNum;

    @ApiModelProperty("商机数量")
    private Long businessNum;

    @ApiModelProperty("超过10天未跟进线索")
    private Long overTenClueNum;

    @ApiModelProperty("超过10天未跟进线索")
    private Long overTenCusNum;

    @ApiModelProperty("转企业数量")
    private Long changeNum;
}