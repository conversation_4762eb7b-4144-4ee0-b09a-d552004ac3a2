package com.jri.biz.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.domain.convert.CustomerSocialFundConvert;
import com.jri.biz.domain.entity.CustomerSocialFund;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.request.CustomerSocialFundForm;
import com.jri.biz.domain.request.CustomerSocialFundQuery;
import com.jri.biz.domain.vo.CustomerSocialFundListVO;
import com.jri.biz.domain.vo.CustomerSocialFundVO;
import com.jri.biz.mapper.CustomerSocialFundMapper;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Service
public class CustomerSocialFundService extends ServiceImpl<CustomerSocialFundMapper, CustomerSocialFund> {

    @Resource
    private CommonBizFileService commonBizFileService;

    @Resource
    private CustomerSocialFundMapper customerSocialFundMapper;

    @Resource
    private CompletenessService completenessService;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerSocialFundListVO> listPage(CustomerSocialFundQuery query) {
        var page = new Page<CustomerSocialFundListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CustomerSocialFundVO getDetailById(Long id) {
        CustomerSocialFundVO customerSocialFundVO=getBaseMapper().getDetailById(id);
        //查询附件
        List<CommonBizFile> fundAccountOpenFileList = commonBizFileService.selectByMainIdAndBizType(customerSocialFundVO.getCiId(), BizType.FUND_ACCOUNT_OPEN);
        List<CommonBizFile> socialAccountOpenFileList = commonBizFileService.selectByMainIdAndBizType(customerSocialFundVO.getCiId(), BizType.SOCIAL_ACCOUNT_OPEN);
        customerSocialFundVO.setFundAccountOpenFileList(fundAccountOpenFileList);
        customerSocialFundVO.setSocialAccountOpenFileList(socialAccountOpenFileList);
        return customerSocialFundVO;
    }

    public CustomerSocialFundVO getDetailByCiId(Long ciId) {
        CustomerSocialFundVO customerSocialFundVO=getBaseMapper().getDetailByCiId(ciId);
        if (null != customerSocialFundVO) {
            //查询附件
            List<CommonBizFile> fundAccountOpenFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.FUND_ACCOUNT_OPEN);
            List<CommonBizFile> socialAccountOpenFileList = commonBizFileService.selectByMainIdAndBizType(ciId, BizType.SOCIAL_ACCOUNT_OPEN);
            customerSocialFundVO.setFundAccountOpenFileList(fundAccountOpenFileList);
            customerSocialFundVO.setSocialAccountOpenFileList(socialAccountOpenFileList);
        }
        return customerSocialFundVO;
    }


    /**
    * 保存
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(CustomerSocialFundForm form) {
        commonBizFileService.deleteByMainIdAndBizType(form.getCiId(), BizType.FUND_ACCOUNT_OPEN);
        commonBizFileService.deleteByMainIdAndBizType(form.getCiId(), BizType.SOCIAL_ACCOUNT_OPEN);
        //保存附件
        List<CommonBizFile> fundAccountOpenFileList = form.getFundAccountOpenFileList();
        List<CommonBizFile> socialAccountOpenFileList = form.getSocialAccountOpenFileList();
        if (ObjectUtil.isNotEmpty(fundAccountOpenFileList)) {
            fundAccountOpenFileList.forEach(item -> {
                item.setMainId(form.getCiId());
                item.setBizType(BizType.FUND_ACCOUNT_OPEN);
            });
            commonBizFileService.saveBatch(fundAccountOpenFileList);
        }
        if (ObjectUtil.isNotEmpty(socialAccountOpenFileList)) {
            socialAccountOpenFileList.forEach(item -> {
                item.setMainId(form.getCiId());
                item.setBizType(BizType.SOCIAL_ACCOUNT_OPEN);
            });
            commonBizFileService.saveBatch(socialAccountOpenFileList);
        }
        CustomerSocialFund customerSocialFund = CustomerSocialFundConvert.INSTANCE.convert(form);
        String content = "编辑";
        if(form.getId() == null){
            content = "新增";
        }
        saveOrUpdate(customerSocialFund);
        customerChangeRecordService.sendChangeMessage(customerSocialFund.getCiId());
        CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
        changeRecordForm.setCiId(customerSocialFund.getCiId());
        changeRecordForm.setContent(content);
        changeRecordForm.setInfoSection("社保公积金");
        customerChangeRecordService.add(changeRecordForm);
        completenessService.UpdateCompleteness(customerSocialFund.getCiId());
        return customerSocialFund.getId();
    }

    /**
    * 修改
    *
    * @param form 表单
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerSocialFundForm form) {
        // todo 完善新增/更新逻辑
        CustomerSocialFund customerSocialFund = CustomerSocialFundConvert.INSTANCE.convert(form);
        return updateById(customerSocialFund);
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    * @return 结果
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        String updateBy= SecurityUtils.getLoginUser().getUser().getNickName();
        CustomerSocialFundVO customerSocialFundVO = getDetailById(id);
        commonBizFileService.deleteByMainIdAndBizType(customerSocialFundVO.getCiId(), BizType.FUND_ACCOUNT_OPEN);
        commonBizFileService.deleteByMainIdAndBizType(customerSocialFundVO.getCiId(), BizType.SOCIAL_ACCOUNT_OPEN);
        return customerSocialFundMapper.deleteById(id,updateBy);
    }
}
