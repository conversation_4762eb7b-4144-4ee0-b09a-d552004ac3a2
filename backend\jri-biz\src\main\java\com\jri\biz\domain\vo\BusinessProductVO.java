package com.jri.biz.domain.vo;
import java.io.Serial;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BusinessProductVO视图对象")
public class BusinessProductVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}