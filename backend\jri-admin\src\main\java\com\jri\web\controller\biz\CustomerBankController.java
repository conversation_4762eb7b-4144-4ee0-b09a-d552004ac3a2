package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerBankForm;
import com.jri.biz.domain.request.CustomerBankQuery;
import com.jri.biz.domain.vo.CustomerBankListVO;
import com.jri.biz.domain.vo.CustomerBankVO;
import com.jri.biz.service.CustomerBankService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerBank")
@Api(tags = "客户银行信息")
public class CustomerBankController {
    @Autowired
    private CustomerBankService customerBankService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerBankListVO>> listPage(CustomerBankQuery query) {
        return R.ok(customerBankService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerBankVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerBankService.getDetailById(id));
    }
    @GetMapping("/getByCiId")
    @ApiOperation("通过ciId获取详情")
    public R<CustomerBankVO> getDetailByCiId(@RequestParam("id") Long ciId) {
        return R.ok(customerBankService.getDetailByCiId(ciId));
    }


    @PostMapping("/save")
    @ApiOperation("保存或更新数据")
    public R<Long> save(@RequestBody @Valid CustomerBankForm form) {
        return R.ok(customerBankService.addOrUpdate(form));
    }

//    @PutMapping("/update")
//    @ApiOperation("更新数据")
//    public R<Boolean> update(@RequestBody @Valid CustomerBankForm form) {
//        return R.ok(customerBankService.update(form));
//    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerBankService.deleteById(id));
    }
}

