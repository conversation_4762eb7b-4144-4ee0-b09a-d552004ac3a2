package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/1/4 16:47
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "人员&平台来源统计VO")
public class ClueStaffSourcePlatformAnalyseVO extends BaseStaffSourceAnalyseVO {

    @ApiModelProperty(value = "申诉量")
    private Long appealCount = 0L;

    @ApiModelProperty(value = "申诉成功量")
    private Long appealSuccessCount = 0L;

    @ApiModelProperty(value = "线索单价")
    private BigDecimal unitPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "线索成本")
    private BigDecimal cost = BigDecimal.ZERO;

}
