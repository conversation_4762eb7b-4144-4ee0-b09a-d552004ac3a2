package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCustomerOrClue;
import com.jri.biz.cus.domain.request.CusCustomerOrClueForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 线索/客户信息对象转换
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Mapper
public interface CusCustomerOrClueConvert {
    CusCustomerOrClueConvert INSTANCE = Mappers.getMapper(CusCustomerOrClueConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCustomerOrClue convert(CusCustomerOrClueForm form);

}