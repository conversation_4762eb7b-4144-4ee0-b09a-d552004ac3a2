package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@ApiModel(value = "编辑归档号")
public class DocumentNoForm {

    @ApiModelProperty("归档号")
    @NotEmpty(message = "归档号不能为空")
    private String documentNo;

    @ApiModelProperty("合同id")
    @NotNull(message = "合同id不能为空")
    private Long contractId;
}
