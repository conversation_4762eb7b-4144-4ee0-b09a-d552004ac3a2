package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("biz_flow_node")
@ApiModel(value = "BizFlowNode对象", description = "")
public class BizFlowNode implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 审批人id
     */
    @NotNull(message = "审批人id不能为空")
    private Long userId;

    /**
     * 节点key
     */
    private Integer nodeKey;

    /**
     * 父节点key
     */
    private Integer parentKey;

    /**
     * 主表id
     */
    private Long mainId;

    /**
     * 审批人姓名
     */
    @TableField(exist = false)
    private String nickName;
}
