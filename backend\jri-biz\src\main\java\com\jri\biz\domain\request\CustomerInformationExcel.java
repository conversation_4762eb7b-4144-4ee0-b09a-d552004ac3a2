package com.jri.biz.domain.request;

import com.jri.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/6/6 9:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerInformationExcel {

    //-- 客户基本信息 --
    //企业名称
    @Excel(name = "企业名称")
    private String customerName;

    @Excel(name = "客户经理")
    private String manger;
    @Excel(name = "所属分公司")
    private String branchOffice;

    @Excel(name = "实际经营地址")
    private String address;

    @Excel(name = "企业状态")
    private String customerStatus;
    @Excel(name = "企业性质")
    private String customerProperty;

    @Excel(name = "行业")
    private String industry;

    //-- 银行信息 --
    @Excel(name = "基本户开户银行")
    private String bankBaseName;
    @Excel(name = "基本户账号")
    private String bankBaseAccount;

    @Excel(name = "结算卡")
    private String debitCard;//设计中只需有或无，不涉及卡号是否存在问题？

    @Excel(name = "回单卡")
    private String receiptCardAccount;
    @Excel(name = "回单卡密码")
    private String receiptCardPassword;

    @Excel(name = "一般户银行开户行")
    private String commonBankName;
    @Excel(name = "一般户账号")
    private String commonBankAccount;
    @Excel(name = "一般户回单卡")
    private String commonInternetbankAccount;
    @Excel(name = "一般户回单卡密码")
    private String commonReceiptCardPassword;

    //-- 公商信息 --
    @Excel(name = "社会信用代码")
    private String crediCode;
    @Excel(name = "公司类型")
    private String type;
    @Excel(name = "法定代表人")
    private String legalPerson;
    @Excel(name = "注册地址")
    private String registeredAddress;
    @Excel(name = "联系方式")
    private String contract;
    @Excel(name = "经营范围")
    private String scope;
    @Excel(name = "登记机关")
    private String registrationAuthority;
    @Excel(name = "成立日期")
    private Date establishDate;
    @Excel(name = "注册号")
    private String registrationNumber;
    @Excel(name = "注册资金")
    private String registeredCapital;
    @Excel(name = "营业开始时间")
    private Date openDate;
    @Excel(name = "营业结束日期")
    private Date openEnd;
    @Excel(name = "组织机关代码")
    private String organizationCode;
    @Excel(name = "核准日期")
    private Date approvalDate;
    @Excel(name = "营业执照")
    private String businessLicenseStr;

    //-- 联系人信息 --
    @Excel(name = "客户名称")
    private String name;
    @Excel(name = "联系电话")
    private String phone;
    //-- 合同信息 --

    //-- 许可 --

    //-- 个性化 --

    //-- 社保信息 --


    //-- 税务信息 --

    //-- 无对应-- 以下数据暂无对应到到项目相关实体，随意命名，后续用到请移除并规范命名
    @Excel(name = "会计")
    private String sponsorAccounting;
    @Excel(name = "客服")
    private String customerSuccess;
    @Excel(name = "累计合同金额")
    private String a3;
    @Excel(name = "累计记账收款")
    private String a4;
    @Excel(name = "a联系人电话")
    private String a5;
    @Excel(name = "票据张数")
    private String a6;
    @Excel(name = "地址是否我方提供")
    private String a7;
    @Excel(name = "开票员")
    private String counselor;
    @Excel(name = "信息来源")
    private String a9;
    @Excel(name = "客户类型")
    private String a10;
    @Excel(name = "手机")
    private String a11;
    @Excel(name = "线索来源")
    private String a12;
    @Excel(name = "注册资料")
    private String registrationInformationFileStr;
    @Excel(name = "银行开户许可证")
    private String accountOpenFileStr;
    @Excel(name = "机构代码证")
    private String a15;
    @Excel(name = "公司章程")
    private String businessConstitutionFileStr;
    @Excel(name = "股东会决议")
    private String shareholderCommitteeRessolutionFileStr;
    @Excel(name = "地址")
    private String adressFileStr;
    @Excel(name = "变更信息")
    private String businessChangeInfoFileStr;
    @Excel(name = "创建者")
    private String a20;
    @Excel(name = "身份证件")
    private String identityDocumentFileStr;
    @Excel(name = "客户开票资料")
    private String invoiceInfoFileStr;
    @Excel(name = "客户经理1")
    private String a23;
    @Excel(name = "企业编号")
    private String a24;
}
