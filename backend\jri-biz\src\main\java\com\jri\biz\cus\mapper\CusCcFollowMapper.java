package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusCcFollow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusCcFollowListVO;
import com.jri.biz.cus.domain.vo.CusCcFollowVO;
import com.jri.biz.cus.domain.request.CusCcFollowQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 跟进记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface CusCcFollowMapper extends BaseMapper<CusCcFollow> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusCcFollowListVO> listPage(@Param("query") CusCcFollowQuery query, Page<CusCcFollowListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCcFollowVO getDetailById(@Param("id") Long id);

    /**
     * 列表查询
     *
     * @param query 查询条件
     */
    List<CusCcFollow> list(@Param("query") CusCcFollowQuery query);

    /**
     * 商机跟进记录列表查询
     *
     * @param query 查询条件
     */
    List<CusCcFollow> listBusiness(@Param("query") CusCcFollowQuery query);

    /**
     * 最新商机跟进记录
     *
     * @param ccId 商机id
     */
    CusCcFollow lastBusiness(@Param("ccId") Long ccId);
}
