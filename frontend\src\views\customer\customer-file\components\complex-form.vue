<!--
 * @Description: 高级新建
 * @Author: thb
 * @Date: 2023-05-25 16:46:03
 * @LastEditTime: 2023-11-14 10:39:28
 * @LastEditors: thb
-->
<template>
  <el-tabs v-model="activeName" class="tabs" :before-leave="handleBeforeLeave" @tab-change="handleTabClick">
    <el-tab-pane v-for="(value, key) in componentMap" :key="key" :label="key" :name="key">
      <template v-if="key === activeName">
        <component
          :is="componentMap[activeName]"
          v-model="formData"
          ref="componentRef"
          @on-edit="handleEdit"
          @on-success="emits('on-success')"
      /></template>
    </el-tab-pane>
    <template v-if="disabled">
      <!-- 详情的时候展示的相关附件 -->
      <!-- files -->
      <el-tab-pane label="相关附件" name="相关附件">
        <files v-if="activeName === '相关附件'" :ciId="formData.customerId" :discard="formData.discard" />
      </el-tab-pane>
      <el-tab-pane label="人员分配记录" name="人员分配记录">
        <userRelateRecord v-if="activeName === '人员分配记录'" :ciId="formData.customerId" />
      </el-tab-pane>
      <el-tab-pane label="编辑记录" name="编辑记录">
        <editRecord v-if="activeName === '编辑记录'" :ciId="formData.customerId" />
      </el-tab-pane>
    </template>
  </el-tabs>
</template>

<script setup>
import companyDetail from './company-detail.vue'
import associatedContract from './associated-contract.vue'
import bankDetail from './bank-detail.vue'
import commercialDetail from './commercial-detail.vue'
import license from './license.vue'
import socialFund from './social-fund.vue'
import tax from './tax.vue'
import personality from './personality.vue'
import contact from './contact.vue'
import files from './files.vue'
import { ElMessageBox } from 'element-plus'
import { getCustomerById } from '@/api/customer/file'
import { FormValidators } from '@/utils/validate'
import editRecord from './edit-record.vue'
import userRelateRecord from './user-relate-record.vue'
const disabled = inject('disabled')
const componentMap = {
  企业信息: companyDetail,
  联系人: contact,
  银行信息: bankDetail,
  工商信息: commercialDetail,
  许可证件: license,
  社保公积金: socialFund,
  税务信息: tax,
  个性化信息: personality,
  关联合同: associatedContract
}

const activeName = ref('企业信息')
// 表单检验方法
const comRef = ref()

const getFormRef = () => {
  return comRef.value.formRef
}

const setBasicData = data => {
  formData.value = Object.assign(
    formData.value,
    // 默认的单选框默认值
    {
      internetbankFlag: '0',

      debitCardFlag: '0',

      receiptCardFlag: '0',

      receiptCardType: '卡',

      commonInternetbankFlag: '0',

      commonInternetbankType: '卡',
      socialAccountFlag: '0',
      fundAccountFlag: '0',
      commonReceiptCardFlag: '0',
      isIndividual: false,
      individualCheckFlag: false,
      legalRealNameFlag: false,
      taxRealNameFlag: false,
      onlineRevenueRegistrationFlag: false,
      tripleAgreementFlag: false,
      identificationMethodFlag: false,
      drawingSheetFlag: '无',
      invoiceFlag: '无',
      invoiceSealFlag: '无'
    },
    data,
    {
      // 各个页面的id
      customerId: data.customerId,
      bankId: '',
      commercialId: '',
      licenseId: '',
      fundId: '',
      taxId: '',
      personalityId: '',
      contractId: '',
      rateRegistration: [],
      otherType: [],
      type: [],
      licenseTable: {
        tableData: []
        // rules: {}
      }, // 许可证
      taxTable: {
        tableData: []
        // rules: {}
      }, // 税收
      contactTable: {
        tableData: [
          {
            name: data.contactPerson,
            // ciId: data.customerId,
            id: data.contractMainId, //联系人id
            dept: '', // 岗位
            phone: data.contactPhone,
            createBy: data.createBy,
            createTime: data.createTime,
            // 默认值
            isLeader: 0,
            isOften: 0,
            post: '', // 岗位
            wechat: '',
            qq: '',
            email: '',
            sex: '0',
            birthday: ''
          }
        ],
        rules: {
          phone: [{ message: '请输入正确的联系电话', trigger: 'blur', validator: FormValidators.allPhone }]
        }
      }, // 联系人    // 将props.modelValue中的联系人姓名和联系人手机号赋值给tableData
      commonList: [] // 一般户银行信息列表
    }
  )
}

const formData = ref({})

// 用于判断当前tab下是否被编辑
const isEdit = ref(false)
// const handleChange = () => {
//   isEdit.value = true
// }

// 监听formData
let firstChange = true
// let firstChangeEdit = false
const handleEdit = () => {
  // firstChangeEdit = true
  isEdit.value = false
}

const edit = inject('isEdit')

let firstChangeEdit = edit.value
watch(
  formData,
  (newVal, oldVal) => {
    console.log('formData', newVal, oldVal)
    if (disabled.value) {
      return
    }
    // 默认不触发
    if (firstChange) {
      firstChange = false
      return
    }
    // 用于编辑下默认不触发
    if (firstChangeEdit) {
      firstChangeEdit = false
      return
    }
    console.log('watch-iseDIT')
    isEdit.value = true
  },
  {
    deep: true
  }
)
const handleBeforeLeave = (activeName, oldActiveName) => {
  if (isEdit.value) {
    // 提示
    ElMessageBox.confirm(`${oldActiveName}下信息变更,请记得保存`, '提示', {
      type: 'warning'
    })
    isEdit.value = false
    return false
  }
}

// tab切换
const handleTabClick = async paneName => {
  if (paneName === '企业信息' && disabled.value) {
    // 获取企业信息接口
    const { data } = await getCustomerById(formData.value.customerId)
    setBasicData(data)
  }
}
const componentRef = ref()
const saveRemote = async () => {
  // const index = Object.keys(componentMap).findIndex(item => item === activeName.value)
  console.log('componentRef', componentRef.value)
  if (componentRef.value[0].saveRemote) {
    const result = await componentRef.value[0].saveRemote()
    console.log('result', result)
    isEdit.value = false
    return result
  }

  // if (result) {
  //   // 如果保存成功则需要初始化isEdit

  // }
}

const emits = defineEmits(['on-success'])
defineExpose({
  formData,
  getFormRef,
  setBasicData,
  saveRemote
})
</script>

<style lang="scss" scoped>
:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor.el-date-editor--date) {
  width: 100%;
}
</style>
