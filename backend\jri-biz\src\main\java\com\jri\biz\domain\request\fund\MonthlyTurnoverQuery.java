package com.jri.biz.domain.request.fund;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jri.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "查询对象")
public class MonthlyTurnoverQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerNo;

    @ApiModelProperty(value = "人员名称")
    private String salesmanName;

    @ApiModelProperty(value = "月营业额开始时间")
    private String monthlyTurnoverStart;

    @ApiModelProperty(value = "月营业额开始时间")
    private String monthlyTurnoverEnd;

    @ApiModelProperty(value = "月营业额最小")
    private BigDecimal monthlyTurnoverMin;

    @ApiModelProperty(value = "月营业额最大")
    private BigDecimal monthlyTurnoverMax;

    @JsonIgnore
    private List<Long> customerInList;

    @JsonIgnore
    private List<Long> customerNotInList;

    @ApiModelProperty("客户成功用户id")
    private Long customerSuccessUserId;

    @ApiModelProperty("业务类型 记账 地址")
    private String bizType;

    @ApiModelProperty("业务类型 记账 地址")
    private String type;

    @ApiModelProperty("排序列")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("开票员")
    private String counselor;

    @ApiModelProperty("客户成功")
    private String customerSuccess;

    @ApiModelProperty("主办会计")
    private String sponsorAccounting;

    @ApiModelProperty("财税顾问")
    private String manger;

    private String role;

    private List<String> customerStatusList;

}
