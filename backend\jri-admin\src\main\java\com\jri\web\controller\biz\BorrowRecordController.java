package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.BorrowRecordForm;
import com.jri.biz.domain.request.BorrowRecordQuery;
import com.jri.biz.domain.vo.BorrowRecordListVO;
import com.jri.biz.domain.vo.BorrowRecordVO;
import com.jri.biz.service.BorrowRecordService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 借阅记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Validated
@RestController
@RequestMapping("/borrowRecord")
@Api(tags = "借阅记录")
public class BorrowRecordController {
    @Resource
    private BorrowRecordService borrowRecordService;

//    @GetMapping("/list")
//    @ApiOperation("列表查询")
//    public R<IPage<BorrowRecordListVO>> listPage(BorrowRecordQuery query) {
//        return R.ok(borrowRecordService.listPage(query));
//    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<BorrowRecordVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(borrowRecordService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> save(@RequestBody @Valid BorrowRecordForm form) {
        return R.ok(borrowRecordService.add(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(borrowRecordService.deleteById(id));
    }

    @GetMapping("/listMyCreate")
    @ApiOperation("我提交的")
    public R<IPage<BorrowRecordListVO>> listMyCreate(BorrowRecordQuery query) {
        return R.ok(borrowRecordService.listMyCreate(query));
    }

    @GetMapping("/listMyAudit")
    @ApiOperation("由我审批")
    public R<IPage<BorrowRecordListVO>> listMyAudit(BorrowRecordQuery query) {
        return R.ok(borrowRecordService.listMyAudit(query));
    }
}

