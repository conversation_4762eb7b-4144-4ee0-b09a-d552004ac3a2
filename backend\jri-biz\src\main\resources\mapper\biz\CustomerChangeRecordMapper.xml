<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerChangeRecordMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerChangeRecordListVO">
        select * from customer_change_record where is_deleted = 0 and ci_id = #{query.ciId} order by create_time desc
    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerChangeRecordVO">

    </select>
    <select id="getDetailByCiId" resultType="com.jri.biz.domain.vo.CustomerChangeRecordListVO">
        select * from customer_change_record where is_deleted = 0 and ci_id = #{ciId} order by create_time desc
    </select>
</mapper>
