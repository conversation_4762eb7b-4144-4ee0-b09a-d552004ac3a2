package com.jri.biz.domain.request;

import com.jri.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="查询对象")
public class ContractDetailQuery extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

}
