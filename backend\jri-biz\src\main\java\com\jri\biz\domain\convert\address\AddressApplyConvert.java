package com.jri.biz.domain.convert.address;

import com.jri.biz.domain.entity.address.AddressApply;
import com.jri.biz.domain.request.address.AddressApplyForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 地址申请对象转换
 *
 * <AUTHOR>
 * @since 2023-12-01
 */

@Mapper
public interface AddressApplyConvert {
    AddressApplyConvert INSTANCE = Mappers.getMapper(AddressApplyConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    AddressApply convert(AddressApplyForm form);

}