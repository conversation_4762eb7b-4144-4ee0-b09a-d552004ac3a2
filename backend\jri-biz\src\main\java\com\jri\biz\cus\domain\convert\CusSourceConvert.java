package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSource;
import com.jri.biz.cus.domain.request.CusSourceBaseForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客资来源对象转换
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Mapper
public interface CusSourceConvert {
    CusSourceConvert INSTANCE = Mappers.getMapper(CusSourceConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusSource convert(CusSourceBaseForm form);

}