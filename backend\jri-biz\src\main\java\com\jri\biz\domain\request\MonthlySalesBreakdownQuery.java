package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024/6/28 16:45
 *
 */
@Getter
@Setter
@ApiModel(value = "月营业额明细")
public class MonthlySalesBreakdownQuery {

    private String type;

    @ApiModelProperty("业务类型 记账 地址")
    private String bizType;

    @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth startDate;

    @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth endDate;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerNo;

    @ApiModelProperty("开票员")
    private String counselor;

    @ApiModelProperty("客户成功")
    private String customerSuccess;

    @ApiModelProperty("主办会计")
    private String sponsorAccounting;

    @ApiModelProperty("财税顾问")
    private String manger;

    @ApiModelProperty(value = "月营业额最小")
    private BigDecimal monthlyTurnoverMin;

    @ApiModelProperty(value = "月营业额最大")
    private BigDecimal monthlyTurnoverMax;

    @JsonIgnore
    private List<Long> customerInList;

    @JsonIgnore
    private List<Long> customerNotInList;

    @JsonIgnore
    private List<String> feeTypeList;

    private String typeName;

    private LocalDate startDateInSql;

    private List<String> customerStatusList;

}
