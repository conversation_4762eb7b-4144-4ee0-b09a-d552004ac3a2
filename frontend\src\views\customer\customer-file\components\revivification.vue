<!--
 * @Description: 还原客户弹窗
 * @Author: thb
 * @Date: 2023-05-30 09:56:01
 * @LastEditTime: 2023-08-30 08:53:26
 * @LastEditors: thb
-->

<template>
  <el-dialog
    align-center
    class="revivification-dialog"
    title="是否确认还原企业"
    width="400"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <!-- <p>请注意!</p> -->
    <!-- <p>当前用户于{{ detail?.discardTime }}被废弃，原因如下:</p> -->
    <el-alert :title="`当前用户于${detail?.discardTime}被废弃`" type="warning" show-icon :closable="false" />
    <p>原因如下：</p>
    <p>{{ detail?.discardReason }}</p>
    <!-- <p class="c-text">是否确认还原客户？</p> -->
    <template #footer>
      <el-button type="primary" @click="revShow = true">确认还原</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
  <revForm :detail="detail" v-if="revShow" @on-close="revShow = false" @on-success="emits('on-success')" />
</template>

<script setup>
// import { abandonCustomer } from '@/api/customer/file'
import revForm from './revForm.vue'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

defineProps({
  detail: Object
})

const revShow = ref(false)
</script>
<style lang="scss" scoped>
.c-text {
  color: #409eff;
}

:deep(.el-alert__title) {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #333333;
}
</style>
