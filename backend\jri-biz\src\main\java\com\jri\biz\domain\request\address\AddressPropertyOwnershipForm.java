package com.jri.biz.domain.request.address;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;


/**
 * 房屋产权证明 表单请求对象
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Data
@NoArgsConstructor
@ApiModel(value = "房屋产权证明表单请求对象")
public class AddressPropertyOwnershipForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("房本名称")
    private String name;

    @ApiModelProperty("房本分类")
    private String propertyCategory;

    @ApiModelProperty("区域id")
    private Long areaId;

    @ApiModelProperty("租赁分类")
    private String leaseCategory;

    @ApiModelProperty("租赁开始时间")
    private LocalDate leaseStartDate;

    @ApiModelProperty("租赁结束时间")
    private LocalDate leaseEndDate;

    @ApiModelProperty("租赁价格")
    private BigDecimal leasePrice;

    @ApiModelProperty("面积")
    private String regin;

    @ApiModelProperty("产权人姓名")
    private String ownerName;

    @ApiModelProperty("产权人手机")
    private String ownerPhone;

    @ApiModelProperty("出租人id")
    private Long lessorId;

    @ApiModelProperty("特许行业")
    private String licensedIndustry;

    @ApiModelProperty("禁止行业")
    private String prohibitedIndustry;

    @ApiModelProperty("其他")
    private String other;

    @ApiModelProperty("托管公司分类")
    private String managementCompanyCategory;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("图片附件")
    private List<CommonBizFile> imageFileList;

    @ApiModelProperty("文件附件")
    private List<CommonBizFile> fileList;

}
