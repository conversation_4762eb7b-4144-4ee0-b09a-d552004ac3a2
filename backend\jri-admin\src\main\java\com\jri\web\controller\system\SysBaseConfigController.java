package com.jri.web.controller.system;

import com.jri.common.annotation.Log;
import com.jri.common.core.controller.BaseController;
import com.jri.common.core.domain.BaseEntity;
import com.jri.common.core.domain.R;
import com.jri.common.core.domain.entity.SysBaseConfig;
import com.jri.common.core.page.TableDataInfo;
import com.jri.common.enums.BusinessType;
import com.jri.system.service.impl.SysBaseConfigServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 基础信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Api(tags = "基础信息管理")
@RestController
@RequestMapping("/system/baseConfig")
public class SysBaseConfigController extends BaseController {
    @Autowired
    private SysBaseConfigServiceImpl sysBaseConfigService;

    /**
     * 查询基础信息列表
     */
    @ApiOperation("查询基础信息列表")
//    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public R<TableDataInfo> list(SysBaseConfig sysBaseConfig) {
        startPage();
        List<SysBaseConfig> list = sysBaseConfigService.selectSysBaseConfigList(sysBaseConfig);
        BaseEntity baseEntity = new BaseEntity();
        baseEntity.setPageNum(sysBaseConfig.getPageNum());
        baseEntity.setPageSize(sysBaseConfig.getPageSize());
        return R.ok(getDataTable(list, baseEntity));
    }


    /**
     * 新增基础信息
     */
    @ApiOperation("新增或修改基础信息")
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> saveOrUpate(@RequestBody SysBaseConfig sysBaseConfig) {
        sysBaseConfigService.saveOrUpateSysBaseConfig(sysBaseConfig);
        return R.ok();
    }

    /**
     * 删除基础信息
     */
    @ApiOperation("删除基础信息")
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{baseconfigIds}")
    public R<Void> remove(@PathVariable Long[] baseconfigIds) {
        sysBaseConfigService.deleteSysBaseConfigByBaseconfigIds(baseconfigIds);
        return R.ok();
    }
}
