package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 产品活动价
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-02
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("business_product_activity")
@ApiModel(value = "BusinessProductActivity对象", description = "产品活动价")
public class BusinessProductActivity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "activity_id",type= IdType.AUTO)
    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("活动报价")
    private String activityQuotation;

    @ApiModelProperty("优惠时长(月)")
    @TableField(fill = FieldFill.UPDATE)
    private Integer activityDiscountTime;

    @ApiModelProperty("是否启用0-否1-是")
    private String activityStatus;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("活动txt")
    private String activityTxt;
}
