package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusTag;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusTagListVO;
import com.jri.biz.cus.domain.vo.CusTagVO;
import com.jri.biz.cus.domain.request.CusTagQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-09
 */
public interface CusTagMapper extends BaseMapper<CusTag> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusTagListVO> listPage(@Param("query") CusTagQuery query, Page<CusTagListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusTagVO getDetailById(@Param("id") Long id);
}
