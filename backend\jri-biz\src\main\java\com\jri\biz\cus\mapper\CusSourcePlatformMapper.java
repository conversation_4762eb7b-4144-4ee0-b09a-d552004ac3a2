package com.jri.biz.cus.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.entity.CusSourcePlatform;

/**
 * <p>
 * 客资来源 平台 关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface CusSourcePlatformMapper extends BaseMapper<CusSourcePlatform> {

    /**
     * 根据主表id获取信息
     *
     * @return 信息
     */
    default CusSourcePlatform getByMainId(Long mainId) {
        return selectOne(new LambdaQueryWrapper<CusSourcePlatform>().eq(CusSourcePlatform::getMainId, mainId));
    }
}
