package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCustomerInSeaListVO视图列表对象")
public class CusCustomerInSeaListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @Excel(name = "客户名称")
    @ApiModelProperty("公司名称(客户名称)")
    private String companyName;

    @ApiModelProperty("公海id")
    private Long seaId;

    @Excel(name = "所属公海")
    @ApiModelProperty("公海名称")
    private String seaName;

    // *************联系人表******************

    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("线索来源(客户来源)")
    private String source;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @Excel(name = "客户来源")
    @ApiModelProperty("客户来源")
    private String sourceName;

    @Excel(name = "客户等级")
    @ApiModelProperty("客户等级")
    private String level;

    @Excel(name = "跟进状态", readConverterExp = "0=未跟进,1=跟进中,2=已转企业")
    @ApiModelProperty("跟进状态0-未跟进1-跟进中2-已转企业")
    private String followStatus;

    @Excel(name = "成为客户时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("成为客户时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime becomeTime;



    @Excel(name = "最近修改时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedTime;

    @Excel(name = "最近跟进时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFollowTime;

    @Excel(name = "标签")
    @ApiModelProperty("标签名")
    private String tagsName;

    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;

    @Excel(name = "税务性质")
    @ApiModelProperty("税务性质")
    private String taxNature;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Excel(name = "创建者")
    @ApiModelProperty("创建者")
    private String createBy;

    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("资源分配规则0-员工领取1-仅管理员分配2-员工领取+管理员分配")
    private String rule;

    @ApiModelProperty("公海管理员id")
    private Long manageId;

    @ApiModelProperty("是否有领取权限")
    private Boolean isGet = false;

    @ApiModelProperty("是否有分配权限")
    private Boolean isDivide = false;

    @ApiModelProperty("保护开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime protectionStartTime;

    @ApiModelProperty("部门id列表")
    private List<Long> deptIds;
}
