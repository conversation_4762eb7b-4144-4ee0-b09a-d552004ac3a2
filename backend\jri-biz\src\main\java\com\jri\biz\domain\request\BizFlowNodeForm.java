package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 *  表单请求对象
 *
 * <AUTHOR>
 * @since 2023-06-16
 */

@Data
@NoArgsConstructor
@ApiModel(value="流程表单请求对象")
public class BizFlowNodeForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

}
