package com.jri.biz.domain.vo;

import com.jri.biz.domain.entity.CustomerBankCommon;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CustomerBankVO视图对象")
public class CustomerBankVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long bankId;

    /**
     * 客户信息主表id
     */
    @NotEmpty(message = "客户信息主表不能为空")
    private Long ciId;
    /**
     * 基本户开户银行
     */
    private String bankBaseName;

    /**
     * 基本户账号
     */
    private String bankBaseAccount;

    /**
     * 是否有网银(0-没有 1-有)
     */
//    private String internetbankFlag;

    /**
     * 是否有结算卡(0-没有 1-有)
     */
//    private String debitCardFlag;

    /**
     * 是否有回单卡(0-没有 1-有)
     */
    private String receiptCardFlag;

    /**
     * 回单卡账户
     */
    private String receiptCardAccount;

    /**
     * 回单卡密码
     */
    private String receiptCardPassword;

    /**
     * 回单卡类型
     */
    private String receiptCardType;

    /**
     * 一般户开户银行
     */
    private String commonBankName;

    /**
     * 一般户账号
     */
    private String commonBankAccount;

    /**
     * 是否有一般户网银(0-没有 1-有)
     */
    private String commonInternetbankFlag;

    /**
     * 是否有一般户回单卡(0-没有 1-有)
     */
    private String commonReceiptCardFlag;

    /**
     * 一般户回单卡账号
     */
    private String commonInternetbankAccount;

    /**
     * 一般户回单卡密码
     */
    private String commonReceiptCardPassword;

    /**
     * 一般户回单卡类型
     */
    private String commonInternetbankType;

    /**
     * 对账周期
     */
//    private String cycle;

    //--附件
    @ApiModelProperty("银行开户信息表")
    private List<CommonBizFile> accountOpenFileList;

    @ApiModelProperty("变更信息")
    private List<CommonBizFile> changeInfoFileList;

    @ApiModelProperty("一般户列表")
    private List<CustomerBankCommon> commonList;
}
