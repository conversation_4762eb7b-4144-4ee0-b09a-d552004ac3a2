package com.jri.biz.domain.vo;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 视图列表对象
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BusinessProductListVO视图列表对象")
public class BusinessProductListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}