package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BizFlowNode;
import com.jri.biz.domain.request.BizFlowNodeForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-06-16
 */

@Mapper
public interface BizFlowNodeConvert {
    BizFlowNodeConvert INSTANCE = Mappers.getMapper(BizFlowNodeConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BizFlowNode convert(BizFlowNodeForm form);

}