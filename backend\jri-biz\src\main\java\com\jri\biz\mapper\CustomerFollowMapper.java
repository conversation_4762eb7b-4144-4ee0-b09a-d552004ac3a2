package com.jri.biz.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.CustomerBank;
import com.jri.biz.domain.entity.CustomerFollow;
import com.jri.biz.domain.request.CustomerFollowQuery;
import com.jri.biz.domain.vo.CustomerBankListVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * 跟进记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-04
 */
public interface CustomerFollowMapper extends BaseMapper<CustomerFollow> {

    /**
     * 查询跟进记录
     *
     * @param id 跟进记录主键
     * @return 跟进记录
     */
    public CustomerFollow selectCustomerFollowById(Long id);

    /**
     * 查询跟进记录列表
     *
     * @param customerFollow 跟进记录
     * @return 跟进记录集合
     */

    /**
     * 新增跟进记录
     *
     * @param customerFollow 跟进记录
     * @return 结果
     */
    public int insertCustomerFollow(CustomerFollow customerFollow);

    /**
     * 修改跟进记录
     *
     * @param customerFollow 跟进记录
     * @return 结果
     */
    public int updateCustomerFollow(CustomerFollow customerFollow);

    /**
     * 删除跟进记录
     *
     * @param id 跟进记录主键
     * @return 结果
     */
    public Boolean deleteCustomerFollowById(@Param("id")Long id,@Param("updateBy")String updateBy);

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @param page 分页
     */
    IPage<CustomerFollow> selectCustomerFollowList(@Param("query") CustomerFollowQuery query, @Param("page") Page<CustomerFollow> page);


    CustomerFollow getNewByCiId(@Param("ciId") Long ciId);
}
