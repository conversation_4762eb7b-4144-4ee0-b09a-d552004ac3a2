package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 保有量设置 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-22
 */

@Data
@NoArgsConstructor
@ApiModel(value="保有量设置表单请求对象")
public class CusSeaInventoryForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("线索掉保时长")
    private Integer clueDuration;

    @ApiModelProperty("线索回收公海id")
    private Long clueSeaId;

    @ApiModelProperty("线索回收提醒提前天数")
    private Integer clueRecovery;

    @ApiModelProperty("线索员工私海上限")
    private Integer clueStaffNum;

    @ApiModelProperty("线索管理员私海上限")
    private Integer clueAdminNum;

    @ApiModelProperty("线索员工私海上限状态0-停用1-启用")
    private String clueStaffStatus;

    @ApiModelProperty("线索管理员私海上限状态0-停用1-启用")
    private String clueAdminStatus;

    @ApiModelProperty("客户掉保时长")
    private Integer cusDuration;

    @ApiModelProperty("客户回收公海id")
    private Long cusSeaId;

    @ApiModelProperty("客户回收提醒提前天数")
    private Integer cusRecovery;

    @ApiModelProperty("客户员工私海上限")
    private Integer cusStaffNum;

    @ApiModelProperty("客户管理员私海上限")
    private Integer cusAdminNum;

    @ApiModelProperty("客户员工私海上限状态0-停用1-启用")
    private String cusStaffStatus;

    @ApiModelProperty("客户管理员私海上限状态0-停用1-启用")
    private String cusAdminStatus;
}
