package com.jri.web.mapper;

import com.jri.biz.domain.entity.CustomerPersonalizedInformation;
import com.jri.biz.mapper.CustomerPersonalizedInformationMapper;
import com.jri.common.utils.SecurityUtils;
import com.jri.framework.security.context.AuthenticationContextHolder;
import org.apache.catalina.Authenticator;
import org.aspectj.lang.annotation.Before;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @since 2023/5/30 16:10
 */
@SpringBootTest
class CustomerPersonalizedInformationMapperTest {
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private CustomerPersonalizedInformationMapper mapper;

    @BeforeEach
    void before(){
        var authenticationToken = new UsernamePasswordAuthenticationToken("admin", "admin123");
        AuthenticationContextHolder.setContext(authenticationToken);
        var authentication = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }
    @Test
    void getDetailById() {
        var a = mapper.getDetailById(1663462608693215233L);
        System.out.println(a.getTags());
    }

    @Test
    void save(){
        var entity = new CustomerPersonalizedInformation();
        entity.setAgeLevel("其他");
        entity.setCiId(0L);
        entity.setPersonality("老虎");
        entity.setTags(List.of("1","2","3"));
        mapper.insert(entity);
    }
}