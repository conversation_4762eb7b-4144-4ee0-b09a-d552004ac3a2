package com.jri.biz.domain.vo;

import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 客户流动报表VO
 *
 * <AUTHOR>
 * @since 2024/4/22 13:47
 *
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "客户流动报表VO")
public class CustomerUserFlowAnalyseVO {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @Excel(name = "人员名称")
    @ApiModelProperty(value = "人员名称")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @Excel(name = "所属部门")
    @ApiModelProperty(value = "部门名")
    private String deptName;

    @Excel(name = "分析月份")
    @ApiModelProperty(value = "分析月份")
    private Integer month;

    @Excel(name = "期初")
    @ApiModelProperty(value = "期初")
    private Integer monthStart = 0;

    @Excel(name = "新接")
    @ApiModelProperty(value = "新接")
    private Integer newRelate = 0;

    @Excel(name = "正常流失")
    @ApiModelProperty(value = "正常流失")
    private Integer normalLose = 0;

    @Excel(name = "注销")
    @ApiModelProperty(value = "注销")
    private Integer cancelLose = 0;

    @Excel(name = "迁入")
    @ApiModelProperty(value = "迁入")
    private Integer migrateIn = 0;

    @Excel(name = "迁出")
    @ApiModelProperty(value = "迁出")
    private Integer migrateOut = 0;

    @Excel(name = "期末")
    @ApiModelProperty(value = "期末")
    private Integer monthEnd = 0;

}
