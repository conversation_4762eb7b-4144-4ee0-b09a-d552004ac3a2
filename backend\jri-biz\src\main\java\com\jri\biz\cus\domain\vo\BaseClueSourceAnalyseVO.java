package com.jri.biz.cus.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/2 14:11
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "基础来源统计VO")
public class BaseClueSourceAnalyseVO {

    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "线索数量")
    private Long clueCount = 0L;

    @ApiModelProperty(value = "线索转换数量 成交量")
    private Long clueConversionCount = 0L;

    @ApiModelProperty(value = "收款额")
    private BigDecimal gmv = BigDecimal.ZERO;

    @ApiModelProperty(value = "记账收款额")
    private BigDecimal bookkeepingGmv = BigDecimal.ZERO;

    @ApiModelProperty(value = "地址收款额")
    private BigDecimal addressGmv = BigDecimal.ZERO;

    @ApiModelProperty(value = "其他收款额")
    private BigDecimal otherGmv = BigDecimal.ZERO;

    private List<? extends BaseClueSourceAnalyseVO> userList;

}
