package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 流程
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("biz_flow")
@ApiModel(value = "BizFlow对象", description = "流程")
public class BizFlow implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 合同类型0-记账合同1-一次性合同2-地址服务协议合同
     */
    private String contractType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 类型0-模板审批流程1-合同借阅2-合同审批
     */
    private String type;

    /**
     * 使用状态0-关闭1-启用
     */
    private String enable;

    /**
     * 部门id集合
     */
    private String deptIds;

    private String xmlStr;
}
