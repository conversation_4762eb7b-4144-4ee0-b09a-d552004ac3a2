package com.jri.web.controller.biz;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.constants.ProgressType;
import com.jri.biz.domain.entity.CustomerTaxInformationImportDto;
import com.jri.biz.domain.request.CustomerTaxInformationForm;
import com.jri.biz.domain.request.CustomerTaxInformationQuery;
import com.jri.biz.domain.vo.CustomerTaxInformationListVO;
import com.jri.biz.domain.vo.CustomerTaxInformationVO;
import com.jri.biz.service.CustomerTaxInformationService;
import com.jri.biz.service.ProgressService;
import com.jri.biz.service.finance.ImportService;
import com.jri.common.core.domain.R;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.poi.NewExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerTaxInformation")
@Api(tags = "客户税务信息")
public class CustomerTaxInformationController {
    @Resource
    private CustomerTaxInformationService customerTaxInformationService;

    @Resource
    private ImportService importService;

    @Resource
    private ProgressService progressService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerTaxInformationListVO>> listPage(CustomerTaxInformationQuery query) {
        return R.ok(customerTaxInformationService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerTaxInformationVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerTaxInformationService.getDetailById(id));
    }

    @GetMapping("/getDetailByCiId")
    @ApiOperation("详情")
    public R<CustomerTaxInformationVO> getDetailByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerTaxInformationService.getDetailByCiId(ciId));
    }

    @PostMapping("/addOrUpdate")
    @ApiOperation("保存/新增数据")
    public R<Long> addOrUpdate(@RequestBody @Valid CustomerTaxInformationForm form) {
        return R.ok(customerTaxInformationService.addOrUpdate(form));
    }

//    @PutMapping("/update")
//    @ApiOperation("更新数据")
//    public R<Boolean> update(@RequestBody @Valid CustomerTaxInformationForm form) {
//        return R.ok(customerTaxInformationService.update(form));
//    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerTaxInformationService.deleteById(id));
    }

    @PostMapping("/importTaxInfo")
    @ApiOperation("导入税务信息")
    public R<Void> importTaxInfo(@RequestPart("file") MultipartFile file) throws Exception {
        ImportService.fileCheck(file);
        NewExcelUtil<CustomerTaxInformationImportDto> excel = new NewExcelUtil<>(CustomerTaxInformationImportDto.class);
        List<CustomerTaxInformationImportDto> taxInfoList = excel.importExcel(file.getInputStream(), excel.getExcelFieldNames().toArray(String[]::new));
        if (ObjectUtil.isEmpty(taxInfoList)) {
            throw new ServiceException("导入内容为空");
        }
        var id = progressService.create(ProgressType.CUSTOMER_UPLOAD, file.getOriginalFilename(), ProgressType.UPLOAD);
        importService.importTaxInfo(taxInfoList, id);
        return R.ok();
    }
}

