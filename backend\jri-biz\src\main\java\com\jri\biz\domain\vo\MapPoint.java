package com.jri.biz.domain.vo;

import com.jri.common.utils.GeoUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MapPoint {
    /**
     * 经度
     */

    @ApiModelProperty("经度")
    private double lng;
    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private double lat;

    public MapPoint() {

    }

    public MapPoint(double lng, double lat) {
        this.lng = lng;
        this.lat = lat;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof MapPoint mapPoint) {
            return mapPoint.getLng() == lng && mapPoint.getLat() == lat;
        } else {
            return false;
        }
    }


    /**
     * 判断目标点是否在距离内
     *
     * @param targetLat 目标维度
     * @param targetLng 目标经度
     * @return 是否在距离内
     */
    public boolean isTargetInDis(
            double targetLat, double targetLng, double dis) {
        return dis >= GeoUtil.getDistanceOfMeter(this.lat, this.lng, targetLat, targetLng);
    }
}
