package com.jri.biz.constants;

public class BizType {

    // resolved: 移除财务和合同管理相关的常量定义
    // public static final String BANK_CHANGE_INFO = "bank_change_info";//变更信息
    // public static final String NOFEE_REASON = "nofee_reason";// 不收费原因
    // public static final String INVOICE_INFO = "invoice_info";// 开票资料
    public static final String INTERVIEW = "interview";// 下户表(企业走访表)
    // public static final String CONTRACT = "contract";// 合同附件
    // public static final String BANK_ACCOUNT_OPEN = "bank_account_open";// 开户信息

    // resolved: 移除财务和材料管理相关的常量定义
    // public static final String INDIVIDUAL_TAX_PASSWORD = "individual_tax_password";// 个税密码
    // public static final String BUSINESS_CHANGE_INFO = "business_change_info";// 工商信息变更信息
    public static final String BUSINESS_LICENSE = "business_license";// 营业执照
    public static final String REGISTRATION_INFORMATION = "registration_information";// 注册资料
    public static final String BUSINESS_CONSTITUTION = "business_constitution";// 公司章程
    public static final String SHAREHOLDER_COMMITTEE_RESSOLUTION = "shareholder_committee_ressolution";// 股东会决议
    public static final String ADRESS = "adress";// 地址
    public static final String IDENTITY_DOCUMENT = "identity_document";// 身份证件
    // public static final String BUSINESS_OTHER = "business_other";// 工商信息其他附件
    // public static final String BUSINESS_HANDOVER_DOCUMENT = "business_handover_document";// 工商信息交接单

    // resolved: 移除财务和材料管理相关的常量定义
    // public static final String SOCIAL_ACCOUNT_OPEN = "social_account_open";// 社保开户信息
    // public static final String FUND_ACCOUNT_OPEN = "fund_account_open";//  公积金开户信息

    // public static final String CUSTOMER_BILLING_INFORMATION = "customer_billing_information";//客户开票资料
    // public static final String TAX_INFORMATION = "tax_information";// 税务资料上传
    // public static final String TAXPAYER_IDENTIFICATION_FORM = "taxpayer_identification_form";// 一般纳税人认定表
    // public static final String CUSTOMER_FIRST_CONFIRM = "customer_first_confirm";// 客户首次确认

    public static final String SHAREHOLDER_FAMILY_RELATIONSHIP = "shareholder_family_relationship";// 客户股东家庭关系
    public static final String INVOICING_SPECIAL_REQUIREMENT = "invoicing_special_requirement";// 开具发票特殊要求
    public static final String PERSONALIZED_INFORMATION_ATTACHMENT = "personalized_information_attachment";//个性化信息附件


    //    --收款台账
    public static final String RECEIPT_VOUCHER = "receipt_voucher";//收款凭证

    public static final String ORDER_INFO = "order_info";//工单
    public static final String ORDER_TAX_CLEARANCE = "order_tax_clearance";//工单-清税证明

    public static final String NOTIFICATION_FILE = "notification_file";//公告附件


    // -- 合同模板code
    public static final String ASSOCIATED_CUSTOMER = "associated_customer";//关联客户
    public static final String CUSTOMER_INFORMATION_CHANGE = "customer_information_change";//客户信息变更
    public static final String CUSTOMER_ASSOCIATED_PERSONNEL_CHANGE = "customer_associated_personnel_change";//客户关联人员变更
    public static final String ASSOCIATED_CONTRACT = "associated_contract";//关联合同
    public static final String CONTRACT_EXPIRATION = "contract_expiration";//合同到期
    public static final String CONTRACT_APPROVAL_PASSED = "contract_approval_passed";//合同审批通过
    public static final String CONTRACT_APPROVAL_REJECTED = "contract_approval_rejected";//合同审批驳回
    public static final String BORROWING_APPROVAL_PASSED = "borrowing_approval_passed";//借阅通过
    public static final String BORROWING_APPROVAL_REJECTED = "borrowing_approval_rejected";//借阅通过
    public static final String CHANGE_APPROVAL_PASSED = "change_approval_passed";////变更通过
    public static final String CHANGE_APPROVAL_REJECTED = "change_approval_rejected";//变更驳回
    public static final String WORK_ORDER_ASSIGNMENT = "work_order_assignment";//指派给我
    public static final String WORK_ORDER_ROLLBACK = "work_order_rollback";//回退给我
    public static final String WORK_ORDER_TIMEOUT = "work_order_timeout"; //工单超时
    public static final String CLUE_RECOVERY_REMIND = "clue_recovery_remind"; //线索回收提醒
    public static final String CUS_RECOVERY_REMIND = "cus_recovery_remind"; //客户回收提醒

    /**
     * 工商注册通知模板
     */
    public static final String BUSINESS_LICENSE_REGISTER = "business_license_register";

    // resolved: 移除证书管理相关的常量定义
    /**
     * 会计通知模板
     */
    // public static final String ACCOUNTANT_NOTIFY = "accountant_notify";

    /**
     * 工商办证-法人身份证件
     */
    // public static final String LICENSE_LEGAL_IDENTITY = "license_legal_identity";

    /**
     * 工商办证-监事身份证件
     */
    // public static final String LICENSE_SUPERVISOR_IDENTITY = "license_supervisor_identity";

    /**
     * 工商办证-产权证明
     */
    // public static final String LICENSE_OWNERSHIP_CERTIFICATE = "license_ownership_certificate";

    /**
     * 工商办证-会商资料
     */
    // public static final String LICENSE_CONSULTATION_MATERIAL = "license_consultation_material";

    /**
     * 工商办证-外资企业证明
     */
    // public static final String LICENSE_FOREIGN_ENTERPRISE_CERTIFICATE = "license_foreign_enterprise_certificate";

    // resolved: 移除证书管理相关的常量定义
    /**
     * 工商办证-翻译件
     */
    // public static final String LICENSE_TRANSLATED_DOCUMENT = "license_translated_document";

    /**
     * 工商办证-外籍护照
     */
    // public static final String LICENSE_FOREIGN_PASSPORT = "license_foreign_passport";

    /**
     * 工商办证-新注册信息确认单
     */
    // public static final String LICENSE_NEW_REGISTRATION_INFORMATION_CONFIRMATION_DOCUMENT = "license_new_registration_information_confirmation_document";

    /**
     * 工商办证-营业执照
     */
    // public static final String LICENSE_BUSINESS_LICENSE = "license_business_license";
    // public static final String NEW_LICENSE_BUSINESS_LICENSE = "new_license_business_license";

    /**
     * 工商办证-税务办理凭证
     */
    // public static final String LICENSE_TAX_PROCESSING_DOCUMENT = "license_tax_processing_document";

    /**
     * 工商办证-交接单
     */
    // public static final String LICENSE_HANDOVER_DOCUMENT = "license_handover_document";

    /**
     * 工商办证-工商注销
     */
    // public static final String LICENSE_CANCELLATION_CERTIFICATE = "license_cancellation_certificate";

    /**
     * 工商办证-注册资料
     */
    public static final String LICENSE_REGISTRATION_INFORMATION = "license_registration_information";

    /**
     * 工商办证-公司章程
     */
    public static final String LICENSE_BUSINESS_CONSTITUTION = "license_business_constitution";

    /**
     * 工商办证-股东会议决议
     */
    public static final String LICENSE_SHAREHOLDER_COMMITTEE_RESOLUTION = "license_shareholder_committee_resolution";

    /**
     * 工商办证-地址
     */
    public static final String LICENSE_ADDRESS = "license_address";
    public static final String NEW_LICENSE_ADDRESS = "new_license_address";

    /**
     * 工商办证-身份证件
     */
    public static final String LICENSE_IDENTITY_DOCUMENT = "license_identity_document";

    /**
     * 工商办证-其他附件
     */
    public static final String LICENSE_OTHER_DOCUMENT = "license_other_document";

    /**
     * 工商办证-开户许可证
     */
    public static final String LICENSE_BANK_ACCOUNT_OPEN_LICENSE = "license_bank_account_open_license";

    /**
     * 工商办证-银行其他材料
     */
    public static final String LICENSE_BANK_OTHER_DOCUMENT = "license_bank_other_document";

    /**
     * 商机其他文件
     */
    public static final String BO_OTHER_DOCUMENT = "bo_other_document";

    /**
     * 股东附件
     */
    public static final String LICENSE_SHAREHOLDER_DOCUMENT = "license_shareholder_document";

    /**
     * 执照副本
     */
    public static final String PERMIT_COPY_OF_LICENSE = "permit_copy_of_license";

    /**
     * 公章
     */
    public static final String PERMIT_OFFICIAL_SEAL = "permit_official_seal";

    /**
     * 房产证
     */
    public static final String PERMIT_PROPERTY_OWNERSHIP_CERTIFICATE = "permit_property_ownership_certificate";

    /**
     * 租赁合同
     */
    public static final String PERMIT_LEASE_CONTRACT = "permit_lease_contract";

    /**
     * 操作员身份证附件
     */
    public static final String PERMIT_OPERATOR_IDENTITY = "permit_operator_identity";

    /**
     * 劳务派遣管理制度
     */
    public static final String PERMIT_LABOR_DISPATCH_MANAGEMENT = "permit_labor_dispatch_management";

    /**
     * 劳务派遣合同范本
     */
    public static final String PERMIT_LABOR_DISPATCH_CONTRACT = "permit_labor_dispatch_contract";

    /**
     * 劳务派遣协议书范本
     */
    public static final String PERMIT_LABOR_DISPATCH_AGREEMENT = "permit_labor_dispatch_agreement";

    /**
     * 办公设备设施清单
     */
    public static final String PERMIT_OFFICE_EQUIPMENT = "permit_office_equipment";

    /**
     * 委托书
     */
    public static final String PERMIT_POWER_OF_ATTORNEY = "permit_power_of_attorney";

    /**
     * 验资报告
     */
    public static final String PERMIT_CAPITAL_VERIFICATION_REPORT = "permit_capital_verification_report";

    /**
     * 与开展业务相适应的信息管理系统清单
     */
    public static final String PERMIT_MANAGEMENT_INFO = "permit_management_info";

    /**
     * 公司章程
     */
    public static final String PERMIT_ASSOCIATION_ARTICLES = "permit_association_articles";

    /**
     * 财务章
     */
    public static final String PERMIT_FINANCIAL_SEAL = "permit_financial_seal";

    /**
     * 法人章
     */
    public static final String PERMIT_CORPORATE_SEAL = "permit_corporate_seal";

    /**
     * 基本存款账户信息表
     */
    public static final String PERMIT_BASIC_DEPOSIT_ACCOUNT = "permit_basic_deposit_account";

    /**
     * 验资密码纸
     */
    public static final String PERMIT_PASSWORD_PAPER = "permit_password_paper";

    /**
     * 平面图
     */
    public static final String PERMIT_PLAN_VIEW = "permit_plan_view";

    /**
     * 健康证
     */
    public static final String PERMIT_HEALTH_CERTIFICATE = "permit_health_certificate";

    /**
     * 健康证人员身份证
     */
    public static final String PERMIT_HEALTH_CERTIFICATE_IDENTITY = "permit_health_certificate_identity";

    /**
     * 驾驶证
     */
    public static final String PERMIT_DRIVERS_LICENSE = "permit_drivers_license";

    /**
     * 驾驶员身份证
     */
    public static final String PERMIT_DRIVERS_IDENTITY = "permit_drivers_identity";

    /**
     * 从业资格证
     */
    public static final String PERMIT_PROFESSIONAL_QUALIFICATION_CERTIFICATE = "permit_professional_qualification_certificate";

    /**
     * 许可证
     */
    public static final String LICENSE_PERMIT = "license_permit";

    /**
     * 线索-附加信息-法人身份证件
     */
    public static final String CLUE_LEGAL_IDENTITY = "clue_legal_identity";

    /**
     * 线索-附加信息-监事身份证件
     */
    public static final String CLUE_SUPERVISOR_IDENTITY = "clue_supervisor_identity";

    /**
     * 客资管理-客户管理-客户详情-附加信息
     */
    public static final String CLUE_OTHER_DOCUMENT = "clue_other_document";

    /**
     * 股东附件 客户资料
     */
    public static final String CUS_SHAREHOLDER_DOCUMENT = "cus_shareholder_document";

    /**
     * 风险客户附件
     */
    public static final String RISK_CUSTOMER = "risk_customer";

    /**
     * 风险客户任务节点附件
     */
    public static final String RISK_CUSTOMER_NODE = "risk_customer_node";

    /**
     * 意见反馈-附件
     */
    public static final String SUGGESTION_FILE = "suggestion_file";

    /**
     * 客户拜访
     */
    public static final String VISIT = "visit";

    /**
     * 房本图片附件
     */
    public static final String ADDRESS_PROPERTY_OWNERSHIP_IMAGE = "address_property_ownership_image";

    /**
     * 房本文件附件
     */
    public static final String ADDRESS_PROPERTY_OWNERSHIP_FILE = "address_property_ownership_file";

    /**
     * 地址申请图片附件
     */
    public static final String ADDRESS_APPLY_OWNERSHIP_IMAGE = "address_apply_ownership_image";

    /**
     * 地址申请文件附件
     */
    public static final String ADDRESS_APPLY_OWNERSHIP_FILE = "address_apply_ownership_file";

    /**
     * 许可证
     */
    public static final String CUSTOMER_LICENSE = "customer_license";
}
