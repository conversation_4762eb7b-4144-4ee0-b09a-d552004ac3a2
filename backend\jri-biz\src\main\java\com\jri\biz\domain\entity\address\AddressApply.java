package com.jri.biz.domain.entity.address;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 地址申请
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("address_apply")
@ApiModel(value = "AddressApply对象", description = "地址申请")
public class AddressApply implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 供应商id
     */
    private Long propertyId;

    /**
     * 账单id
     */
    private Long paymentId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 地址成本
     */
    private String addressCost;

    /**
     * 毛利
     */
    private String grossProfit;

    /**
     * 不通过原因
     */
    private String reason;

    /**
     * 审批状态 0-待审批1-通过2-驳回
     */
    private String status;

    /**
     * 审批时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审批人
     */
    private String reviewPerson;

    /**
     * 备注
     */
    private String remark;

    /**
     * 区域id
     */
    private Long areaId;

    /**
     * 托管分类
     */
    private String hostingType;

}
