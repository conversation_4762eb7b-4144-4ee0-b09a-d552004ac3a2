package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerContact;
import com.jri.biz.domain.request.CustomerContactForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客户联系人对象转换
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Mapper
public interface CustomerContactConvert {
    CustomerContactConvert INSTANCE = Mappers.getMapper(CustomerContactConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerContact convert(CustomerContactForm form);

}