package com.jri.biz.domain.vo;

import com.jri.biz.domain.vo.turnoverStatement.TurnoverStatementMonthInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "客户账单详情VO")
public class CustomerBillDetailVO  implements Serializable {

    /**
     * 客户主表id
     */
    @ApiModelProperty("客户主表id")
    private Long ciId;

    /**
     * 当月营业额
     */
    @ApiModelProperty("当月营业额")
    private BigDecimal monthlyTurnover;

    @ApiModelProperty("当月记账营业额")
    private BigDecimal bookkeepingMonthlyTurnover;

    @ApiModelProperty("当月地址营业额")
    private BigDecimal addressMonthlyTurnover;

    @ApiModelProperty("收款总额")
    private BigDecimal totalPayment;

    /**
     * 已收款
     */
    @ApiModelProperty("已收款")
    private BigDecimal payment;

    /**
     * 总欠费合计
     */
    @ApiModelProperty("总欠费合计")
    private BigDecimal totalArrears;

    /**
     * 工本费欠费
     */
    @ApiModelProperty("工本费欠费")
    private BigDecimal productionOverdue;

    /**
     * 工商业务欠费
     */
    @ApiModelProperty("工商业务欠费")
    private BigDecimal commercialBusinessArrears;

    @ApiModelProperty("地址费欠费")
    private BigDecimal addressFeeArrears;

    @ApiModelProperty("代理记账费欠费")
    private BigDecimal agencyBookkeepingFeeArrears;

    @ApiModelProperty("总回款率")
    private BigDecimal totalReturnRate;


    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户经理
     */
    private String manger;
    @ApiModelProperty("主办会计")
    private String sponsorAccounting;

    @ApiModelProperty("财税顾问")
    private String counselor;

    @ApiModelProperty("客户成功")
    private String customerSuccess;

    @ApiModelProperty("欠费类型列表")
    private List<TurnoverStatementMonthInfoVO> arrearageAmonut;

    /**
     * 钱包金额
     */
    @ApiModelProperty(value = "钱包金额")
    private BigDecimal walletAmount;
}
