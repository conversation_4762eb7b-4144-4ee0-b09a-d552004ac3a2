package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerChangeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerChangeRecordListVO;
import com.jri.biz.domain.vo.CustomerChangeRecordVO;
import com.jri.biz.domain.request.CustomerChangeRecordQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 客户信息修改记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
public interface CustomerChangeRecordMapper extends BaseMapper<CustomerChangeRecord> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
     List<CustomerChangeRecordListVO> listPage(@Param("query") CustomerChangeRecordQuery query);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerChangeRecordVO getDetailById(@Param("id") Long id);

    /**
     * 详情
     *
     * @param ciId ciId
     */
    List<CustomerChangeRecordListVO> getDetailByCiId(@Param("ciId") Long ciId);
}
