package com.jri.biz.cus.domain.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 客资来源 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Data
@NoArgsConstructor
@ApiModel(value = "客资来源表单请求对象")
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "biz", defaultImpl = CusSourceForm.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = CusSourcePlatformForm.class, name = "平台"),
        @JsonSubTypes.Type(value = CusSourceDirectForm.class, name = "直投"),
        @JsonSubTypes.Type(value = CusSourceCustomerIntroductionForm.class, name = "客户介绍"),
        @JsonSubTypes.Type(value = CusSourceChannelForm.class, name = "渠道")
})
public abstract class CusSourceBaseForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("业务类型 平台 直投 客户介绍 渠道")
    private String biz;

    @ApiModelProperty("来源名称")
    private String name;

    @ApiModelProperty("显示顺序")
    private Integer sort;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("使用状态0-停用 1-启用")
    private String enable = "1";

}