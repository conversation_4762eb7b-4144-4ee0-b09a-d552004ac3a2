package com.jri.biz.domain.vo.job;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 视图对象
 *
 * <AUTHOR>
 * @since 2023-08-08
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="ContractAlertVO视图对象")
public class ContractAlertVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
