package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcShare;
import com.jri.biz.cus.domain.request.CusCcShareForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 线索/客户共享对象转换
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Mapper
public interface CusCcShareConvert {
    CusCcShareConvert INSTANCE = Mappers.getMapper(CusCcShareConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCcShare convert(CusCcShareForm form);

}