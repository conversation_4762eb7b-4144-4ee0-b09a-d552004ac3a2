package com.jri.biz.domain.request;

import com.jri.biz.domain.entity.BusinessProductActivity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 *  表单请求对象
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Data
@NoArgsConstructor
@ApiModel(value="产品表单请求对象")
public class BusinessProductForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @NotBlank(message = "产品名称不能为空")
    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品报价")
    private String quotation;

    @ApiModelProperty("是否在合同中定义0-否1-是")
    private String isInContract;

    @NotBlank(message = "收费类型不能为空")
    @ApiModelProperty("收费类型0-一次性收费 1-每年收费 2-每月收费")
    private String feeType;

    @ApiModelProperty("产品代码")
    private String code;

    @NotNull(message = "业务类型id不能为空")
    @ApiModelProperty("业务类型id")
    private Long typeId;

    @ApiModelProperty("排序号")
    private Integer sort;

    @ApiModelProperty("活动价是否启用0-否1-是")
    private String activityStatus;

    @ApiModelProperty("活动报价")
    private String activityQuotation;

    @ApiModelProperty("优惠时长(月)")
    private Integer activityDiscountTime;

    @ApiModelProperty("活动价列表")
    private List<BusinessProductActivity> activityList;

    @ApiModelProperty("是否给企业用户显示")
    private Boolean enterpriseShowFlag;

    @ApiModelProperty("使用状态0-停用 1-启用")
    private Boolean enable;

}
