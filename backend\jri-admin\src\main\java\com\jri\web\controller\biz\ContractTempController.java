package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.ContractTempForm;
import com.jri.biz.domain.request.ContractTempQuery;
import com.jri.biz.domain.vo.ContractTempListVO;
import com.jri.biz.domain.vo.ContractTempVO;
import com.jri.biz.domain.vo.TempFieldListVO;
import com.jri.biz.service.ContractTempService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 合同模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Validated
@RestController
@RequestMapping("/contractTemp")
@Api(tags = "合同模板")
public class ContractTempController {
    @Resource
    private ContractTempService contractTempService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<ContractTempListVO>> listPage(ContractTempQuery query) {
        return R.ok(contractTempService.listPage(query));
    }

    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<ContractTempVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(contractTempService.getDetailById(id));
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("保存数据")
    public R<Boolean> saveOrUpdate(@RequestBody @Valid ContractTempForm form) {
        return R.ok(contractTempService.saveOrUpdate(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(contractTempService.deleteById(id));
    }

    @DeleteMapping("/deleteByIds")
    @ApiOperation("批量删除")
    public R<Void> deleteByIds(@RequestParam("ids") List<Long> ids) {
        contractTempService.deleteByIds(ids);
        return R.ok();
    }

    @PostMapping("/enable")
    @ApiOperation("状态设置")
    public R<Void> enable(@RequestParam("id") Long id) {
        contractTempService.enable(id);
        return R.ok();
    }

    @GetMapping("/listMyCreate")
    @ApiOperation("我提交的")
    public R<IPage<ContractTempListVO>> listMyCreate(ContractTempQuery query) {
        return R.ok(contractTempService.listMyCreate(query));
    }

    @GetMapping("/listMyAudit")
    @ApiOperation("由我审批")
    public R<IPage<ContractTempListVO>> listMyAudit(ContractTempQuery query) {
        return R.ok(contractTempService.listMyAudit(query));
    }

    @GetMapping("/getFieldList")
    @ApiOperation("按合同类型获取字段列表")
    public R<List<TempFieldListVO>> getFieldList(@ApiParam("合同类型0-记账合同1-一次性合同2-地址服务协议合同") @RequestParam("contractType") String contractType) {
        return R.ok(contractTempService.getFieldList(contractType));
    }
}

