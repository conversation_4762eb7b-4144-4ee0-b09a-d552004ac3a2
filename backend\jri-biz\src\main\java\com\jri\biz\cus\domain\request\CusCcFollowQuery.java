package com.jri.biz.cus.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * 跟进记录查询类
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="跟进记录查询对象")
public class CusCcFollowQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索/客户id")
    @NotNull(message = "线索/客户id不能为空")
    private Long ccId;

    @ApiModelProperty("查询关键词")
    private String text;
}
