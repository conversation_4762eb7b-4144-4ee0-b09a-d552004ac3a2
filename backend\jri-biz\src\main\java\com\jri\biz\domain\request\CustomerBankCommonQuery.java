package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 一般户信息查询类
 *
 * <AUTHOR>
 * @since 2023-07-13
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="一般户信息查询对象")
public class CustomerBankCommonQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
