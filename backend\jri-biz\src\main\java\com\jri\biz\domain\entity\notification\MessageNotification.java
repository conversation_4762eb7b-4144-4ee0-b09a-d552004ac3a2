package com.jri.biz.domain.entity.notification;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("message_notification")
@ApiModel(value = "MessageNotification对象", description = "")
public class MessageNotification implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通知id")
      private Long id;

    @ApiModelProperty("发送范围type  1-全部 2-指定用户")
    private String scopeType;

    @ApiModelProperty("指定范围部门列表")
    private String scopeDepts;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
      @TableField(fill = FieldFill.INSERT)
      private LocalDateTime createTime;

    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
      @TableField(fill = FieldFill.INSERT_UPDATE)
      private LocalDateTime updateTime;

    @ApiModelProperty("逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;


}
