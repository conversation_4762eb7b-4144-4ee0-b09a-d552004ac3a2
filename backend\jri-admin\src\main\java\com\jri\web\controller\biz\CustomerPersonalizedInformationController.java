package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerPersonalizedInformationForm;
import com.jri.biz.domain.request.CustomerPersonalizedInformationQuery;
import com.jri.biz.domain.vo.CustomerPersonalizedInformationListVO;
import com.jri.biz.domain.vo.CustomerPersonalizedInformationVO;
import com.jri.biz.service.CustomerPersonalizedInformationService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Validated
@RestController
@RequestMapping("/customerPersonalizedInformation")
@Api(tags = "客户个性化信息")
public class CustomerPersonalizedInformationController {
    @Resource
    private CustomerPersonalizedInformationService customerPersonalizedInformationService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerPersonalizedInformationListVO>> listPage(CustomerPersonalizedInformationQuery query) {
        return R.ok(customerPersonalizedInformationService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerPersonalizedInformationVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerPersonalizedInformationService.getDetailById(id));
    }
    @GetMapping("/getByCiId")
    @ApiOperation("通过ciId获取详情")
    public R<CustomerPersonalizedInformationVO> getByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerPersonalizedInformationService.getByCiId(ciId).orElse(null));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Long> save(@RequestBody @Valid CustomerPersonalizedInformationForm form) {
        return R.ok(customerPersonalizedInformationService.add(form));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CustomerPersonalizedInformationForm form) {
        return R.ok(customerPersonalizedInformationService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerPersonalizedInformationService.deleteById(id));
    }
}

