package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerPersonalizedInformation;
import com.jri.biz.domain.request.CustomerPersonalizedInformationForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Mapper
public interface CustomerPersonalizedInformationConvert {
    CustomerPersonalizedInformationConvert INSTANCE = Mappers.getMapper(CustomerPersonalizedInformationConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerPersonalizedInformation convert(CustomerPersonalizedInformationForm form);

}