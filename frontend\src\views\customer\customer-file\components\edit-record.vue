<!--
 * @Description: 编辑记录
 * @Author: thb
 * @Date: 2023-07-14 15:49:44
 * @LastEditTime: 2023-07-14 16:25:07
 * @LastEditors: thb
-->
<template>
  <el-table :data="tableData">
    <el-table-column prop="createBy" label="操作人" />
    <el-table-column prop="createTime" label="操作时间" />
    <el-table-column prop="infoSection" label="信息板块" />
    <el-table-column prop="content" label="修改内容" />
  </el-table>
  <Pagination v-if="total > 0" :total="total" v-model:page="pages" @pagination="handlePageChange" />
</template>
<script setup>
import Pagination from '@/components/Pagination'
import { getCustomerChangeRecord } from '@/api/customer/file'
const props = defineProps({
  ciId: Number
})
// 获取所有的编辑记录
const tableData = ref([])
const total = ref(0)
const pages = ref(1)
const getRecords = async () => {
  const { data } = await getCustomerChangeRecord({
    ciId: props.ciId,
    pageNum: 1,
    pageSize: 10
  })
  tableData.value = data.records || []
  pages.value = data.current
  total.value = Number(data.total) || 0
}

// 切换页数或者页码
// <!-- emit('pagination', { page: val, limit: pageSize.value }) -->
const handlePageChange = async ({ page, limit }) => {
  const { data } = await getCustomerChangeRecord({
    ciId: props.ciId,
    pageNum: page,
    pageSize: limit
  })
  tableData.value = data.records || []
  total.value = Number(data.total) || 0
}
getRecords()
</script>
<style lang="scss" scoped>
.pagination-container {
  height: 35px;
}
</style>
