package com.jri.web.controller.biz;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jri.biz.domain.entity.Progress;
import com.jri.biz.domain.request.ProgressQuery;
import com.jri.biz.service.ProgressService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/6/6 15:09
 */
@Api(tags = "导入进度查询")
@RestController
@RequestMapping("progress")
public class ProgressController {

    private final ProgressService progressService;
    public ProgressController(ProgressService progressService){
        this.progressService = progressService;
    }

    @GetMapping("getDetailById")
    @ApiOperation(value = "根据id获取进度")
    public R<Progress> getDetailById(@RequestParam("id") Long id){
        return R.ok(progressService.getById(id));
    }

    @GetMapping("list")
    @ApiOperation(value = "获取列表")
    public R<Page<Progress>> list(ProgressQuery query){
        return R.ok(progressService.list(query));
    }

}
