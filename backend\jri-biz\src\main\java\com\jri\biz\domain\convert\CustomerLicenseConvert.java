package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerLicense;
import com.jri.biz.domain.request.CustomerLicenseForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Mapper
public interface CustomerLicenseConvert {
    CustomerLicenseConvert INSTANCE = Mappers.getMapper(CustomerLicenseConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerLicense convert(CustomerLicenseForm form);

}