package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="查询对象")
public class BusinessTypeQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务类型
     */
    private String typeName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 代码
     */
    private String code;

    /**
     * 合同类型
     */
    private String contractType;

    /**
     * 是否给企业用户显示
     */
    private Boolean enterpriseShowFlag;

    /**
     * 使用状态0-停用 1-启用
     */
    private Boolean enable;

}
