<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerTaxInformationMapper">
    <insert id="add" parameterType="CustomerTaxInformation">
        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
            select LAST_INSERT_ID()
        </selectKey>
        insert into customer_tax_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="identityNumber != null">identity_number,</if>
            <if test="taxRegistrationOrgan != null and taxRegistrationOrgan != ''">tax_registration_organ,</if>
            <if test="taxOrganAddress != null and taxOrganAddress != ''">tax_organ_address,</if>
            <if test="rateRegistration != null">rate_registration,</if>
            <if test="legalRealNameFlag != null and legalRealNameFlag != ''">legal_real_name_flag,</if>
            <if test="taxRealNameFlag != null">tax_real_name_flag,</if>
            <if test="individualCheckFlag != null">individual_check_flag,</if>
            <if test="onlineRevenueRegistrationFlag != null">online_revenue_registration_flag,</if>
            <if test="reservedPhoneNumber != null">reserved_phone_number,</if>
            <if test="tripleAgreementFlag != null">triple_agreement_flag,</if>
            <if test="identificationMethodFlag != null">identification_method_flag,</if>
            <if test="certificateAccount != null">certificate_account,</if>
            <if test="certificatePassword != null">certificate_password,</if>
            <if test="drawingSheetFlag != null">drawing_sheet_flag,</if>
            <if test="invoiceFlag != null">invoice_flag,</if>
            <if test="invoiceSealFlag != null">invoice_seal_flag,</if>
            <if test="drawingSheetDept != null">drawing_sheet_dept,</if>
            <if test="invoiceDept != null">invoice_dept,</if>
            <if test="invoiceSealDept != null">invoice_seal_dept,</if>
            <if test="invoiceLimit != null">invoice_limit,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="drawingSheetType != null">drawing_sheet_type,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="identityNumber != null">#{identityNumber},</if>
            <if test="taxRegistrationOrgan != null and taxRegistrationOrgan != ''">#{taxRegistrationOrgan},</if>
            <if test="taxOrganAddress != null and taxOrganAddress != ''">#{taxOrganAddress},</if>
            <if test="rateRegistration != null">#{rateRegistration},</if>
            <if test="legalRealNameFlag != null and legalRealNameFlag != ''">#{legalRealNameFlag},</if>
            <if test="taxRealNameFlag != null">#{taxRealNameFlag},</if>
            <if test="individualCheckFlag != null">#{individualCheckFlag},</if>
            <if test="onlineRevenueRegistrationFlag != null">#{onlineRevenueRegistrationFlag},</if>
            <if test="reservedPhoneNumber != null">#{reservedPhoneNumber},</if>
            <if test="tripleAgreementFlag != null">#{tripleAgreementFlag},</if>
            <if test="identificationMethodFlag != null">#{identificationMethodFlag},</if>
            <if test="certificateAccount != null">#{certificateAccount},</if>
            <if test="certificatePassword != null">#{certificatePassword},</if>
            <if test="drawingSheetFlag != null">#{drawingSheetFlag},</if>
            <if test="invoiceFlag != null">#{invoiceFlag},</if>
            <if test="invoiceSealFlag != null">#{invoiceSealFlag},</if>
            <if test="drawingSheetDept != null">#{drawingSheetDept},</if>
            <if test="invoiceDept != null">#{invoiceDept},</if>
            <if test="invoiceSealDept != null">#{invoiceSealDept},</if>
            <if test="invoiceLimit != null">#{invoiceLimit},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="drawingSheetType != null">#{drawingSheetType},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>
    <delete id="deleteById">
        update customer_tax_information set is_deleted='1',update_by=#{updateBy} where id= #{id}
    </delete>


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerTaxInformationListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerTaxInformationVO">
      select * from customer_tax_information where id =#{id}
    </select>
    <select id="getDetailByCiId" resultType="com.jri.biz.domain.vo.CustomerTaxInformationVO">
        select * from customer_tax_information where is_deleted=0 and ci_id=#{ciId} limit 1
    </select>
</mapper>
