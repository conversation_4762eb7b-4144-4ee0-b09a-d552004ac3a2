package com.jri.web.service;

import com.jri.biz.constants.ProgressType;
import com.jri.biz.service.CustomerInformationService;
import com.jri.biz.service.ProgressService;
import com.jri.framework.security.context.AuthenticationContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2023/6/6 14:00
 */
@SpringBootTest
class CustomerInformationServiceTest {

    @Autowired
    private AuthenticationManager authenticationManager;


    @Autowired
    private CustomerInformationService service;
    @Autowired
    private ProgressService progressService;

    @BeforeEach
    void before() {
        var authenticationToken = new UsernamePasswordAuthenticationToken("admin", "admin123");
        AuthenticationContextHolder.setContext(authenticationToken);
        var authentication = authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }

//    @Test
//    void upload() {
//        var path = System.getenv("USERPROFILE");
//        path = path + File.separator + "Desktop" + File.separator + "客户导出20230601142304.xls";
//        var file = new File(path);
//        try (var fis = new FileInputStream(file)) {
//            var id = progressService.create(ProgressType.CUSTOMER_UPLOAD);
//
//            service.upload(fis, id);
//            Thread.sleep(10000L);
//        } catch (IOException e) {
//            e.printStackTrace();
//
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//    }
}