package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusCcBusiness;
import com.jri.biz.cus.domain.request.CusCcBusinessForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机对象转换
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Mapper
public interface CusCcBusinessConvert {
    CusCcBusinessConvert INSTANCE = Mappers.getMapper(CusCcBusinessConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusCcBusiness convert(CusCcBusinessForm form);

}