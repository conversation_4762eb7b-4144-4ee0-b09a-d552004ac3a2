package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerTaxExportDetail;
import com.jri.biz.domain.request.CustomerTaxExportDetailForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Mapper
public interface CustomerTaxExportDetailConvert {
    CustomerTaxExportDetailConvert INSTANCE = Mappers.getMapper(CustomerTaxExportDetailConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerTaxExportDetail convert(CustomerTaxExportDetailForm form);

}