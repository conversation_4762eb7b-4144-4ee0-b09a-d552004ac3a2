package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;
import java.io.Serial;
import java.io.Serializable;

/**
 * 客户信息修改记录查询类
 *
 * <AUTHOR>
 * @since 2023-07-14
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="客户信息修改记录查询对象")
public class CustomerChangeRecordQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户信息id
     */
    private Long ciId;
}
