package com.jri.biz.domain.request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import com.jri.common.core.domain.BaseQuery;

import javax.validation.constraints.NotBlank;
import java.awt.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * 流程查询类
 *
 * <AUTHOR>
 * @since 2023-06-16
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="流程查询对象")
public class BizFlowQuery extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 使用状态0-关闭1-启用
     */
    private String enable;

    /**
     * 类型0-模板审批流程1-合同借阅2-合同审批
     */
    @NotBlank(message = "类型不能为空")
    private String type;
}
