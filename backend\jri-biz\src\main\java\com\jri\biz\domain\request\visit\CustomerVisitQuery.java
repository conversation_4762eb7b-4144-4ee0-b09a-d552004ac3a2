package com.jri.biz.domain.request.visit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 查询类
 *
 * <AUTHOR>
 * @since 2023-07-28
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="查询对象")
public class CustomerVisitQuery extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("计划标题")
    private String planName;

    @ApiModelProperty("关联客户No")
    private String customerNo;

    @ApiModelProperty("关联客户")
    private String customerName;

    @ApiModelProperty("计划拜访日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDate;

    @ApiModelProperty("计划拜访日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDateStart;

    @ApiModelProperty("计划拜访日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDateEnd;

    @ApiModelProperty("实际拜访时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date actualPlanDateStart;

    @ApiModelProperty("实际拜访时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date actualPlanDateEnd;

    @ApiModelProperty("拜访方式")
    private String planVisitMethod;

    @ApiModelProperty("拜访人id")
    private Long visitorId;

    @ApiModelProperty("关键词")
    private String keyword;

    @ApiModelProperty("排序列 计划时间-plan_visit_date 完成状态-status 不限排序 no_limit")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("选中导出记录id")
    private List<Long> idList;

}
