package com.jri.biz.mapper;

import com.jri.biz.domain.entity.CustomerTaxInformation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.domain.vo.CustomerTaxInformationListVO;
import com.jri.biz.domain.vo.CustomerTaxInformationVO;
import com.jri.biz.domain.request.CustomerTaxInformationQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface CustomerTaxInformationMapper extends BaseMapper<CustomerTaxInformation> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CustomerTaxInformationListVO> listPage(@Param("query") CustomerTaxInformationQuery query, Page<CustomerTaxInformationListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CustomerTaxInformationVO getDetailById(@Param("id") Long id);

    void add(CustomerTaxInformation customerTaxInformation);

    CustomerTaxInformationVO getDetailByCiId(Long ciId);

    Boolean deleteById(@Param("id") Long id,@Param("updateBy") String updateBy);
}
