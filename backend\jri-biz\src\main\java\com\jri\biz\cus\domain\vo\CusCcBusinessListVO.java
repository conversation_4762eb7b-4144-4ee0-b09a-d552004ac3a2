package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机视图列表对象
 *
 * <AUTHOR>
 * @since 2023-08-18
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCcBusinessListVO视图列表对象")
public class CusCcBusinessListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("商机名称")
    private String name;

    @ApiModelProperty("客户id")
    private Long ccId;

    @ApiModelProperty("预计成交金额")
    private String expectAmount;

    @ApiModelProperty("预计成交时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("销售阶段")
    private String stage;

    @ApiModelProperty("阶段百分比")
    private String stagePercentage;

    @ApiModelProperty("实际成交金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("赢单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime winTime;

    @ApiModelProperty("公司名称(客户名称)")
    private String companyName;

    @ApiModelProperty("当前处理人")
    private Long currentUserId;

    @ApiModelProperty("当前处理人姓名(跟进人)")
    private String currentUserName;

    @ApiModelProperty("跟进状态0-未跟进 1-跟进中 2-赢单 3-输单")
    private String followStatus;
}