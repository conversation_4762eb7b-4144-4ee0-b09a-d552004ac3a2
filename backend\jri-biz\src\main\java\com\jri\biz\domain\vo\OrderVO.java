package com.jri.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单视图对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="OrderVO视图对象")
public class OrderVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("工单编号")
    private String orderNo;

    @ApiModelProperty("客户id")
    private Long ciId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty("紧急状态0-一般 1-紧急")
    private String isUrgent;

    @ApiModelProperty("期望完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectTime;

    @ApiModelProperty("工单类型id")
    private Long orderTypeId;

    @ApiModelProperty("工单类型名称")
    private String orderTypeName;

    @ApiModelProperty("补充说明")
    private String supplementExplain;

    @ApiModelProperty("工单标题")
    private String orderTitle;

    @ApiModelProperty("地区")
    private String address;

    @ApiModelProperty("工单内容")
    private String content;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("完成反馈/回退说明")
    private String feedbackOrDescription;

    @ApiModelProperty("指派给")
    private Long executor;

    @ApiModelProperty("工单状态0-待完成 1-完成 2-回退")
    private String orderStatus;

    @ApiModelProperty("完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty("工单记录列表")
    private List<OrderRecordListVO> recordList;

    @ApiModelProperty("附件列表")
    private List<CommonBizFile> fileList;

    @ApiModelProperty("清税证明")
    private List<CommonBizFile> taxClearanceList;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("行政区划id")
    private Long administrativeId;

    @ApiModelProperty("行政区划名称")
    private Long administrativeName;
}