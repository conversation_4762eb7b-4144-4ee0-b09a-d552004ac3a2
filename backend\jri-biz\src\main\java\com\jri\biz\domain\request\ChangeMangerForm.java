package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@ApiModel(value = "变更经理")
public class ChangeMangerForm {

    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @ApiModelProperty(value = "客户经理")
    @NotBlank(message = "客户经理不能为空")
    private String manger;

    @ApiModelProperty(value = "客户经理id")
    private Long mangerUserId;
}
