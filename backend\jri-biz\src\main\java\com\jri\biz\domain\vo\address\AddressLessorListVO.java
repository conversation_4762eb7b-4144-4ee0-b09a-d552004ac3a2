package com.jri.biz.domain.vo.address;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 出租人视图列表对象
 *
 * <AUTHOR>
 * @since 2024-06-04
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "AddressLessorListVO视图列表对象")
public class AddressLessorListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("出租人姓名")
    private String lessorName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账户")
    private String bankAccount;

    @ApiModelProperty("房本数量")
    private Long propertyCount;

}