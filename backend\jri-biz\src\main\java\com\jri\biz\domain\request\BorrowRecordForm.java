package com.jri.biz.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 借阅记录 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-11
 */

@Data
@NoArgsConstructor
@ApiModel(value="借阅记录表单请求对象")
public class BorrowRecordForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("合同id")
    private Long mainId;

    @ApiModelProperty("借阅人")
    private Long userId;

    @ApiModelProperty("借阅到期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expirationTime;

    @ApiModelProperty("借阅事由")
    private String borrowReason;
}
