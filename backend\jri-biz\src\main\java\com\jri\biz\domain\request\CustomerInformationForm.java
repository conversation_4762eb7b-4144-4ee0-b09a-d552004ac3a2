package com.jri.biz.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Data
@NoArgsConstructor
@ApiModel(value = "基础信息请求对象")
public class CustomerInformationForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户名称
     */
    @NotEmpty(message = "客户名称不能为空")
    private String customerName;


    /**
     * 客户经理
     */
    private String manger;

    @ApiModelProperty(value = "客户经理id")
    private Long mangerUserId;

    /**
     * 所属分公司
     */
    private String branchOffice;

    /**
     * 实际经营地址
     */
    private String address;

    /**
     * 企业联系信息备注
     */
    private String informationMark;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 客户性质
     */
    private String customerProperty;

    /**
     * 从事行业
     */
    private String industry;

    /**
     * 不收费原因备注
     */
    private String nofeeReasonMark;

    private Boolean isDeleted;

    private int discard;

    private String discardReason;

    //========以下附表内容=============


    /**
     * 联系人姓名
     */
    @NotEmpty(message = "联系人不能为空")
    private String contactPerson;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 营业执照路径
     */
    private String businessLicense;


    @ApiModelProperty("营业执照")
    private List<CommonBizFile> businessFileList;

//    @ApiModelProperty("客户开票资料")
//    private List<CommonBizFile> invoiceInfoFileList;

    @ApiModelProperty("不收费原因")
    private String nofeeReason;

    @ApiModelProperty("下户表")
    private List<CommonBizFile> interviewFileList;

    /**
     * 主联系人id
     */
    private Long contractMainId;

    @ApiModelProperty("财税顾问(开票员)")
    private String counselor;

    @ApiModelProperty("财税顾问id")
    private Long counselorUserId;

    @ApiModelProperty("客户成功")
    private String customerSuccess;

    @ApiModelProperty("客户成功id")
    private Long customerSuccessUserId;


    @ApiModelProperty("主办会计")
    private String sponsorAccounting;

    @ApiModelProperty("主办会计id")
    private Long sponsorAccountingUserId;

    /**
     * 职位
     */
    private String post;

    /**
     * 微信
     */
    private String wx;

    /**
     * QQ
     */
    private String qq;

    /**
     * 性别0-未知 1-男 2-女
     */
    private String sex;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 是否决策人0-否1-是
     */
    private Integer isLeader;


    /**
     * 是否常用联系人0-否1-是
     */
    private Integer isOften;

    @ApiModelProperty(value = "企业认定")
    private List<String> companyIdentificationList;

    @ApiModelProperty("经度")
    private BigDecimal lng;

    @ApiModelProperty("纬度")
    private BigDecimal lat;
}
