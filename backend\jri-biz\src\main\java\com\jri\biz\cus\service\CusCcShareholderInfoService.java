package com.jri.biz.cus.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.constants.BizType;
import com.jri.biz.cus.domain.convert.CusCcShareholderInfoConvert;
import com.jri.biz.cus.domain.entity.CusCcShareholderInfo;
import com.jri.biz.cus.domain.request.CusCcShareholderInfoForm;
import com.jri.biz.cus.domain.request.CusCcShareholderInfoQuery;
import com.jri.biz.cus.domain.vo.CusCcShareholderInfoListVO;
import com.jri.biz.cus.domain.vo.CusCcShareholderInfoVO;
import com.jri.biz.cus.mapper.CusCcShareholderInfoMapper;
import com.jri.biz.service.CommonBizFileService;
import com.jri.common.core.domain.CommonBizFile;
import com.jri.common.utils.bean.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 股东信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Service
public class CusCcShareholderInfoService extends ServiceImpl<CusCcShareholderInfoMapper, CusCcShareholderInfo> {

    @Resource
    private CommonBizFileService commonBizFileService;
    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CusCcShareholderInfoListVO> listPage(CusCcShareholderInfoQuery query) {
        var page = new Page<CusCcShareholderInfoListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
    * 根据id获取详情
    *
    * @param id id
    * @return 结果
    */
    public CusCcShareholderInfoVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
    * 保存
    *
    * @param form 表单
    */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(CusCcShareholderInfoForm form) {
        CusCcShareholderInfo cusCcShareholderInfo = CusCcShareholderInfoConvert.INSTANCE.convert(form);
        saveOrUpdate(cusCcShareholderInfo);

        // 附件处理
        List<CommonBizFile> shareholderFileList = form.getShareholderFileList();
        if (ObjectUtil.isNotEmpty(shareholderFileList)) {
            shareholderFileList.forEach(item -> {
                item.setMainId(cusCcShareholderInfo.getId());
                item.setBizType(BizType.CUS_SHAREHOLDER_DOCUMENT);
            });
            commonBizFileService.saveBatch(shareholderFileList);
        }
    }

    /**
    * 根据id删除
    *
    * @param id 主键id
    */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        removeById(id);
        commonBizFileService.deleteByMainIdAndBizType(id, BizType.CUS_SHAREHOLDER_DOCUMENT);
    }

    /**
     * 根据ccId删除
     *
     * @param ccId ccId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTaskId(Long ccId) {
        List<CusCcShareholderInfo> list = list(Wrappers.lambdaQuery(CusCcShareholderInfo.class)
                .eq(CusCcShareholderInfo::getCcId, ccId));
        list.forEach(item -> deleteById(item.getId()));
    }

    /**
     * 保存股东信息
     *
     * @param shareholderInfoList 股东信息列表
     * @param ccId 客户id
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBatch(List<CusCcShareholderInfoForm> shareholderInfoList, Long ccId) {
        // 先删除
        deleteByTaskId(ccId);

        // 保存
        if (ObjectUtil.isEmpty(shareholderInfoList)) {
            return;
        }
        shareholderInfoList.forEach(item -> {
            item.setCcId(ccId);
            saveOrUpdate(item);
        });
    }

    /**
     * 根据ccId查询
     *
     * @param ccId 客户id
     */
    public List<CusCcShareholderInfoForm> getListByCcId(Long ccId) {
        List<CusCcShareholderInfo> list = list(Wrappers.lambdaQuery(CusCcShareholderInfo.class)
                .eq(CusCcShareholderInfo::getCcId, ccId));
        List<CusCcShareholderInfoForm> res = new ArrayList<>();
        list.forEach(item -> {
            CusCcShareholderInfoForm form = new CusCcShareholderInfoForm();
            BeanUtils.copyBeanProp(form, item);
            res.add(form);
        });
        res.forEach(item -> item.setShareholderFileList(commonBizFileService.selectByMainIdAndBizType(item.getId(), BizType.CUS_SHAREHOLDER_DOCUMENT)));
        return res;
    }
}
