package com.jri.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.jri.common.core.domain.model.LoginBody;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.annotation.Resource;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @since 2023/6/5 9:55
 */
@SpringBootTest
class SysLoginControllerTest {

    @Resource
    private SysLoginController controller;

    @Test
    void login() throws Exception {
        var body = new LoginBody();
        body.setUsername("admin");
        body.setPassword("123456");
        var mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        var request = MockMvcRequestBuilders.post("/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(body));
        var response = mockMvc.perform(request)
                .andReturn()
                .getResponse()
                .getContentAsString(StandardCharsets.UTF_8);
        System.out.println(response);
    }
}