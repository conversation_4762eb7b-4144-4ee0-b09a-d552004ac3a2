package com.jri.biz.cus.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.biz.cus.domain.entity.CusCcTags;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


/**
 * 线索/客户信息 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-08-10
 */

@Data
@NoArgsConstructor
@ApiModel(value = "线索客户信息表单请求对象")
public class CusCustomerOrClueForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("线索来源(客户来源)")
    private String source;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @ApiModelProperty("介绍客户id")
    private Long introductionCustomerId;

    @ApiModelProperty("公司名称(客户名称)")
    private String companyName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("行业")
    private String industry;

    @ApiModelProperty("地区")
    private String area;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("税务性质")
    private String taxNature;

    @ApiModelProperty("首次跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate firstFollowTime;

    @ApiModelProperty("客户等级")
    private String level;

    @ApiModelProperty("公海id")
    private Long seaId;

    @ApiModelProperty("是否正在公海中0-否1-是")
    private String isSea;

    @ApiModelProperty("主联系人id")
    private Long mainContactId;

    @ApiModelProperty("类型0-线索1-客户2-线索关联已有客户")
    private String type;

    @ApiModelProperty("录入方式0-手动录入1-公海录入")
    private String entryType;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("产品id")
    private Long productId;


    // *************联系人表******************

    @ApiModelProperty("姓名")
    private String contactName;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("职位")
    private String post;

    @ApiModelProperty("微信")
    private String wx;

    @ApiModelProperty("QQ")
    private String qq;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("生日")
    private String birthday;

    //************标签*************

    @ApiModelProperty("标签列表")
    private List<CusCcTags> tags;
}
