package com.jri.biz.cus.domain.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线索申诉记录表查询类
 *
 * <AUTHOR>
 * @since 2024-01-10
 */

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "线索申诉记录表查询对象")
public class ClueAppealRecordQuery extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索名称")
    private String clueName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建人用户姓名")
    private String createUserName;

    @ApiModelProperty("排序列")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @ApiModelProperty("申述原因")
    private String remark;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

}
