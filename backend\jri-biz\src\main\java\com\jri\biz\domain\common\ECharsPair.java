package com.jri.biz.domain.common;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

/**
 * 针对EChars 饼图结构
 *
 * <AUTHOR>
 * @since 2022/9/16 11:52
 */
public class ECharsPair<K, V> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    protected K name;
    protected V value;

    /**
     * 构建{@code Pair}对象
     *
     * @param <K>   键类型
     * @param <V>   值类型
     * @param name  键
     * @param value 值
     * @return {@code Pair}
     * @since 5.4.3
     */
    public static <K, V> ECharsPair<K, V> of(K name, V value) {
        return new ECharsPair<>(name, value);
    }

    /**
     * 构造
     *
     * @param name  键
     * @param value 值
     */
    public ECharsPair(K name, V value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 获取键
     *
     * @return 键
     */
    public K getName() {
        return this.name;
    }

    /**
     * 获取值
     *
     * @return 值
     */
    public V getValue() {
        return this.value;
    }

    @Override
    public java.lang.String toString() {
        return "Pair [name=" + name + ", value=" + value + "]";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o instanceof ECharsPair<?, ?> pair) {
            return Objects.equals(getName(), pair.getName()) &&
                    Objects.equals(getValue(), pair.getValue());
        }
        return false;
    }

    @Override
    public int hashCode() {
        //copy from 1.8 HashMap.Node
        return Objects.hashCode(name) ^ Objects.hashCode(value);
    }
}
