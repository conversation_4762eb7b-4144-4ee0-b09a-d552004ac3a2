package com.jri.biz.constants;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/6 15:11
 */

public final class ProgressType {
    /**
     * 导入企业
     */
    public static final String CUSTOMER_UPLOAD = "customer_upload";

    /**
     * 导入账单
     */
    public static final String PAYMENT_UPLOAD = "payment_upload";

    /**
     * 材料导入
     */
    public static final String MATERIAL_UPLOAD = "material_upload";

    /**
     * 导出企业
     */
    public static final String CUSTOMER_DOWNLOAD = "customer_download";

    /**
     * 导出工单
     */
    public static final String ORDER_DOWNLOAD = "order_download";

    /**
     * 导出工商办证
     */
    public static final String LICENSE_BIZ_TASK_DOWNLOAD = "license_biz_task_download";

    /**
     * 客户流动统计导出
     */
    public static final String CUSTOMER_USER_FLOW_ANALYSE_DOWNLOAD = "customer_user_flow_analyse_download";

    /**
     * 拜访计划导出
     */
    public static final String VISIT_DOWNLOAD = "visit_download";

    /**
     * 线索导出
     */
    public static final String CLUE_DOWNLOAD = "clue_download";

    /**
     * 合同导出
     */
    public static final String CONTRACT_DOWNLOAD = "contract_download";

    /**
     * 风险客户导出
     */
    public static final String RISK_CUSTOMER_DOWNLOAD = "risk_customer_download";

    /**
     * 导入税务信息
     */
    public static final String CUSTOMER_TAX = "customer_tax";

    /**
     * 导入账单信息
     */
    public static final String PAYMENT = "payment";

    /**
     * 导入企业联系人
     */
    public static final String CUSTOMER_CONTACT_UPLOAD = "CUSTOMER_CONTACT_UPLOAD";

    /**
     * 导入
     */
    public static final String UPLOAD = "upload";

    /**
     * 导出
     */
    public static final String DOWNLOAD = "download";

    private static final ArrayList<String> LIST = new ArrayList<>();

    static {
        LIST.add(CUSTOMER_UPLOAD);
    }

    public static List<String> getAll() {
        return LIST;
    }
}
