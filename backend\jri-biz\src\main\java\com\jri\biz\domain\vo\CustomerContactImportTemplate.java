package com.jri.biz.domain.vo;

import com.jri.common.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 联系人导入导出模板
 *
 * <AUTHOR>
 * @since 2023/12/12 10:05
 */
@Getter
@Setter
public class CustomerContactImportTemplate implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Excel(name = "企业名称")
    private String customerName;

    @Excel(name = "企业编号")
    private String customerNo;

    @Excel(name = "联系人")
    private String contactPerson;

    @Excel(name = "手机号")
    private String contactPhone;

}
