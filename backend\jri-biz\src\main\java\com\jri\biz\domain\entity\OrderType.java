package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * <p>
 * 工单类型
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("order_type")
@ApiModel(value = "OrderType对象", description = "工单类型")
public class OrderType implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 名称
     */
    private String typeName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态0-停用 1-正常
     */
    private String status;

    /**
     * 补充说明
     */
    private String supplementExplain;

    /**
     * 备注
     */
    private String remark;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;
}
