package com.jri.biz.cus.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="CusCustomerListVO视图列表对象")
public class CusCustomerListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @Excel(name = "客户名称")
    @ApiModelProperty("公司名称(客户名称)")
    private String companyName;

    // *************联系人表******************

    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("线索来源(客户来源)")
    private String source;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @Excel(name = "客户来源")
    @ApiModelProperty("来源名称")
    private String sourceName;

    @Excel(name = "客户等级")
    @ApiModelProperty("客户等级")
    private String level;

    @Excel(name = "跟进人")
    @ApiModelProperty("跟进人")
    private String currentUserName;

    @Excel(name = "跟进状态", readConverterExp = "0=未跟进,1=跟进中,2=已转企业")
    @ApiModelProperty("跟进状态0-未跟进1-跟进中2-已转企业")
    private String followStatus;

    @Excel(name = "成为客户时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("成为客户时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime becomeTime;

    @Excel(name = "最近跟进时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFollowTime;

    //************标签*************

    @Excel(name = "标签")
    @ApiModelProperty("标签名")
    private String tagsName;

    @Excel(name = "商机数量")
    @ApiModelProperty("商机数量")
    private Long opportunityNum;

    @Excel(name = "商机分布")
    @ApiModelProperty("商机分布")
    private String opportunityDistribution;

    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;

    @Excel(name = "税务性质")
    @ApiModelProperty("税务性质")
    private String taxNature;

    @Excel(name = "最近修改时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Excel(name = "创建人")
    @ApiModelProperty("创建者")
    private String createBy;

    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    //************商机*************

    @ApiModelProperty("赢单数量")
    private Long winNum = 0L;

    @ApiModelProperty("输单数量")
    private Long loseNum = 0L;

    @ApiModelProperty("其它数量")
    private Long otherNum = 0L;

    @ApiModelProperty("保护开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime protectionStartTime;

    @ApiModelProperty("是否申诉")
    private Boolean appealFlag;

    @ApiModelProperty("产品名称")
    private String productName;

    public Long getOpportunityNum() {
        Long winNum = getWinNum();
        Long loseNum = getLoseNum();
        Long otherNum = getOtherNum();
        return winNum + loseNum + otherNum;
    }
}
