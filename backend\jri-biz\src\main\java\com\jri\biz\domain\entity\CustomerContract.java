package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_contract")
@ApiModel(value = "CustomerContract对象", description = "合同")
public class CustomerContract implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合同id
     */
    @TableId(value = "contract_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("合同id")
    private Long contractId;

    /**
     * 客户信息主表id
     */
    @ApiModelProperty("客户信息主表id")
    private Long ciId;

    /**
     * 合同名称
     */
    @ApiModelProperty("合同名称")
    private String contractName;

    /**
     * 合同类型0-记账合同1-一次性合同2-地址服务协议合同
     */
    @ApiModelProperty("合同类型0-记账合同1-一次性合同2-地址服务协议合同")
    private String contractType;

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    private String contractNo;

    /**
     * 合同状态0-待审批 1-通过 2-驳回
     */
    @ApiModelProperty("合同状态0-待审批 1-通过 2-驳回")
    private String contractStatus;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contactPerson;

    /**
     * 联系人电话
     */
    @ApiModelProperty("联系人电话")
    private String contactPhone;

    /**
     * 0-录入合同1-模板创建合同
     */
    @ApiModelProperty("0-录入合同1-模板创建合同")
    private String type;

    @ApiModelProperty("纳税人类型")
    private String taxpayerType;

    @ApiModelProperty("网上申报0-零申报 1-非零申报")
    private String declareType;

    @ApiModelProperty("起始时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date endTime;

    @ApiModelProperty("服务月份")
    private Integer monthNum;

    @ApiModelProperty("销售收入")
    private String salesRevenue;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("服务费")
    private BigDecimal serviceCost;

    @ApiModelProperty("其它费用")
    private BigDecimal otherCost;

    @ApiModelProperty("合同总金额(中文大写)")
    private String totalCostCn;

    @ApiModelProperty("合同总金额")
    private BigDecimal totalCost;

    @ApiModelProperty("工本费")
    private String productionCost;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("终止时间")
    private LocalDateTime terminationTime;

    @ApiModelProperty("其他补充说明")
    private String otherRemark;

    @ApiModelProperty("原合同id")
    private Long originId;

    @ApiModelProperty("业务类型0-新增合同1-变更合同")
    private String bizType;

    @ApiModelProperty("身份证号码")
    private String identityNumber;

    @ApiModelProperty("拟注册成立企业名称")
    private String companyName;

    @ApiModelProperty("拟注册成立企业法定代表人姓名")
    private String legalPerson;

    @ApiModelProperty("拟注册成立企业法定代表人联系电话")
    private String legalPhone;

    @ApiModelProperty("托管住所地址")
    private String custodyAddress;

    @ApiModelProperty("变更原因")
    private String changeReason;

    @ApiModelProperty("html字符串")
    private String htmlStr;

    @ApiModelProperty("模板id")
    private Long tempId;

    @ApiModelProperty("是否意向合同0-否1-是")
    private String isIntention;

    @ApiModelProperty("所属公司联系人")
    private String companyPerson;

    @ApiModelProperty("所属公司联系电话")
    private String companyPhone;

    @ApiModelProperty("所属公司地址")
    private String companyAddress;

    @ApiModelProperty("是否建账0-否1-是")
    private String isEstablish;

    @ApiModelProperty("账册软件费")
    private BigDecimal softwareFee;

    @ApiModelProperty("以后每年")
    private BigDecimal everyYear;

    @ApiModelProperty("办理人联系地址")
    private String contactAddress;

    @ApiModelProperty("乙方账号信息")
    private String accountNumber;

    @ApiModelProperty("归档号")
    private String documentNo;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("活动报价")
    private String activityQuotation;

    @ApiModelProperty("活动时长(月)")
    private Integer activityDiscountTime;

    @ApiModelProperty("备注")
    private String activityRemark;

    @ApiModelProperty("活动txt")
    private String activityTxt;

    @ApiModelProperty("人事薪酬服务")
    private String payrollService;

    @ApiModelProperty(value = "所属分公司")
    private String branchOffice;

    @ApiModelProperty("变更科目")
    private String changeSubject;

    @ApiModelProperty("价格变动标记 1表示变动 0表示未变动 默认为0")
    private Boolean priceChangeFlag;

    @ApiModelProperty("签字标记")
    private Boolean signFlag;

    @ApiModelProperty(value = "优惠类型")
    private String discount;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty("优惠时长(月)")
    private Integer discountTime;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "金额优惠折扣率")
    private BigDecimal discountRate;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "时长优惠折扣率")
    private BigDecimal discountTimeRate;

    @ApiModelProperty(value = "变更开始时间")
    private Date changeStartTime;

    @ApiModelProperty(value = "财税顾问id")
    private Long mangerUserId;

    @ApiModelProperty(value = "开票员id")
    private Long counselorUserId;

    @ApiModelProperty(value = "客户成功id")
    private Long customerSuccessUserId;

    @ApiModelProperty(value = "主办会计id")
    private Long sponsorAccountingUserId;

}
