package com.jri.biz.cus.domain.convert;

import com.jri.biz.cus.domain.entity.CusSourceDirect;
import com.jri.biz.cus.domain.request.CusSourceDirectForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客资来源 直投 关联对象转换
 *
 * <AUTHOR>
 * @since 2023-12-25
 */

@Mapper
public interface CusSourceDirectConvert {
    CusSourceDirectConvert INSTANCE = Mappers.getMapper(CusSourceDirectConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CusSourceDirect convert(CusSourceDirectForm form);

}