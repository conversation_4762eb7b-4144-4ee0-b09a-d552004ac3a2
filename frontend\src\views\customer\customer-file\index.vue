<!--
 * @Description: 客户档案index
 * @Author: thb
 * @Date: 2023-05-25 09:11:14
 * @LastEditTime: 2024-03-26 08:59:51
 * @LastEditors: thb
-->
<template>
  <div class="main-wrap">
    <dataList ref="staticsRef" @on-search="handleSearch" />
    <ProTable
      ref="proTable"
      title="企业档案"
      :init-param="initParam"
      :columns="columns"
      :toolButton="true"
      rowKey="customerId"
      :request-api="getCustomers"
      :transformRequestParams="transformRequestParams"
      @sort-change="sortChange"
      @on-reset="searchReset"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.discard" @change="handleRadioChange">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{
            item.dictLabel
          }}</el-radio-button>
        </el-radio-group></template
      >
      <template #customerName="{ row }">
        <span class="blue-text" @click="showDetail(row.customerId)">{{ row.customerName }}</span>
      </template>
      <template #receiptCardFlag="{ row }">
        <span v-if="row.receiptCardFlag !== null">
          {{ row.receiptCardFlag === '0' ? '无' : '有' }}
        </span>
        <span v-else> -- </span>
      </template>
      <template #socialAccountFlag="{ row }">
        <span>{{ row.socialAccountFlag === '1' ? '是' : '否' }}</span>
      </template>
      <template #fundAccountFlag="{ row }">
        <span>
          {{ row.fundAccountFlag === '1' ? '是' : '否' }}
        </span>
      </template>
      <template #taxRegistrationOrgan="{ row }">
        <span>
          {{
            row.taxRegistrationOrgan?.split(',').length
              ? row.taxRegistrationOrgan.split(',')[row.taxRegistrationOrgan.split(',').length - 1]
              : ''
          }}</span
        >
      </template>
      <template #taxRealNameFlag="{ row }">
        <span>
          {{ row.taxRealNameFlag ? '是' : '否' }}
        </span>
      </template>

      <template #individualCheckFlag="{ row }">
        <span>
          {{ row.individualCheckFlag ? '是' : '否' }}
        </span>
      </template>
      <template #identificationMethodFlag="{ row }">
        <span>
          {{ row.identificationMethodFlag === true ? '核定' : row.identificationMethodFlag === false ? '查账' : '--' }}
        </span>
      </template>
      <template #bankBaseName="{ row }">
        <span>
          {{ row.bankBaseName?.split(',').length ? row.bankBaseName.split(',')[row.bankBaseName.split(',').length - 1] : '' }}
        </span>
      </template>
      <template #monthlyFee="{ row }">
        <span>{{ row.monthlyFee }} 元</span>
      </template>
      <template #bookkeepingMonthlyFee="{ row }">
        <span>{{ row.bookkeepingMonthlyFee }} 元</span>
      </template>
      <template #addressMonthlyFee="{ row }">
        <span>{{ row.addressMonthlyFee }} 元</span>
      </template>
      <template #arrears="{ row }">
        <span>{{ row.arrears }} 元</span>
      </template>
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <!-- <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button> -->
        <el-button type="danger" :icon="Delete" @click="handleDelete(scope.selectedListIds)">删除</el-button>
        <el-button :icon="Upload" @click="handleImport">导入企业信息</el-button>
        <!-- 增加导入税务信息 -->
        <el-button :icon="Upload" @click="handleImportByTax">导入税务信息</el-button>
        <!-- 增加导入联系人信息 -->
        <el-button :icon="Upload" @click="handleImportByContact">导入联系人</el-button>
        <!-- 增加列表导出功能 -->
        <el-button :icon="Download" @click="handleExport" v-hasPermi="['customer:customer-file:export']">导出</el-button>

        <!-- 高级查询 -->
        <el-button @click="advancedQuery">高级查询</el-button>
      </template>
    </ProTable>
  </div>
  <ImportExcel ref="dialogRef" :coverFlagShow="coverFlagShow" />
  <!-- 高级详情 -->
  <customerDetail
    v-if="detailShow"
    :id="rowId"
    :hideActionBtn="parseInt(initParam.discard) === 99"
    :hideActionBtnForRiskCustomer="parseInt(initParam.discard) === 99"
    @on-close="handleDeleteRefresh"
    @on-edit="handleEdit"
    @on-delete="handleDeleteRefresh"
    @on-list="getList()"
  />

  <!-- 高级新建 -->
  <complexWrap :title="title" v-if="wrapShow" :data="basicData" @on-close="getList()" />

  <!-- 高级查询 -->
  <advancedSearch
    v-if="isAdvance"
    :queryColumAndValue="queryColumAndValueCache"
    @on-close="isAdvance = false"
    @on-search="highSearch"
  />

  <cancleProgress v-if="progressIsShow" :id="customerId" @on-close="progressIsShow = false" />
</template>

<script setup lang="tsx">
import { ref, reactive, computed } from 'vue'
import dataList from './components/data-list.vue'
import ImportExcel from '@/components/ImportExcel/index.vue'
import customerForm from './components/customer-form.vue'
import cancleProgress from './components/cancleProgress'
import { useHandleData } from '@/hooks/useHandleData'
import { useDialog } from '@/hooks/useDialog'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Delete, Upload, Download } from '@element-plus/icons-vue'
import {
  deleteCustomers,
  getCustomers,
  saveCustomer,
  uploadCustomer,
  uploadTax,
  downloadFile,
  getContactImportTemplate,
  uploadContact
} from '@/api/customer/file'
import customerDetail from './components/customer-detail.vue'
import complexWrap from './components/complex-wrap.vue'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { changeFileList } from '@/utils/common'
import advancedSearch from './components/advanced-search'
const useCommon = useCommonStore()

const { getDic } = useDic()

// 高级查询
const isAdvance = ref(false)
const advancedQuery = () => {
  isAdvance.value = true
}

const queryColumAndValueCache = ref({})
const highSearch = queryColumAndValue => {
  // queryColumAndValueCache.value = queryColumAndValue
  const queryTemp = {}
  if (queryColumAndValue.sponsorAccountingAllocationDate) {
    Object.assign(queryTemp, {
      sponsorAccountingAllocationStartDate: queryColumAndValue.sponsorAccountingAllocationDate[0],
      sponsorAccountingAllocationEndDate: queryColumAndValue.sponsorAccountingAllocationDate[1]
    })
    // delete queryColumAndValue.sponsorAccountingAllocationDate
  }
  if (queryColumAndValue.customerSuccessAllocationDate) {
    Object.assign(queryTemp, {
      customerSuccessAllocationStartDate: queryColumAndValue.customerSuccessAllocationDate[0],
      customerSuccessAllocationEndDate: queryColumAndValue.customerSuccessAllocationDate[1]
    })
    // delete queryColumAndValue.customerSuccessAllocationDate
  }
  if (queryColumAndValue.establishDate) {
    Object.assign(queryTemp, {
      establishDateStart: queryColumAndValue.establishDate[0],
      establishDateEnd: queryColumAndValue.establishDate[1]
    })
    // delete queryColumAndValue.establishDate
  }
  Object.assign(queryColumAndValueCache.value, queryColumAndValue)
  // customerSuccessUserId  counselorUserId sponsorAccountingUserId managerUserId
  delete queryColumAndValue.customerSuccessUserId
  delete queryColumAndValue.counselorUserId
  delete queryColumAndValue.sponsorAccountingUserId
  delete queryColumAndValue.managerUserId
  Object.assign(proTable.value.searchParam, { queryColumAndValue }, queryTemp)
  proTable.value.search()
  // setTimeout(() => {
  //   console.log('proTable.value.searchParam', proTable.value.searchParam)
  // }, 2000)
}

// searchReset
const searchReset = () => {
  queryColumAndValueCache.value = {}
}
// 参数修改
const transformRequestParams = data => {
  // if(queryColumAndValueCache.value){
  // }
  // console.log('transformRequestParams', data)
  // if (Array.isArray(data.queryColumAndValueBase) && data.queryColumAndValueBase.length) {
  //   data.queryColumAndValue = {}
  //   data.queryColumAndValueBase.forEach(item => {
  //     data.queryColumAndValue['cc.phone'] = '***********'
  //   })
  //   delete data.queryColumAndValueBase
  // }
  // if (orderStatus === 'descending') {
  //   // 降序 isAsc === 'desc'
  //   data.isAsc = 'desc'
  //   data.orderByColumn = 'info.completeness'
  // } else if (orderStatus === 'ascending') {
  //   // 升序 isAsc === 'asc'
  //   data.isAsc = 'asc'
  //   data.orderByColumn = 'info.completeness'
  // } else {
  //   data.isAsc = ''
  //   data.orderByColumn = ''
  // }
}
const initParam = reactive({ discard: '' })
const tabs = ref([
  {
    dictLabel: '全部',
    dicValue: ''
  },
  {
    dictLabel: '服务中',
    dicValue: 0
  },
  {
    dictLabel: '风险客户',
    dicValue: 99
  },
  {
    dictLabel: '停止服务',
    dicValue: 1
  }
])

// 获取当前路由
const route = useRoute()

// 显示注销办理的进度
const progressIsShow = ref(false)
const customerId = ref('')
const showProgress = (id, customerProperty) => {
  //showProgress
  if (customerProperty === '注销办理') {
    customerId.value = id
    progressIsShow.value = true
  }
}

const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '企业名称',
    width: '300',
    fixed: 'left',
    isColShow: false,
    search: { el: 'input' },
    sortable: 'custom',
    sortName: 'info.customer_name'
  },
  {
    prop: 'customerNo',
    label: '企业编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'info.customer_no'
  },
  {
    prop: 'contactPerson',
    label: '联系人',
    width: '150'
  },
  {
    prop: 'contactPhone',
    label: '手机号',
    width: '200',
    sortable: 'custom',
    sortName: 'cc.phone'
  },
  {
    prop: 'managerUserName',
    width: '280',
    label: '财税顾问',
    sortable: 'custom',
    sortName: 'manager_user.nick_name'
  },
  {
    prop: 'customerSuccessUserName',
    width: 150,
    label: '客户成功',
    sortable: 'custom',
    sortName: 'customer_success_user.nick_name'
  },
  {
    prop: 'sponsorAccountingUserName',
    width: 150,
    label: '主办会计',
    sortable: 'custom',
    sortName: 'sponsor_accounting_user.nick_name'
  },
  {
    prop: 'counselorUserName',
    width: 150,
    label: '开票员',
    sortable: 'custom',
    sortName: 'counselor_user.nick_name'
  },
  {
    prop: 'branchOffice',
    label: '所属分公司',
    width: '280',
    render: scope => {
      return <span>{scope.row.branchOffice || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'info.branch_office'
  },
  {
    prop: 'address',
    label: '实际经营地址',
    width: '400',
    sortable: 'custom',
    sortName: 'info.address'
  },
  {
    prop: 'informationMark',
    label: '备注',
    width: '300',
    sortable: 'custom',
    sortName: 'info.information_mark'
  },
  {
    prop: 'customerStatus',
    label: '企业状态',
    // enum: remoteEnumList,
    enum: getDic('customer_status', [
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    width: '150',
    // render: scope => {
    //   return <span>{scope.row.customerStatus || '--'}</span>
    // },
    render: scope => {
      return (
        <span
          class="blue-text"
          onClick={() => {
            showProgress(scope.row.customerId, scope.row.customerStatus)
          }}
        >
          {scope.row.customerStatus || '--'}
        </span>
      )
    },
    search: { el: 'select' },
    sortable: 'custom',
    sortName: 'info.customer_status'
  },
  {
    prop: 'customerProperty',
    label: '企业性质',
    // enum: customerProperty.concat([
    //   {
    //     label: '废弃客户',
    //     value: '废弃客户'
    //   }
    // ]),
    enum: getDic('customer_property', [
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'info.customer_property',
    search: { el: 'select' }
  },
  {
    prop: 'nofeeReason',
    label: '不收费原因',
    width: '200',
    sortable: 'custom',
    sortName: 'info.nofee_reason'
  },
  {
    prop: 'nofeeReasonMark',
    label: '不收费原因备注',
    width: '300',
    sortable: 'custom',
    sortName: 'info.nofee_reason_mark'
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'info.industry'
  },
  {
    prop: 'companyIdentification',
    label: '企业认定',
    enum: getDic('company_identification'),
    width: '200',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.companyIdentification || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'info.company_identification'
  },

  //银行
  {
    prop: 'bankBaseName',
    width: '200',
    label: '基本户开户银行',
    sortable: 'custom',
    sortName: 'bank.bank_base_name'
  },
  {
    prop: 'bankBaseAccount',
    width: '200',
    label: '基本户账号',
    sortable: 'custom',
    sortName: 'bank.bank_base_account'
  },
  {
    prop: 'receiptCardFlag',
    width: '200',
    label: '回单卡',
    sortable: 'custom',
    sortName: 'bank.receipt_card_flag'
  },
  {
    prop: 'receiptCardType',
    width: '200',
    label: '回单卡类型',
    sortable: 'custom',
    sortName: 'bank.receipt_card_type'
  },
  {
    prop: 'receiptCardAccount',
    width: '200',
    label: '回单卡账号',
    sortable: 'custom',
    sortName: 'bank.receipt_card_account'
  },
  {
    prop: 'receiptCardPassword',
    width: '200',
    label: '回单卡密码',
    sortable: 'custom',
    sortName: 'bank.receipt_card_password'
  },
  {
    prop: 'commonListSize',
    width: '200',
    label: '一般户账户数量(个)'
  },

  // 工商
  {
    prop: 'crediCode',
    width: '200',
    label: '统一社会信用代码',
    sortable: 'custom',
    sortName: 'biz_info.credi_code'
  },
  {
    prop: 'type',
    width: '150',
    label: '公司类型',
    sortable: 'custom',
    sortName: 'biz_info.type'
  },

  {
    prop: 'legalPerson',
    width: '150',
    label: '法定代表人',
    sortable: 'custom',
    sortName: 'biz_info.legal_person'
  },
  {
    prop: 'registeredAddress',
    width: '400',
    label: '注册地址',
    sortable: 'custom',
    sortName: 'biz_info.registered_address'
  },

  {
    prop: 'contract',
    width: '150',
    label: '联系方式',
    sortable: 'custom',
    sortName: 'biz_info.contract'
  },

  {
    prop: 'scope',
    width: '400',
    label: '经营范围',
    sortable: 'custom',
    sortName: 'biz_info.scope'
  },
  {
    prop: 'website',
    width: '150',
    label: '网址',
    sortable: 'custom',
    sortName: 'biz_info.website'
  },
  {
    prop: 'bussinessStatus',
    width: '150',
    label: '经营状态',
    sortable: 'custom',
    sortName: 'biz_info.bussiness_status'
  },
  {
    prop: 'registrationAuthority',
    width: '200',
    label: '登记机关',
    sortable: 'custom',
    sortName: 'biz_info.registration_authority'
  },
  {
    prop: 'establishDate',
    width: '150',
    label: '成立日期',
    sortable: 'custom',
    sortName: 'biz_info.establish_date'
  },
  {
    prop: 'registeredCapital',
    width: '150',
    label: '注册资金',
    sortable: 'custom',
    sortName: 'biz_info.registered_capital'
  },
  {
    prop: 'businessIndustry',
    width: '150',
    label: '行业',
    sortable: 'custom',
    sortName: 'biz_info.industry'
  },
  {
    prop: 'registrationNumber',
    width: '150',
    label: '注册号',
    sortable: 'custom',
    sortName: 'biz_info.registration_number'
  },

  {
    prop: 'openDate',
    width: '150',
    label: '营业开始时间',
    sortable: 'custom',
    sortName: 'biz_info.open_date'
  },

  {
    prop: 'openEnd',
    width: '150',
    label: '营业结束时间',
    sortable: 'custom',
    sortName: 'biz_info.open_end'
  },

  {
    prop: 'organizationCode',
    width: '150',
    label: '组织机关代码',
    sortable: 'custom',
    sortName: 'biz_info.organization_code'
  },
  {
    prop: 'approvalDate',
    width: '150',
    label: '核准日期',
    sortable: 'custom',
    sortName: 'biz_info.approval_date'
  },
  // 许可证
  {
    prop: 'licenseSize',
    width: '150',
    label: '许可证数量(个)'
  },
  {
    prop: 'socialAccountFlag',
    width: '200',
    label: '社保开户',
    sortable: 'custom',
    sortName: 'social_fund.social_account_flag'
  },
  {
    prop: 'socialAccount',
    width: '200',
    label: '社保账号',
    sortable: 'custom',
    sortName: 'social_fund.social_account'
  },
  {
    prop: 'socialPassword',
    width: '200',
    label: '社保密码',
    sortable: 'custom',
    sortName: 'social_fund.social_password'
  },
  {
    prop: 'fundAccountFlag',
    width: '200',
    label: '公积金开户',
    sortable: 'custom',
    sortName: 'social_fund.fund_account_flag'
  },
  {
    prop: 'fundAccount',
    width: '200',
    label: '公积金账户',
    sortable: 'custom',
    sortName: 'social_fund.fund_account'
  },
  {
    prop: 'fundPassword',
    width: '200',
    label: '公积金密码',
    sortable: 'custom',
    sortName: 'social_fund.fund_password'
  },
  // 税务信息
  {
    prop: 'taxRegistrationOrgan',
    width: '200',
    label: '登记税务机关',
    sortable: 'custom',
    sortName: 'tax.tax_registration_organ'
  },
  {
    prop: 'taxOrganAddress',
    width: '200',
    label: '税务机关地址',
    sortable: 'custom',
    sortName: 'tax.tax_organ_address'
  },

  {
    prop: 'rateRegistration',
    width: '100',
    label: '税率登记',
    sortable: 'custom',
    sortName: 'tax.rate_registration'
  },

  {
    prop: 'taxRealNameFlag',
    width: '200',
    label: '办税员实名认证',
    sortable: 'custom',
    sortName: 'tax.tax_real_name_flag'
  },

  {
    prop: 'individualCheckFlag',
    width: '200',
    label: '个体户核定',
    sortable: 'custom',
    sortName: 'tax.individual_check_flag'
  },

  {
    prop: 'reservedPhoneNumber',
    width: '200',
    label: '预留手机号',
    sortable: 'custom',
    sortName: 'tax.reserved_phone_number'
  },
  {
    prop: 'naturalPersonPassword',
    width: '200',
    label: '自然人密码',
    sortable: 'custom',
    sortName: 'tax.natural_person_password'
  },
  {
    prop: 'identificationMethodFlag',
    width: '200',
    label: '所得税认定方式',
    sortable: 'custom',
    sortName: 'tax.identification_method_flag'
  },
  {
    prop: 'certificateAccount',
    width: '200',
    label: '证书账号',
    sortable: 'custom',
    sortName: 'tax.certificate_account'
  },

  {
    prop: 'certificatePassword',
    width: '200',
    label: '证书密码',
    sortable: 'custom',
    sortName: 'tax.certificate_password'
  },
  {
    prop: 'drawingSheetFlag',
    width: '200',
    label: '发票盘'
  },

  {
    prop: 'invoiceFlag',
    width: '200',
    label: '发票',
    sortable: 'custom',
    sortName: 'tax.invoice_flag'
  },

  {
    prop: 'invoiceSealFlag',
    width: '200',
    label: '发票章',
    sortable: 'custom',
    sortName: 'tax.invoice_seal_flag'
  },
  {
    prop: 'drawingSheetDept',
    width: '200',
    label: '开票盘归属部门',
    sortable: 'custom',
    sortName: 'tax.drawing_sheet_dept'
  },

  {
    prop: 'invoiceDept',
    width: '200',
    label: '发票归属部门',
    sortable: 'custom',
    sortName: 'tax.invoice_dept'
  },
  {
    prop: 'invoiceSealDept',
    width: '200',
    label: '发票章归属部门',
    sortable: 'custom',
    sortName: 'tax.invoice_seal_dept'
  },
  {
    prop: 'invoiceType',
    width: '200',
    label: '发票类型',
    sortable: 'custom',
    sortName: 'tax.invoice_type'
  },
  {
    prop: 'drawingSheetType',
    width: '200',
    label: '开票盘类型',
    sortable: 'custom',
    sortName: 'tax.drawing_sheet_type'
  },

  {
    prop: 'watchTime',
    width: '200',
    label: '看点时间',
    sortable: 'custom',
    sortName: 'tax_rebate_identified.watch_time'
  },
  {
    prop: 'approvalTime',
    width: '200',
    label: '进出口认定时间',
    sortable: 'custom',
    sortName: 'tax_rebate_identified.approval_time'
  },
  {
    prop: 'watchInstructions',
    width: '200',
    label: '看点说明',
    sortable: 'custom',
    sortName: 'tax_rebate_identified.watch_instructions'
  },
  {
    prop: 'dept',
    width: '200',
    label: '所属部门',
    sortable: 'custom',
    sortName: 'tax_rebate_identified.dept'
  },

  // {
  //   prop: 'naturalPersonPassword',
  //   width: '200',
  //   label: '自然人密码'
  // },
  // 个性化信息
  {
    prop: 'personality',
    width: '150',
    label: '客户性格'
  },
  {
    prop: 'typeStr',
    width: '200',
    label: '客户类型'
  },
  {
    prop: 'tagStr',
    width: '200',
    label: '客户标签'
  },

  {
    prop: 'ageLevel',
    width: '200',
    label: '客户年龄层次'
  },

  {
    prop: 'personalityComplement',
    width: '200',
    label: '客户性格补充'
  },
  {
    prop: 'collectionRequirement',
    width: '200',
    label: '资料收取要求'
  },
  {
    prop: 'dealRequirement',
    width: '200',
    label: '财务处理要求'
  },
  {
    prop: 'billingDemand',
    width: '200',
    label: '开票特殊需求'
  },
  {
    prop: 'monthlyFee',
    width: '150',
    label: '月营业额'
  },
  {
    prop: 'bookkeepingMonthlyFee',
    width: '150',
    label: '记账营业额'
  },
  {
    prop: 'addressMonthlyFee',
    width: '150',
    label: '地址营业额'
  },
  {
    prop: 'arrears',
    width: '150',
    label: '总欠费合计'
  },
  {
    prop: 'dueDate',
    width: '200',
    label: '最近合同到期日期'
  },
  {
    prop: 'latestFollowUpDate',
    width: '200',
    label: '最后跟进时间'
  },

  // {
  //   prop: 'type',
  //   width: '150',
  //   label: '企业类型',
  //   render: scope => {
  //     return <span>{scope.row.types ? scope.row.types.join(',') : '--'}</span>
  //   }
  // },
  // {
  //   prop: 'tag',
  //   width: '150',
  //   label: '企业标签'
  //   render: scope => {
  //     return <span>{scope.row.tags ? scope.row.tags.join(',') : '--'}</span>
  //   }
  // },

  {
    prop: 'createTime',
    label: '创建时间',
    width: 180
  },
  {
    prop: 'completeness',
    label: '资料完善度',
    fixed: 'right',
    sortable: 'custom',
    width: 180,
    render: scope => {
      return <span>{scope.row.completeness ? scope.row.completeness + '%' : '--'}</span>
    },
    sortName: 'completeness'
  }
  // {
  //   prop: 'queryColumAndValueBase',
  //   label: '列字段',
  //   isShow: false,
  //   enum: [
  //     {
  //       label: '手机号',
  //       value: 'cc.phone'
  //     },
  //     {
  //       label: '统一社会信用代码 ',
  //       value: 'biz_info.credi_code'
  //     },
  //     {
  //       label: '法定代表人',
  //       value: 'biz_info.legal_person'
  //     },
  //     {
  //       label: '注册地址',
  //       value: 'biz_info.registered_address'
  //     },
  //     {
  //       label: '联系方式',
  //       value: ' biz_info.contract'
  //     },
  //     {
  //       label: '财税顾问',
  //       value: 'manager_user.nick_name'
  //     },
  //     {
  //       label: '主办会计姓名',
  //       value: 'sponsor_accounting_user.nick_name'
  //     },
  //     {
  //       label: '开票员',
  //       value: 'counselor_user.nick_name'
  //     },
  //     {
  //       label: '客户成功',
  //       value: 'customer_success_user.nick_name'
  //     }
  //   ],
  //   search: {
  //     el: 'select',
  //     props: {
  //       multiple: true
  //     }
  //   }
  // }
]
const wrapShow = ref(false)
// 传递给高级新建弹窗组件的数据
const basicData = ref()
const submitCallback = (data: any) => {
  basicData.value = data
  title.value = '更多信息'
  wrapShow.value = true
}
const { showDialog } = useDialog()
// 处理表单提交参数
const handleRevertParams = (data: any) => {
  if (Array.isArray(data.businessFileList) && data.businessFileList.length) {
    data.businessFileList = changeFileList(data.businessFileList)
  } else {
    data.businessFileList = []
  }
}
// 新增客户
const handleAdd = () => {
  showDialog({
    title: '新增企业',
    component: customerForm,
    customClass: 'customer-dialog',
    submitApi: saveCustomer,
    submitCallback,
    handleRevertParams
  })
}

// 批量删除客户
const handleDelete = async (id: string[]) => {
  if (!id.length) return
  await useHandleData(deleteCustomers, id, '删除所选企业信息')
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
  staticsRef.value?.getList()
}

// 导入
const proTable = ref()
const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null)
const coverFlagShow = ref(false)
const handleImport = () => {
  const params = {
    title: '企业',
    tempApiUrl: '/客户导入下载模板.xls',
    // tempApi: exportUserInfo, // 下载模板接口
    importApi: uploadCustomer, // 导入家口
    getTableList: proTable.value?.getTableList
  }
  coverFlagShow.value = true
  dialogRef.value?.acceptParams(params)
}
// 导入税务信息
const handleImportByTax = () => {
  const params = {
    title: '税务',
    tempApiUrl: '/税务导入下载模板.xls',
    // tempApi: exportUserInfo, // 下载模板接口
    importApi: uploadTax, // 导入接口
    getTableList: proTable.value?.getTableList
  }
  coverFlagShow.value = false
  dialogRef.value?.acceptParams(params)
}
// 导入联系人
const handleImportByContact = () => {
  const params = {
    title: '联系人',
    // tempApiUrl: '/联系人导入下载模板.xls',
    tempApi: getContactImportTemplate, // 下载模板接口
    importApi: uploadContact, // 导入接口
    getTableList: proTable.value?.getTableList
  }
  coverFlagShow.value = false
  dialogRef.value?.acceptParams(params)
}

// 导出列表功能
const { proxy } = getCurrentInstance()
const handleExport = async () => {
  const result = await downloadFile({
    ...proTable.value.searchParam,
    // tab的参数
    discard: initParam.discard,
    noShowList: columns.filter(column => column.prop && !column.isShow).map(column => column.prop)
  })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
  // proxy.download(
  //   '/customerInformation/export', // 导出路径
  //   {
  //     ...proTable.value.searchParam,
  //     noShowList: columns.filter(column => column.prop && !column.isShow).map(column => column.prop)
  //   }, // 查询条件
  //   '企业档案导出.xlsx'
  // )
}
// 展示客户详情
const detailShow = ref(false)
const rowId = ref()
// if (route.query.code) {
//   // 跳出详情
//   detailShow.value = true
//   rowId.value = route.query.id
// }
watch(
  () => useCommon.id,
  () => {
    console.log('watching', useCommon.id)
    if (useCommon.id && (useCommon.bizType === 'customer' || useCommon.bizType === 'accountant_notify')) {
      detailShow.value = true
      rowId.value = useCommon.id
      //立马clear useCommon id
      useCommon.clearId()
      useCommon.clearBizType()
    }
  },
  {
    immediate: true
  }
)

const showDetail = (id: number) => {
  rowId.value = id
  detailShow.value = true
  isEdit.value = false
}
// 高级新建关闭后需要调用查询接口
const getList = () => {
  wrapShow.value = false
  // detailShow.value = false
  proTable.value?.getTableList()
  staticsRef.value?.getList()
}

const staticsRef = ref()
const handleDeleteRefresh = () => {
  detailShow.value = false
  proTable.value?.getTableList()
  staticsRef.value?.getList()
}

// 用于编辑状态下
const isEdit = ref(false)
const title = ref('更多信息')
provide('isEdit', isEdit)
// handleEdit

const handleEdit = (data: any) => {
  detailShow.value = false
  title.value = '编辑'
  basicData.value = {
    ...data,
    ciId: data.customerId
  }
  isEdit.value = true
  wrapShow.value = true
}
// dataList组件点击事件接受
const handleSearch = ({ searchType, searchValue }) => {
  if (searchType === 'customerStatus') {
    proTable.value.searchParam['customerProperty'] = ''
    proTable.value.searchParam['companyIdentification'] = ''
  } else if (searchType === 'companyIdentification') {
    proTable.value.searchParam['customerStatus'] = ''
    proTable.value.searchParam['customerProperty'] = ''
  } else {
    //customerProperty
    proTable.value.searchParam['companyIdentification'] = ''
    proTable.value.searchParam['customerStatus'] = ''
  }
  proTable.value.searchParam[searchType] = searchValue
  proTable.value.search()
}

const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  initParam.discard = value
}

// 列排序事件
let orderStatus = ''
const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  orderStatus = order
  // proTable.value.search()

  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
