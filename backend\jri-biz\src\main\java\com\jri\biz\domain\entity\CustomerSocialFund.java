package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_social_fund")
@ApiModel(value = "CustomerSocialFund对象", description = "")
public class CustomerSocialFund implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 社保公积金表id
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 客户信息主表id
     */
    private Long ciId;

    /**
     * 社保是否开户(0未开 1开户)
     */
    private String socialAccountFlag;

    /**
     * 社保账号
     */
    private String socialAccount;

    /**
     * 社保密码
     */
    private String socialPassword;

    /**
     * 公积金是否开户(0未开 1开户)
     */
    private String fundAccountFlag;

    /**
     * 公积金账户
     */
    private String fundAccount;

    /**
     * 公积金密码
     */
    private String fundPassword;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;


}
