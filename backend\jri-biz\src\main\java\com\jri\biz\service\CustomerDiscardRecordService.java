package com.jri.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.domain.convert.CustomerDiscardRecordConvert;
import com.jri.biz.domain.convert.CustomerInformationConvert;
import com.jri.biz.domain.entity.CustomerDiscardRecord;
import com.jri.biz.domain.request.CustomerDiscardRecordForm;
import com.jri.biz.domain.request.CustomerDiscardRecordQuery;
import com.jri.biz.domain.vo.CustomerDiscardRecordListVO;
import com.jri.biz.domain.vo.CustomerDiscardRecordVO;
import com.jri.biz.mapper.CustomerDiscardRecordMapper;
import com.jri.biz.mapper.CustomerInformationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Service
public class CustomerDiscardRecordService extends ServiceImpl<CustomerDiscardRecordMapper, CustomerDiscardRecord> {

    private final CustomerInformationMapper customerInformationMapper;

    public CustomerDiscardRecordService(CustomerInformationMapper customerInformationMapper) {
        this.customerInformationMapper = customerInformationMapper;
    }

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerDiscardRecordListVO> listPage(CustomerDiscardRecordQuery query) {
        var page = new Page<CustomerDiscardRecordListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerDiscardRecordVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long add(CustomerDiscardRecordForm form) {
        CustomerDiscardRecord customerDiscardRecord = CustomerDiscardRecordConvert.INSTANCE.convert(form);
        if (save(customerDiscardRecord)) {
            var civ = customerInformationMapper.getDetailById(customerDiscardRecord.getCiId());
            civ.setDiscard(customerDiscardRecord.getDiscard());
            civ.setDiscardReason(customerDiscardRecord.getDiscardReason());
            if (form.getDiscard() == 1) {
                civ.setCustomerStatus("废弃客户");
                civ.setCustomerProperty("废弃客户");
            } else {
                civ.setCustomerStatus(form.getCustomerStatus());
                civ.setCustomerProperty(form.getCustomerProperty());
            }
            var entity = CustomerInformationConvert.INSTANCE.convert2(civ);
            customerInformationMapper.updateById(entity);
            return customerDiscardRecord.getId();
        } else {
            return -1L;
        }
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerDiscardRecordForm form) {
        // todo 完善新增/更新逻辑
        CustomerDiscardRecord customerDiscardRecord = CustomerDiscardRecordConvert.INSTANCE.convert(form);
        return updateById(customerDiscardRecord);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // todo 完善级联删除逻辑
        return removeById(id);
    }

    public CustomerDiscardRecordVO getDetailByCiId(Long ciId) {
        var list = getBaseMapper().getDetailByCiId(ciId);
        if (!ObjectUtils.isEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    public List<CustomerDiscardRecordVO> getListByCiId(Long ciId) {
        return getBaseMapper().getDetailByCiId(ciId);
    }


}
