package com.jri.biz.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jri.biz.domain.entity.BizFlowNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 流程视图对象
 *
 * <AUTHOR>
 * @since 2023-06-16
 */

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value="BizFlowVO视图对象")
public class BizFlowVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 合同类型0-记账合同1-一次性合同2-地址服务协议合同
     */
    private String contractType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 类型0-模板审批流程1-合同借阅2-合同审批
     */
    private String type;

    /**
     * 使用状态0-关闭1-启用
     */
    private String enable;

    private String xmlStr;

    private String deptIds;

    private String deptNames;

    @ApiModelProperty("节点列表")
    List<BizFlowNode> nodeList;

}