package com.jri.biz.domain.request;

import com.jri.common.core.domain.CommonBizFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-05-29
 */

@Data
@NoArgsConstructor
@ApiModel(value = "社保表单请求对象")
public class CustomerSocialFundForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 社保公积金表id
     */
    private Long id;

    /**
     * 客户信息主表id
     */
    private Long ciId;

    /**
     * 社保是否开户(0未开 1开户)
     */
    private String socialAccountFlag;

    /**
     * 社保账号
     */
    private String socialAccount;

    /**
     * 社保密码
     */
    private String socialPassword;

    /**
     * 公积金是否开户(0未开 1开户)
     */
    private String fundAccountFlag;

    /**
     * 公积金账户
     */
    private String fundAccount;

    /**
     * 公积金密码
     */
    private String fundPassword;
    private Boolean isDeleted;

    @ApiModelProperty("社保开户附件")
    private List<CommonBizFile> socialAccountOpenFileList;
    @ApiModelProperty("公积金开户附件")
    private List<CommonBizFile> fundAccountOpenFileList;
}
