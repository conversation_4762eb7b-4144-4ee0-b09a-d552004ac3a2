package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.OrderRecord;
import com.jri.biz.domain.request.OrderRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 工单记录对象转换
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Mapper
public interface OrderRecordConvert {
    OrderRecordConvert INSTANCE = Mappers.getMapper(OrderRecordConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    OrderRecord convert(OrderRecordForm form);

}