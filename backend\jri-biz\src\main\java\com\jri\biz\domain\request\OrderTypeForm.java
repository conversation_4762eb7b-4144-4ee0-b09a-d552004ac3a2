package com.jri.biz.domain.request;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 工单类型 表单请求对象
 *
 * <AUTHOR>
 * @since 2023-07-17
 */

@Data
@NoArgsConstructor
@ApiModel(value="工单类型表单请求对象")
public class OrderTypeForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String typeName;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 状态0-停用 1-正常
     */
    @ApiModelProperty("状态0-停用 1-正常")
    private String status;

    /**
     * 补充说明
     */
    @ApiModelProperty("补充说明")
    private String supplementExplain;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 父id
     */
    @ApiModelProperty("父id")
    private Long parentId;

    /**
     * 祖级列表
     */
    @ApiModelProperty("祖级列表")
    private String ancestors;
}
