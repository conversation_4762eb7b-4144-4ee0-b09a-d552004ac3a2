<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jri.biz.mapper.CustomerTaxRebateIdentifiedMapper">


    <select id="listPage" resultType="com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedListVO">

    </select>

    <select id="getDetailById" resultType="com.jri.biz.domain.vo.CustomerTaxRebateIdentifiedVO">

    </select>
    <select id="selectByMainId" resultType="com.jri.biz.domain.entity.CustomerTaxRebateIdentified">
        select * from customer_tax_rebate_identified where  is_deleted=0 and main_id=#{id} limit 1
    </select>
</mapper>
