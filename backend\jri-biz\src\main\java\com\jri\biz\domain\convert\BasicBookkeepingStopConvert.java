package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.BasicBookkeepingStop;
import com.jri.biz.domain.request.BasicBookkeepingStopForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 停止记账原因对象转换
 *
 * <AUTHOR>
 * @since 2023-08-01
 */

@Mapper
public interface BasicBookkeepingStopConvert {
    BasicBookkeepingStopConvert INSTANCE = Mappers.getMapper(BasicBookkeepingStopConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    BasicBookkeepingStop convert(BasicBookkeepingStopForm form);

}