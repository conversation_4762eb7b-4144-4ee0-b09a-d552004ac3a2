package com.jri.biz.cus.mapper;

import com.jri.biz.cus.domain.entity.CusCcShare;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jri.biz.cus.domain.vo.CusCcShareListVO;
import com.jri.biz.cus.domain.vo.CusCcShareVO;
import com.jri.biz.cus.domain.request.CusCcShareQuery;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 线索/客户共享 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface CusCcShareMapper extends BaseMapper<CusCcShare> {

     /**
     * 列表查询
     *
     * @param query 查询参数
     * @param page 分页参数
     * @return 查询结果
     */
    IPage<CusCcShareListVO> listPage(@Param("query") CusCcShareQuery query, Page<CusCcShareListVO> page);

    /**
     * 根据id查询明细
     *
     * @param id id
     * @return 结果
     */
    CusCcShareVO getDetailById(@Param("id") Long id);
}
