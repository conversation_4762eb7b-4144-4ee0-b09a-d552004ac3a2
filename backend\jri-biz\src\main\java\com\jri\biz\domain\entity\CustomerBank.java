package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_bank")
@ApiModel(value = "CustomerBank对象", description = "")
public class CustomerBank implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 银行信息id
     */
    @TableId(value = "bank_id",type= IdType.ASSIGN_ID)
    private Long bankId;

    /**
     * 客户信息主表id
     */
    private Long ciId;

    /**
     * 基本户开户银行
     */
    private String bankBaseName;

    /**
     * 基本户账号
     */
    private String bankBaseAccount;

    /**
     * 是否有网银(0-没有 1-有)
     */
    private String internetbankFlag;

    /**
     * 是否有结算卡(0-没有 1-有)
     */
    private String debitCardFlag;

    /**
     * 是否有回单卡(0-没有 1-有)
     */
    private String receiptCardFlag;

    /**
     * 回单卡账户
     */
    private String receiptCardAccount;

    /**
     * 回单卡密码
     */
    private String receiptCardPassword;

    /**
     * 回单卡类型
     */
    private String receiptCardType;

    /**
     * 一般户开户银行
     */
    private String commonBankName;

    /**
     * 一般户账号
     */
    private String commonBankAccount;

    /**
     * 是否有一般户网银(0-没有 1-有)
     */
    private String commonInternetbankFlag;

    /**
     * 是否有一般户回单卡(0-没有 1-有)
     */
    private String commonReceiptCardFlag;

    /**
     * 一般户回单卡账号
     */
    private String commonInternetbankAccount;

    /**
     * 一般户回单卡密码
     */
    private String commonReceiptCardPassword;

    /**
     * 一般户回单卡类型
     */
    private String commonInternetbankType;

    /**
     * 对账周期
     */
    private String cycle;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @TableLogic
    private Boolean isDeleted;


}
