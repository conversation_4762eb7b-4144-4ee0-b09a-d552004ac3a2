package com.jri.biz.utils;

import com.jri.biz.domain.entity.CustomerInformation;
import com.jri.biz.mapper.CustomerInformationMapper;
import com.jri.common.utils.reflect.ReflectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * 客户信息相关工具类
 * <AUTHOR>
 * @since 2024/4/18 14:14
 *
 */
@Component
public class CustomerInformationUtil {

    @Resource
    private CustomerInformationMapper customerInformationMapper;

    public static final String[] relatedUserFiled = {"mangerUserId", "sponsorAccountingUserId", "counselorUserId", "customerSuccessUserId"};

    public void setRelatedUser(Long customerId, Object obj) {
        CustomerInformation customerInformation = customerInformationMapper.selectById(customerId);
        setRelatedUser(customerInformation, obj);
    }

    /**
     * 填充与客户信息关联的人员信息
     */
    public void setRelatedUser(CustomerInformation customerInformation, Object obj) {
        for (String propertyName : relatedUserFiled) {
            ReflectUtils.invokeSetter(obj, propertyName, ReflectUtils.getFieldValue(customerInformation, propertyName));
        }
    }

}
