package com.jri.web.controller.biz;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jri.biz.domain.request.CustomerDiscardRecordForm;
import com.jri.biz.domain.request.CustomerDiscardRecordQuery;
import com.jri.biz.domain.vo.CustomerDiscardRecordListVO;
import com.jri.biz.domain.vo.CustomerDiscardRecordVO;
import com.jri.biz.service.CustomerDiscardRecordService;
import com.jri.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Validated
@RestController
@RequestMapping("/customerDiscardRecord")
@Api(tags = "废弃记录")
public class CustomerDiscardRecordController {
    @Resource
    private CustomerDiscardRecordService customerDiscardRecordService;

    @GetMapping("/list")
    @ApiOperation("列表查询")
    public R<IPage<CustomerDiscardRecordListVO>> listPage(CustomerDiscardRecordQuery query) {
        return R.ok(customerDiscardRecordService.listPage(query));
    }


    @GetMapping("/getById")
    @ApiOperation("详情")
    public R<CustomerDiscardRecordVO> getDetailById(@RequestParam("id") Long id) {
        return R.ok(customerDiscardRecordService.getDetailById(id));
    }

    @GetMapping("/getByCiId")
    @ApiOperation("详情")
    public R<CustomerDiscardRecordVO> getDetailByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerDiscardRecordService.getDetailByCiId(ciId));
    }

    @GetMapping("/getListByCiId")
    @ApiOperation("详情")
    public R<List<CustomerDiscardRecordVO>> getListByCiId(@RequestParam("ciId") Long ciId) {
        return R.ok(customerDiscardRecordService.getListByCiId(ciId));
    }

    @PostMapping("/save")
    @ApiOperation("保存数据")
    public R<Long> save(@RequestBody @Valid CustomerDiscardRecordForm form) {
        return R.ok(customerDiscardRecordService.add(form));
    }

    @PostMapping("/update")
    @ApiOperation("更新数据")
    public R<Boolean> update(@RequestBody @Valid CustomerDiscardRecordForm form) {
        return R.ok(customerDiscardRecordService.update(form));
    }

    @DeleteMapping("/delete")
    @ApiOperation("根据id删除")
    public R<Boolean> deleteById(Long id) {
        return R.ok(customerDiscardRecordService.deleteById(id));
    }
}

