package com.jri.biz.cus.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "所有线索客户信息查询对象")
public class AllCusCustomerOrClueQuery extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("线索来源(客户来源)")
    private String source;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @ApiModelProperty("跟进状态0-未跟进1-跟进中")
    private String followStatus;

    @ApiModelProperty("创建者")
    private String createBy;

    private Long userId;

    private Long id;

    private String keyword;

    @ApiModelProperty("前跟进人")
    private String currentUserName;

    @ApiModelProperty("姓名")
    private String contactName;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近跟进时间开始")
    private LocalDateTime lastFollowTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最近跟进时间结束")
    private LocalDateTime lastFollowTimeEnd;

    private List<Long> idList;

    @ApiModelProperty("排序列")
    private String orderByColumn;

    @ApiModelProperty("排序的方向desc或者asc")
    private String isAsc;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("产品id")
    private String productId;

    @ApiModelProperty("税务性质")
    private String taxNature;

}