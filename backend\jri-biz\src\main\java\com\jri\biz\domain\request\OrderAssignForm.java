package com.jri.biz.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value="指派工单请求对象")
public class OrderAssignForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("指派给")
    @NotNull(message = "指派人不能为空")
    private Long executor;

    @ApiModelProperty("工单id")
    @NotNull(message = "工单id不能为空")
    private Long orderId;

    @ApiModelProperty("备注")
    private String remark;
}
