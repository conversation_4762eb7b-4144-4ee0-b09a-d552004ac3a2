package com.jri.biz.domain.convert;

import com.jri.biz.domain.entity.CustomerDiscardRecord;
import com.jri.biz.domain.request.CustomerDiscardRecordForm;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 对象转换
 *
 * <AUTHOR>
 * @since 2023-06-01
 */

@Mapper
public interface CustomerDiscardRecordConvert {
    CustomerDiscardRecordConvert INSTANCE = Mappers.getMapper(CustomerDiscardRecordConvert.class);

    /**
     * 表单->实体转换接口
     *
     * @param form 表单
     * @return 实体
     */
    CustomerDiscardRecord convert(CustomerDiscardRecordForm form);

}